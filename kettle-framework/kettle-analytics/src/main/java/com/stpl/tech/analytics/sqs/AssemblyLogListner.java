package com.stpl.tech.analytics.sqs;

import com.amazon.sqs.javamessaging.message.SQSObjectMessage;
import com.amazon.sqs.javamessaging.message.SQSTextMessage;
import com.amazonaws.regions.Regions;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonElement;
import com.google.gson.reflect.TypeToken;
import com.stpl.tech.analytics.adapter.CustomDateAdapter;
import com.stpl.tech.analytics.adapter.FlexibleDateDeserializer;
import com.stpl.tech.analytics.model.WeightCalibrationDetail;
import com.stpl.tech.analytics.service.LoggingService;
import com.stpl.tech.kettle.data.model.MonkTaskCompletionStats;
import com.stpl.tech.kettle.data.model.MonkXTwoFailureLogs;
import com.stpl.tech.kettle.domain.model.AssemblyErrorMessageThroughSqs;
import com.stpl.tech.kettle.domain.model.ChaiMonk2Errors;
import com.stpl.tech.kettle.domain.model.MonkTaskCompletionStatsDto;
import com.stpl.tech.kettle.domain.model.MonkXTwoFailureLogsDTO;
import com.stpl.tech.loggingservice.model.chaimonk.log.GenericLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkBrokerStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkConsumptionLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkDiagnosisLogData;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusDetailLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkTaskStatusLog;
import com.stpl.tech.loggingservice.model.chaimonk.log.MonkXTwoLog;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.external.inventory.service.SQSNotificationService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;

import javax.jms.JMSException;
import javax.jms.Message;
import javax.jms.MessageListener;
import java.util.Date;

public class AssemblyLogListner implements MessageListener {

    private SQSNotificationService sqsNotificationService;

    private Environment env;

    private static final String MONK_CONSUMPTION_LOG = MonkConsumptionLog.class.getSimpleName();
    private static final String MONK_TASK_STATUS_LOG = MonkTaskStatusLog.class.getSimpleName();
    private static final String MONK_TASK_STATUS_DETAIL_LOG = MonkTaskStatusDetailLog.class.getSimpleName();
    private static final String MONK_STATUS_LOG = MonkStatusLog.class.getSimpleName();
    private static final String MONK_TASK_COMPLETION_STATS_LOG = MonkTaskCompletionStatsDto.class.getSimpleName();
    private static final String ASSEMBLY_ERROR_MESSAGE_THROUGH_SQS = AssemblyErrorMessageThroughSqs.class.getSimpleName();

    private static final String MONK_SDIAGNOSIS_LOG = MonkDiagnosisLogData.class.getSimpleName();
    private static final String MONK_BROKER_STATUS_LOG = MonkBrokerStatusLog.class.getSimpleName();
    private static final String WEIGHT_CALIBRATION_DETAIL = WeightCalibrationDetail.class.getSimpleName();
    private static final String GENERIC_LOG_DATA = GenericLogData.class.getSimpleName();
    private static final String MONK_X_TWO_LOG = MonkXTwoLog.class.getSimpleName();
    private static final Logger LOG = LoggerFactory.getLogger(AssemblyLogListner.class);
    private static final String CHAI_MONK_2_ERRORS = ChaiMonk2Errors.class.getSimpleName();
    private static final String MONK_X_TWO_FAILURE_LOGS = MonkXTwoFailureLogs.class.getSimpleName();

    private static final Gson diagnosisGson = new GsonBuilder().registerTypeAdapter(Date.class, new CustomDateAdapter()).create();

    private static final Gson gson = new GsonBuilder().registerTypeAdapter(Date.class, new FlexibleDateDeserializer()).create();

    private LoggingService loggingService;

    public AssemblyLogListner(LoggingService loggingService) {
        this.loggingService = loggingService;
    }

    @Override
    public void onMessage(Message message) {
        if (message != null) {
            try {
                message.acknowledge();
                if (message instanceof SQSObjectMessage) {
                    if(((SQSObjectMessage) message).getObject() instanceof MonkTaskStatusDetailLog){
                        MonkTaskStatusDetailLog monkTaskStatusDetailLog = (MonkTaskStatusDetailLog) ((SQSObjectMessage) message).getObject();
                        saveLogs((new Gson().toJson(monkTaskStatusDetailLog)));
                    }else{
                        saveLogs(((SQSObjectMessage) message).getMessageBody());
                    }
                } else if (message instanceof SQSTextMessage) {
                    saveLogs(((SQSTextMessage) message).getText());
                } else {
                    LOG.info("Message Not Acknowledged {}", message.getJMSMessageID());
                }
            } catch (JMSException e) {
                LOG.error("Error in acknowledging message for SignUpMessageListner");
            }
        }
    }

    private void saveLogs(String data) {
       data = StringEscapeUtils.unescapeJava(data);
      if(data.startsWith("\"") && data.endsWith("\"")) {
           data = data.substring(1, data.length() - 1);
       }else{
          if(data.substring(0,1).equals('"')){
              data = data.substring(1, data.length() - 1);
          }
      }
        JSONObject obj = new JSONObject(data);
        try {
            String sessionKeyId = obj.getString("skId");
            if(!"republished".equals(sessionKeyId)){
                try {
                    String result = PasswordImpl.decrypt(sessionKeyId);
                    if(StringUtils.isBlank(result) || !result.contains("unitId:")){
                        return;
                    }
                }catch (Exception e){
                    return;
                }
            }
            String className = obj.getString("className");
            LOG.info("Adding Assembly Log - {}", className);
            if (className.equals(MONK_CONSUMPTION_LOG)) {
                MonkConsumptionLog log = gson.fromJson(data, MonkConsumptionLog.class);
                loggingService.addMonkConsumptionLogData(log);
            } else if (className.equals(MONK_TASK_STATUS_LOG)) {
                MonkTaskStatusLog log = gson.fromJson(data, MonkTaskStatusLog.class);
                loggingService.addMonkTaskStatusLogData(log);
            } else if (className.equals(MONK_TASK_STATUS_DETAIL_LOG)) {
                MonkTaskStatusDetailLog log = gson.fromJson(data, MonkTaskStatusDetailLog.class);
                loggingService.addMonkTaskStatusDetailLogData(log);
            } else if (className.equals(MONK_STATUS_LOG)) {
                MonkStatusLog log = gson.fromJson(data, MonkStatusLog.class);
                loggingService.addMonkStatusLogData(log);
//                loggingService.saveMonkTaskCompletionStats(log);
                loggingService.saveRealTimeDashboardDetails(log);
            } else if (className.equals(MONK_SDIAGNOSIS_LOG)) {
                MonkDiagnosisLogData log = diagnosisGson.fromJson(data, MonkDiagnosisLogData.class);
                loggingService.addMonkDiagnosisLogData(log);
            } else if (className.equals(MONK_TASK_COMPLETION_STATS_LOG)) {
                MonkTaskCompletionStatsDto log = gson.fromJson(data, MonkTaskCompletionStatsDto.class);
                loggingService.saveMonkTaskCompletionStatLogs(log);
            } else if (className.equals(ASSEMBLY_ERROR_MESSAGE_THROUGH_SQS)) {
                loggingService.sendGChatMessageOfError(data);
            }
            else if(className.equals(MONK_BROKER_STATUS_LOG)){
                MonkBrokerStatusLog log = gson.fromJson(data, MonkBrokerStatusLog.class);
                loggingService.saveMonkBrokerStatusLog(log);
            }
            else if(className.equals(WEIGHT_CALIBRATION_DETAIL)) {
                WeightCalibrationDetail log = gson.fromJson(data,WeightCalibrationDetail.class);
                loggingService.addWeightCalibrationDetail(log);
            } else if(className.equals(GENERIC_LOG_DATA)) {
                GenericLogData log = gson.fromJson(data,GenericLogData.class);
                loggingService.addGenericLogData(log);
            } else if(className.equals(MONK_X_TWO_LOG)) {
                MonkXTwoLog log = JSONSerializer.toJSON(data, new TypeToken<MonkXTwoLog>(){}.getType());
                loggingService.addMonkXTwoLog(log);
            } else if (className.equals(CHAI_MONK_2_ERRORS)) {
                loggingService.sendGChatMessageOfChaiMonk2Error(data);
            } else if(className.equals(MONK_X_TWO_FAILURE_LOGS)) {
                MonkXTwoFailureLogsDTO log = new Gson().fromJson(data, MonkXTwoFailureLogsDTO.class);
                loggingService.addMonkXTwoFailureLogs(log);
            }
        } catch (Exception e) {
            LOG.info("Exception occured during saving generic assembly log ", e);
        }
    }

}
