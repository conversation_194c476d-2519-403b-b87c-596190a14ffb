package com.stpl.tech.analytics.data.dao;

import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.analytics.dao.UnitAppVersionMetadata;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;

@Repository
public interface UnitAppVersionMetadataRepository extends JpaRepository<UnitAppVersionMetadata, Long> {

    // Find all version records by unit ID
    List<UnitAppVersionMetadata> findByUnitIdOrderByVersionTimeDesc(String unitId);

    // Find latest version by unit ID
    UnitAppVersionMetadata findTopByUnitIdOrderByVersionTimeDesc(String unitId);

    // Find latest version by unit ID and screen type
    UnitAppVersionMetadata findTopByUnitIdAndScreenTypeOrderByVersionTimeDesc(String unitId, ScreenType screenType);

    // Find all latest versions for each screen type for a unit
    @Query("SELECT v FROM UnitAppVersionMetadata v WHERE v.unitId = :unitId AND v.versionTime = " +
           "(SELECT MAX(v2.versionTime) FROM UnitAppVersionMetadata v2 WHERE v2.unitId = :unitId AND v2.screenType = v.screenType)")
    List<UnitAppVersionMetadata> findAllLatestVersionsByUnitId(@Param("unitId") String unitId);
}
