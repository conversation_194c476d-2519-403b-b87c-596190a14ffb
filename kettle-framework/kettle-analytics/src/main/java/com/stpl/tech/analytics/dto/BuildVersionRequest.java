package com.stpl.tech.analytics.dto;

import com.stpl.tech.master.core.service.model.ScreenType;
import java.util.HashMap;
import java.util.Map;

public class BuildVersionRequest {
    private String unitId;
    private Map<ScreenType, String> screenVersions;

    public BuildVersionRequest() {
        this.screenVersions = new HashMap<>();
    }

    // Getters and Setters
    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Map<ScreenType, String> getScreenVersions() {
        return screenVersions;
    }

    public void setScreenVersions(Map<ScreenType, String> screenVersions) {
        this.screenVersions = screenVersions;
    }

    // Helper methods for easier usage
    public void addScreenVersion(ScreenType type, String version) {
        if (screenVersions == null) {
            screenVersions = new HashMap<>();
        }
        screenVersions.put(type, version);
    }

    public String getScreenVersion(ScreenType type) {
        return screenVersions != null ? screenVersions.get(type) : null;
    }
}

