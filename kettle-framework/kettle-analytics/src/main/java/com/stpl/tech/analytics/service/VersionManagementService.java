package com.stpl.tech.analytics.service;

import com.stpl.tech.analytics.dao.UnitAppVersionMetadata;
import com.stpl.tech.analytics.dto.BuildVersionRequest;
import com.stpl.tech.master.core.service.model.ScreenType;

import java.util.List;

public interface VersionManagementService {
    String saveBuildVersion(BuildVersionRequest request);
    List<UnitAppVersionMetadata> getVersionHistory(String unitId);
    BuildVersionRequest getLatestVersions(String unitId);
}
