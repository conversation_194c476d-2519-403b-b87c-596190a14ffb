package com.stpl.tech.analytics.dao;

import com.stpl.tech.master.core.service.model.ScreenType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@Entity
@Table(name = "UNIT_APPLICATION_VERSION")
public class UnitAppVersionMetadata {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Long id;

    @Column(name = "UNIT_ID", nullable = false)
    private String unitId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "VERSION_TIME", nullable = false)
    private Date versionTime;

    @Enumerated(EnumType.STRING)
    @Column(name = "APPLICATION_TYPE", nullable = false, length = 50)
    private ScreenType screenType;

    @Column(name = "APPLICATION_VERSION", length = 50)
    private String applicationVersion;

    // Getters and Setters

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUnitId() {
        return unitId;
    }

    public void setUnitId(String unitId) {
        this.unitId = unitId;
    }

    public Date getVersionTime() {
        return versionTime;
    }

    public void setVersionTime(Date versionTime) {
        this.versionTime = versionTime;
    }

    public ScreenType getScreenType() {
        return screenType;
    }

    public void setScreenType(ScreenType screenType) {
        this.screenType = screenType;
    }

    public String getApplicationVersion() {
        return applicationVersion;
    }

    public void setApplicationVersion(String applicationVersion) {
        this.applicationVersion = applicationVersion;
    }
}

