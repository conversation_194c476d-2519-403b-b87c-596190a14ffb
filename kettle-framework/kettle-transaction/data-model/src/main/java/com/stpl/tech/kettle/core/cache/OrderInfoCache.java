/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.cache;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.TreeSet;
import java.util.stream.Collectors;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.master.domain.model.ConsumptionCodeData;
import com.stpl.tech.master.domain.model.DispenserRecipeVariantKey;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.model.OrderItemStatus;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import javax.annotation.PostConstruct;

import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import com.hazelcast.core.HazelcastInstance;
import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.data.vo.OrderStatusData;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.TemplateRenderingException;

@Repository
public class OrderInfoCache {
	@Autowired
	private OrderSearchService orderService;
	@Autowired
	private EnvironmentProperties props;
	
	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private OrderManagementDao orderManagementDao;

	@Autowired
	@Qualifier(value = "KettleHazelCastInstance")
	private HazelcastInstance instance;


	private static final Logger LOG = LoggerFactory.getLogger(OrderInfoCache.class);

	private static final List<OrderStatus> orderStatusList = Arrays.asList(OrderStatus.CREATED, OrderStatus.PROCESSING,
			OrderStatus.READY_TO_DISPATCH, OrderStatus.CANCELLED_REQUESTED);
	public static final List<OrderStatus> unsettledOrderStaus = Arrays.asList(OrderStatus.CREATED,
			OrderStatus.PROCESSING, OrderStatus.READY_TO_DISPATCH);

	public static final List<OrderStatus> undeliveredOrderStaus = Arrays.asList(OrderStatus.SETTLED);

	private static final List<UnitCategory> categoryList = Arrays.asList(UnitCategory.TAKE_AWAY, UnitCategory.COD,
			UnitCategory.CAFE);
	
	private IMap<Integer, OrderInfo> oMap;
	private IMap<Integer, List<Integer>> undeliveredMessages;
	private IMap<Integer, Map<UnitCategory, Set<Integer>>> orderByCategoryCache;
	private IMap<Integer, Set<OrderEmailEntryType>> orderDeliveryEmail;
	/**
	 * riderContactNo :(allotedNo : orderId)
	 */
	private IMap<String, HashMap<String, Integer>> riderMappingCache;
	private IMap<String,Integer> allotmentCountMap;

	private IMap<String,Integer> resendSMSCount;
	private IMap<DispenserRecipeVariantKey, ConsumptionCodeData> dispenserConsumptionCodeDataMap;
	// Not to use this due to master cache dependency
	// if master cache is not up all the calls will fail
	/*
	 * @PostConstruct public void loadCache() { try { getOrders(categoryList); }
	 * catch (Exception e) { LOG.error("Error while loading Order Info Cache",
	 * e); } }
	 */

	@PostConstruct
	public void createOrderInfoCache(){
		LOG.info("##### Inside PostConstruct Of OrderInfoCache #####");
		LOG.info("##### Creating Order Info Cache #####");
		oMap = instance.getMap("OrderInfoCache:OrdersCache");
		undeliveredMessages = instance.getMap("OrderInfoCache:UndeliveredMessages");
		orderByCategoryCache = instance.getMap("OrderInfoCache:OrderByCategoryCache");
		orderDeliveryEmail = instance.getMap("OrderInfoCache:OrderDeliveryEmail");
		riderMappingCache = instance.getMap("OrderInfoCache:RiderMappingCache");
		allotmentCountMap = instance.getMap("OrderInfoCache:AllotmentCountMap");
		resendSMSCount = instance.getMap("OrderInfoCache:ResendSMSCount");
		dispenserConsumptionCodeDataMap = instance.getMap("OrderInfoCache:dispenserConsumptionCodeDataMap");
	}

	public void clearOrderInfoCache(){
		LOG.info("##### Clearing OrderInfo Cache #####");
		instance.getMap("OrderInfoCache:OrdersCache").clear();;
		instance.getMap("OrderInfoCache:UndeliveredMessages").clear();
		instance.getMap("OrderInfoCache:OrderByCategoryCache").clear();
		instance.getMap("OrderInfoCache:OrderDeliveryEmail").clear();
		instance.getMap("OrderInfoCache:RiderMappingCache").clear();
		instance.getMap("OrderInfoCache:AllotmentCountMap").clear();
		instance.getMap("OrderInfoCache:ResendSMSCount").clear();
	}


	 
	public Boolean addToUndelivered(OrderInfo order) {
		Integer unitId = order.getOrder().getUnitId();
		List<Integer> orderInfoList = getOrderInfoList(unitId);
		//LOG.info("Before adding list size :::: " + orderInfoList.size());
		Boolean flag = orderInfoList.add(order.getOrder().getOrderId());
		oMap.put(order.getOrder().getOrderId(), order);
		//LOG.info("After adding list size :::: " + orderInfoList.size());
		if (flag) {
			undeliveredMessages.put(unitId, orderInfoList);
		}
		return flag;
	}

	public void addEmailDetail(int orderId, OrderEmailEntryType notification) {
		if (!orderDeliveryEmail.containsKey(orderId)) {
			orderDeliveryEmail.put(orderId, new TreeSet<>());
		}
		orderDeliveryEmail.get(orderId).add(notification);
	}

	public boolean checkEmailIfDelivered(int orderId, OrderEmailEntryType notification) {
		if (!orderDeliveryEmail.containsKey(orderId)) {
			return false;
		} else {
			return orderDeliveryEmail.get(orderId).contains(notification);
		}
	}

	public Boolean removeFromUndelivered(Integer orderId, Integer unitId) {
		List<Integer> orderInfoList = getOrderInfoList(unitId);
		//LOG.info("Size of original list is {}", orderInfoList.size());
		List<Integer> saveList = orderInfoList.stream()
				.filter(orderInfo -> !orderInfo.equals(orderId)).collect(Collectors.toList());
		//LOG.info("Size of modified list is {}", saveList.size());
		undeliveredMessages.put(unitId, saveList);
		return true;
	}

	public Integer getFailedMessagesSize(Integer unitId) {
		Integer size = getOrderInfoList(unitId).size();
		//LOG.info("Size of order list in undelivered queue for unitId {} is {}", unitId, size);
		return size;
	}

	public void resetUndeliveredMessages(Integer unitId) {
		undeliveredMessages.put(unitId, new ArrayList<Integer>());
	}

	private List<Integer> getOrderInfoList(Integer unitId) {
		List<Integer> orderInfoList = undeliveredMessages.get(unitId);
		if (orderInfoList == null) {
			orderInfoList = new ArrayList<Integer>();
		}
		return orderInfoList;
	}

	public synchronized void addToCache(OrderInfo order) {
		// LOG.info("Add To cache request for the order - " +
		// order.getOrder().getOrderId());
//		Map<Integer, OrderInfo> ordersMap = get(order.getOrder().getUnitId(),
//				UnitCategory.valueOf(order.getOrder().getSource()));
//		ordersMap.put(order.getOrder().getOrderId(), order);
		Set<Integer> ordersMap = null;
		Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(order.getOrder().getUnitId());
		if (Objects.isNull(categoryMap)) {
			categoryMap = new HashMap<>();
			for (UnitCategory category : categoryList) {
				categoryMap.put(category, new TreeSet<>());
			}
		}
		if (Objects.nonNull(UnitCategory.valueOf(order.getOrder().getSource()))) {
			ordersMap = categoryMap.get(UnitCategory.valueOf(order.getOrder().getSource()));
			if (ordersMap == null || ordersMap.size() == 0) {
				ordersMap = new TreeSet<>();
			}
			ordersMap.add(order.getOrder().getOrderId());
			oMap.put(order.getOrder().getOrderId(), order);
			categoryMap.put(UnitCategory.valueOf(order.getOrder().getSource()), ordersMap);
			orderByCategoryCache.put(order.getOrder().getUnitId(), categoryMap);
		} else {
			ordersMap = new TreeSet<>();
			Collection<Set<Integer>> values = categoryMap.values();
			if (Objects.nonNull(values)) {
				for (Set<Integer> orders : values) {
					ordersMap.addAll(orders);
				}
			}
		}

	}


	public synchronized boolean clearUnitOrderCache(int unitId) throws DataNotFoundException {
		Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(unitId);
		Set<Integer> ordersMap = null;
		try {
			for (UnitCategory category : categoryList) {
				ordersMap = categoryMap.get(category);
				if (Objects.nonNull(ordersMap)) {
					for (Integer orderId : ordersMap) {
						orderDeliveryEmail.remove(orderId);
						oMap.remove(orderId);
					}
				}
				categoryMap.put(category, new TreeSet<>());
			}
			orderByCategoryCache.put(unitId, categoryMap);
			return true;
		} catch (Exception e) {
			LOG.info("Clearing Unit Order Cache encounter an error - ", e);
		}
		return false;
	}

	public synchronized boolean removeFromCache(int unitId, int orderId) throws DataNotFoundException {
		//LOG.info("Remove from cache request for the order - " + orderId);
		Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(unitId);
		if(Objects.isNull(categoryMap)){
			categoryMap = new HashMap<>();
			for (UnitCategory category : categoryList) {
				categoryMap.put(category, new TreeSet<>());
			}
		}
		Set<Integer> ordersMap = null;
		for (UnitCategory category : categoryList) {
			ordersMap = categoryMap.get(category);
			if(Objects.isNull(ordersMap) || ordersMap.isEmpty()){
				ordersMap = new TreeSet<>();
			}
			if (ordersMap.contains(orderId)) {
				try {
					ordersMap.remove(orderId);
					oMap.remove(orderId);
					categoryMap.put(category,ordersMap);
					orderByCategoryCache.put(unitId,categoryMap);
					orderDeliveryEmail.remove(orderId);
					return true;
				} catch (Exception e) {
					LOG.error("Remove from cache request for the order encountered an error- ", e);
					return false;
				}
			}
		}
		return false;
	}

	public void updateStatus(int unitId, int orderId, OrderStatus status, UnitCategory orderSource)
			throws DataNotFoundException, TemplateRenderingException {
		if (oMap != null && !oMap.containsKey(orderId)) {
			cacheMiss(orderId);
		}
		oMap.get(orderId).getOrder().setStatus(status);

	}

	private Map<Integer, OrderInfo> get(int unitId, UnitCategory unitCategory){
		Set<Integer> ordersMap = null;
		Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(unitId);
		if(categoryMap==null){
			categoryMap = new HashMap<>();
			for (UnitCategory category : categoryList) {
				categoryMap.put(category, new TreeSet<>());
			}
		}
		if (Objects.nonNull(unitCategory)) {
			ordersMap = categoryMap.get(unitCategory);
			if (Objects.isNull(ordersMap) || ordersMap.isEmpty()) {
				ordersMap = new TreeSet<>();
//				categoryMap.put(unitCategory, ordersMap);
			}
			categoryMap.put(unitCategory,ordersMap);
			orderByCategoryCache.put(unitId,categoryMap);
		} else {
			ordersMap = new TreeSet<>();
			Collection<Set<Integer>> values = categoryMap.values();
			if (values != null) {
				for (Set<Integer> orders : values) {
					ordersMap.addAll(orders);
				}
			}
		}
//		System.out.println("UNIT CATEGORY");
//		System.out.println(unitCategory);
		Map<Integer, OrderInfo> finalMap = new TreeMap<>();
		if(ordersMap != null && ordersMap.size() >0 ) {
			for(Integer orderId : ordersMap) {
				if(!oMap.containsKey(orderId)) {
					try {
						cacheMiss(orderId);
					} catch (DataNotFoundException | TemplateRenderingException e) {
						LOG.error("Unable to load the order into the cache {}", orderId, e);
					}
				}
				if(oMap.containsKey(orderId)) {
					finalMap.put(orderId, oMap.get(orderId));
				}
			}
		}
		return finalMap;
	}

	/*
	 * private Map<Integer, OrderInfo> getOrdersByCategory(int unitId,
	 * Map<UnitCategory, Set<Integer>> categoryMap, UnitCategory unitCategory) {
	 *
	 * Set<Integer> ordersMap = categoryMap.get(unitCategory); if (ordersMap == null
	 * || ordersMap.size() == 0) { ordersMap = new TreeSet<Integer>();
	 * categoryMap.put(unitCategory, ordersMap); orderByCategoryCache.put(unitId,
	 * categoryMap); } Map<Integer, OrderInfo> finalMap = new TreeMap<>();
	 * if(ordersMap != null && ordersMap.size() >0 ) { for(Integer orderId :
	 * ordersMap) { if(!oMap.containsKey(orderId)) { try { cacheMiss(orderId); }
	 * catch (DataNotFoundException | TemplateRenderingException e) {
	 * LOG.error("Unable to load the order into the cache {}", orderId, e); } }
	 * if(oMap.containsKey(orderId)) { finalMap.put(orderId, oMap.get(orderId)); } }
	 * } return finalMap; }
	 *
	 * private Map<UnitCategory, Map<Integer, OrderInfo>> getCategoryMap(Integer
	 * unitId) { Map<UnitCategory, Map<Integer, OrderInfo>> finalMap = new
	 * HashMap<>(); Map<UnitCategory, Set<Integer>> categoryMap =
	 * orderByCategoryCache.get(unitId); if (categoryMap == null) { categoryMap =
	 * new HashMap<>(); for (UnitCategory category : categoryList) {
	 * categoryMap.put(category, new TreeSet<>()); }
	 * orderByCategoryCache.put(unitId, categoryMap); } else { for (UnitCategory
	 * category : categoryMap.keySet()) { finalMap.put(category, new HashMap<>());
	 * Set<Integer> orderMap = categoryMap.get(category); if(orderMap != null &&
	 * orderMap.size() > 0) { for(Integer orderId : orderMap) {
	 * if(!oMap.containsKey(orderId)) { try { cacheMiss(orderId); } catch
	 * (DataNotFoundException | TemplateRenderingException e) {
	 * LOG.error("Unable to load the order into the cache {}", orderId, e); } }
	 * finalMap.get(category).put(orderId, oMap.get(orderId)); } } } } return
	 * finalMap; }
	 */

	public OrderInfo getOrderById(Integer orderId, UnitCategory orderSource, boolean callCacheMiss, boolean isOrderPresent)
			throws TemplateRenderingException, DataNotFoundException {
		isOrderPresent = oMap.containsKey(orderId);
		if (callCacheMiss && !isOrderPresent) {
			LOG.info("Calling cache miss for order id  " + orderId);
			return cacheMiss(orderId);
		} else {
			return oMap.get(orderId);
		}
	}
	public OrderInfo getOrderById(Integer orderId, UnitCategory orderSource, boolean callCacheMiss)
			throws TemplateRenderingException, DataNotFoundException {
		if (callCacheMiss) {
			LOG.info("Calling cache miss for order id  " + orderId);
			return cacheMiss(orderId);
		} else {
			return oMap.get(orderId);
		}
	}

	public OrderInfo getOrderById(Integer orderId, UnitCategory orderSource)
			throws TemplateRenderingException, DataNotFoundException {
		return getOrderById(orderId, orderSource, true);
	}

	public List<OrderInfo> getOrders(int unitId, UnitCategory category) throws DataNotFoundException {
		List<OrderInfo> info = null;
		Map<Integer, OrderInfo> ordersMap = get(unitId, category);
		if (ordersMap != null) {
			info = new ArrayList<OrderInfo>(ordersMap.values());
		} else {
			info = new ArrayList<>();
		}
		return info;
	}

	public List<OrderInfo> getDeliveryOrders(int unitId, OrderStatus status) throws DataNotFoundException {
		List<OrderInfo> info = new ArrayList<>();
		Map<Integer, OrderInfo> ordersMap = get(unitId, UnitCategory.COD);

		if (ordersMap != null) {
			for (OrderInfo order : ordersMap.values()) {
				if (order.getOrder().getStatus().equals(status)) {
					info.add(order);
				}
			}
		}
		return info;
	}

	public Map<String, Map<Integer, OrderInfo>> getOrders(UnitCategory category,List<String> orderStatus,String generatedOrderId) throws DataNotFoundException {
		return getOrders(Arrays.asList(category),orderStatus,generatedOrderId);
	}

	public Map<String, Map<Integer, OrderInfo>> getOrders(List<UnitCategory> categories,List<String> orderStatus,String generatedOrderId) throws DataNotFoundException {

		Map<String, Map<Integer, OrderInfo>> returnList = new HashMap<>();
		List<OrderStatusData> list = orderService.getOrderStatusForDay(0, orderStatusList, categories,orderStatus,generatedOrderId);
		try {
			if (list != null && list.size() > 0) {
				for (OrderStatusData status : list) {
					int unitId = status.getUnitId();
					UnitBasicDetail detail = masterCache.getUnitBasicDetail(unitId);
					OrderInfo info = getOrder(unitId, status);
					if (!returnList.containsKey(detail.getName())) {
						LOG.info("creating new map for unit {}", detail.getName());
						returnList.put(detail.getName(), new HashMap<>());
					}
					Map<Integer, OrderInfo> ordersMap = returnList.get(detail.getName());

					if (orderStatusList.contains(info.getOrder().getStatus())) {
						ordersMap.put(info.getOrder().getOrderId(), info);
					}

					/*
					 * LOG.info("ordersMap for {} is of size {}", detail.getName(),
					 * returnList.get(detail.getName()).size());
					 */
				}
			}

			orderByCategoryCache.values().stream().forEach(m -> {
				m.get(UnitCategory.COD).forEach(i -> {
					OrderInfo e = oMap.get(i);
					if (OrderStatus.SETTLED.equals(e.getOrder().getStatus())) {
						if (!returnList.containsKey(e.getUnit().getName())) {
							LOG.info("creating new map for unit for status settled(out for delivery) : ",
									e.getUnit().getName());
							returnList.put(e.getUnit().getName(), new HashMap<>());
						}
						Map<Integer, OrderInfo> ordersMap = returnList.get(e.getUnit().getName());
						ordersMap.put(e.getOrder().getOrderId(), e);
					}
				});
			});

		} catch (DataNotFoundException e) {
			LOG.error("Error in Getting orders in line for assembly screen of category");
		}
		return returnList;
	}

	public Map<String, Map<Integer, OrderInfo>> getOrdersByUnitId(UnitCategory category,List<String> orderStatus,String generatedOrderId, int unitId) throws DataNotFoundException {
		return getOrdersByUnitId(Arrays.asList(category),orderStatus,generatedOrderId,unitId);
	}

	public Map<String, Map<Integer, OrderInfo>> getOrdersByUnitId(List<UnitCategory> categories,List<String> orderStatus,String generatedOrderId,int unitIdForSession) throws DataNotFoundException {

		Map<String, Map<Integer, OrderInfo>> returnList = new HashMap<>();
		List<OrderStatusData> list = orderService.getOrderStatusForDay(unitIdForSession, orderStatusList, categories,orderStatus,generatedOrderId);
		try {
			if (list != null && list.size() > 0) {
				for (OrderStatusData status : list) {
					int unitId = status.getUnitId();
					UnitBasicDetail detail = masterCache.getUnitBasicDetail(unitId);
					OrderInfo info = getOrder(unitId, status);
					if (!returnList.containsKey(detail.getName())) {
						LOG.info("creating new map for unit {}", detail.getName());
						returnList.put(detail.getName(), new HashMap<>());
					}
					Map<Integer, OrderInfo> ordersMap = returnList.get(detail.getName());

					if (orderStatusList.contains(info.getOrder().getStatus())) {
						ordersMap.put(info.getOrder().getOrderId(), info);
					}

					/*
					 * LOG.info("ordersMap for {} is of size {}", detail.getName(),
					 * returnList.get(detail.getName()).size());
					 */
				}
			}

			// Get category map for specific unitId
			Map<UnitCategory, Set<Integer>> categoryMap = orderByCategoryCache.get(unitIdForSession);
			if (categoryMap != null && categoryMap.containsKey(UnitCategory.COD)) {
				Set<Integer> codOrders = categoryMap.get(UnitCategory.COD);
				if (codOrders != null) {
					codOrders.forEach(i -> {
						OrderInfo e = oMap.get(i);
						if (e != null && OrderStatus.SETTLED.equals(e.getOrder().getStatus())) {
							if (!returnList.containsKey(e.getUnit().getName())) {
								LOG.info("creating new map for unit for status settled(out for delivery) : ",
										e.getUnit().getName());
								returnList.put(e.getUnit().getName(), new HashMap<>());
							}
							Map<Integer, OrderInfo> ordersMap = returnList.get(e.getUnit().getName());
							ordersMap.put(e.getOrder().getOrderId(), e);
						}
					});
				}
			}

		} catch (DataNotFoundException e) {
			LOG.error("Error in Getting orders in line for assembly screen of category");
		}
		return returnList;
	}


	public List<OrderInfo> getOrders(int unitId) throws DataNotFoundException {
		List<OrderStatusData> list = null;
		List<OrderInfo> infoList = new ArrayList<>();
		/** getting orders from cache */
		Map<Integer, OrderInfo> ordersMap = get(unitId, null);
		if (ordersMap == null || ordersMap.isEmpty()) {
			try {
				list = orderService.getOrderStatusForDay(unitId, orderStatusList, categoryList,null,null);
				if (list == null || list.size() == 0) {
					return infoList;
				}
				for (OrderStatusData status : list) {
					OrderInfo order = getOrder(unitId, status);
					if (order !=null && orderStatusList.contains(order.getOrder().getStatus())) {
						infoList.add(order);
					}
				}
			} catch (DataNotFoundException e) {
				LOG.error("Error in Getting orders in line for assembly screen of unit :" + unitId);
				return infoList;
			}
		} else {
			//LOG.info("Getting orders in line for assembly screen of unit :" + unitId);
			infoList.addAll(ordersMap.values());
		}
		return infoList;
	}

	private OrderInfo getOrder(int unitId, OrderStatusData data) throws DataNotFoundException {
		OrderInfo order = oMap.get(data.getOrderId());
		if (order == null) {
			//LOG.info("cacheMiss called as order is null");
			try {
				order = cacheMiss(data.getOrderId());
				//LOG.info("Fetched order with productId ::: {}", order.getOrder().getOrderId());
			} catch (DataNotFoundException e) {
				LOG.error(String.format(
						"Error in Getting order info line for assembly screen of unit : %d and order productId %d",
						unitId, data.getOrderId()));
			} catch (TemplateRenderingException e) {
				LOG.error(String.format(
						"Error in Rendering receipts of order for assembly screen of unit : %d and order productId %d",
						unitId, data.getOrderId()));
			}
		} else {
			if (!order.getOrder().getStatus().equals(data.getStatus())) {
				order.getOrder().setStatus(data.getStatus());
				oMap.put(data.getOrderId(), order);
			}
		}
		return order;
	}

	private OrderInfo cacheMiss(int orderId) throws DataNotFoundException, TemplateRenderingException {
		//LOG.info("Cache Miss for the order - " + orderId);
		OrderInfo info = orderService.getOrderReceipt(orderId, true, null);
		if(Objects.nonNull(info.getOrder().getTableRequestId()) && info.getOrder().getTableRequestId().intValue() > 0){
			if(Objects.nonNull(info.getOrder()) && !CollectionUtils.isEmpty(info.getOrder().getOrders())) {
				for (OrderItem oi : info.getOrder().getOrders()) {
					OrderItemStatus itemStatus = orderManagementDao.getOrderItemStatusByOrderItemId(oi.getItemId());
					if(OrderStatus.ON_HOLD.value().equalsIgnoreCase(itemStatus.getStatus())){
						oi.setIsHoldOn(AppConstants.YES);
					}
					if(Objects.nonNull(oi.getComposition()) && !CollectionUtils.isEmpty(oi.getComposition().getMenuProducts())){
						for(OrderItem mi : oi.getComposition().getMenuProducts()){
							OrderItemStatus menuItemStatus = orderManagementDao.getOrderItemStatusByOrderItemId(mi.getItemId());
							if(OrderStatus.ON_HOLD.value().equalsIgnoreCase(menuItemStatus.getStatus())) {
								mi.setIsHoldOn(AppConstants.YES);
							}
						}
					}
				}
			}
		}
		LOG.info("Fetching order info for order id - {} completed" , orderId);
		OrderStatus orderStatus = info.getOrder().getStatus();
		if (orderStatusList.contains(orderStatus)) {
			addToCache(info);
		}
		return info;
	}

	public List<OrderStatus> getValidOrderStatuses() {
		return orderStatusList;
	}

	public Map<Integer, OrderStatus> getOrderStatusForAssemblyOrders(Set<Integer> orderIds)
			throws TemplateRenderingException, DataNotFoundException {
		Map<Integer, OrderStatus> returnMap = new HashMap<>();
		for (Integer orderId : orderIds) {
			OrderInfo info = getOrderById(orderId, UnitCategory.COD);
			if (info != null && info.getOrder() != null) {
				returnMap.put(orderId, info.getOrder().getStatus());
			}
		}
		return returnMap;
	}

	public Map<Integer, Map<UnitCategory, Set<Integer>>> getOrderByCategoryCache() {
		return orderByCategoryCache;
	}

	public void allotNoToRider(int orderId, String riderContactNo, String availableNo) {
		String rider = getTrimmedNumber(riderContactNo);
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(rider);
		if(allotedNoMap==null){
			allotedNoMap = new HashMap<>();
		}
		allotedNoMap.put(availableNo, orderId);
		riderMappingCache.put(rider, allotedNoMap);
	}
	
	public void updateMissedCallNoAllotmentCount(String availableNo, String action) {
		if (action.equalsIgnoreCase("ADDED")) {
			allotmentCountMap.put(availableNo,
					allotmentCountMap.get(availableNo) == null ? 1 : allotmentCountMap.get(availableNo) + 1);
		} else if (action.equalsIgnoreCase("REMOVED")) {
			allotmentCountMap.put(availableNo, allotmentCountMap.get(availableNo) == null
					? allotmentCountMap.remove(availableNo) : allotmentCountMap.get(availableNo) - 1);
		}
	}

	public String getAvailableNoForRider(String riderContactNo) {
		String rider = getTrimmedNumber(riderContactNo);
		if(allotmentCountMap.isEmpty()){
			for(String no:props.getDeliveryConfirmationBySDPNosList()){
				allotmentCountMap.put(no,0);
			}
		}
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(rider);
		if(allotedNoMap == null){
			allotedNoMap=new HashMap<>();
		}
		Set<String> allotedNoSet = allotedNoMap.keySet();
		List<String> unAllotedNoList=new ArrayList<>();
		for (String no : props.getDeliveryConfirmationBySDPNosList()) {
			if (!allotedNoSet.contains(no)) {
				unAllotedNoList.add(no);
			}
		}
		LOG.info(" allotmentCountMap : "+allotmentCountMap);
		LOG.info(" unAllotedNoList : "+unAllotedNoList);
		if(unAllotedNoList.size()>0){
			allotedNoMap=getSmallestValueMap(allotmentCountMap, unAllotedNoList);
			LOG.info(" allotedNoMap  : "+allotedNoMap);
			return (String) allotedNoMap.keySet().toArray()[0];
		}
		return null;
	}
	
	private String getTrimmedNumber(String riderContactNo) {
		return riderContactNo.substring(riderContactNo.length() - 10, riderContactNo.length());
	}

	public int getOrderIdOfRider(String riderContactNo,String allotedNo) {
		String rider = getTrimmedNumber(riderContactNo);
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(rider);
		if (allotedNoMap != null) {
			for (Map.Entry<String, Integer> entry : allotedNoMap.entrySet()) {
				if (entry.getKey().equals(allotedNo)) {
					return entry.getValue();
				}
			}
		}
		return 0;
	}
	
	private HashMap<String, Integer> getSmallestValueMap(Map<String, Integer> allotedNoMap,
			List<String> unAllotedNoList) {
		HashMap<String, Integer> hmap = null;
		int smallestCount = Integer.MAX_VALUE;
		for (Map.Entry<String, Integer> entry : allotedNoMap.entrySet()) {
			if (unAllotedNoList.contains(entry.getKey())) {
				if (entry.getValue() < smallestCount) {
					smallestCount=entry.getValue();
					hmap=new HashMap<>();
					hmap.put(entry.getKey(),entry.getValue());
				}
			}
		}
		return hmap;
	}
	/**
	 * removeAllotedNoToRider to remove when order is delivered
	 * @param riderContactNo
	 * @param orderId
	 */
	public boolean removeAllotedNoToRider(String riderContactNo, int orderId) {
		String rider = getTrimmedNumber(riderContactNo);
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(rider);
		if (allotedNoMap != null) {
			for (Map.Entry<String, Integer> entry : allotedNoMap.entrySet()) {
				if (entry.getValue() == orderId) {
					allotedNoMap.remove(entry.getKey());
					riderMappingCache.put(rider,allotedNoMap);
					return true;
				}
			}
		}
		return false;
	}

	public void removeAllotedNoToRider(String riderContactNo, String allotedContactNo) {
		String rider = getTrimmedNumber(riderContactNo);
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(rider);
		if (allotedNoMap != null) {
			Set<String> allotedNoSet = allotedNoMap.keySet();
			for (String no : props.getDeliveryConfirmationBySDPNosList()) {
				if (allotedNoSet.contains(no)) {
					allotedNoMap.remove(allotedContactNo);
				}
			}
		}
		riderMappingCache.put(rider,allotedNoMap);
	}
	
	public void resetRiderAllotedNoMapping(String riderContactNo) {
		if(riderMappingCache.get(riderContactNo)!=null){
			riderMappingCache.remove(riderContactNo);
		}
	}
	
	public HashMap<String, Integer> getSDPOrders(String riderContactNo) {
		String riderContact = getTrimmedNumber(riderContactNo);
		if (riderMappingCache.get(riderContact) != null) {
			return riderMappingCache.get(riderContact);
		}
		return null;
	}
	
	public boolean checkRiderPoolForOrder(String riderContactNo, int orderId) {
		HashMap<String, Integer> allotedNoMap = riderMappingCache.get(getTrimmedNumber(riderContactNo));
		if (allotedNoMap != null) {
			for (Map.Entry<String, Integer> entry : allotedNoMap.entrySet()) {
				if (entry.getValue() == orderId) {
					return true;
				}
			}
		}
		return false;
	}
	
	public void  updateSMSCount(String generatedOrderId) {
		if(resendSMSCount.get(generatedOrderId)!=null){
			resendSMSCount.put(generatedOrderId, resendSMSCount.get(generatedOrderId)+1);
		}else{
			resendSMSCount.put(generatedOrderId, 1);
		}
	}
	
	public int  getSMSCount(String generatedOrderId) {
		if(resendSMSCount.get(generatedOrderId)!=null){
			return resendSMSCount.get(generatedOrderId);
		}else{
			return 0;
		}
		
	}
	
	public void  resetSMSCount(String generatedOrderId) {
		if(resendSMSCount.get(generatedOrderId)!=null){
			resendSMSCount.remove(generatedOrderId);
		}
		
	}

	@Override
	public String toString() {
		return "OrderInfoCache{" +
				"undeliveredMessages=" + undeliveredMessages.size() +
				", orderByCategoryCache=" + orderByCategoryCache.size() +
				", orderDeliveryEmail=" + orderDeliveryEmail.size() +
				", riderMappingCache=" + riderMappingCache.size() +
				", allotmentCountMap=" + allotmentCountMap.size() +
				", resendSMSCount=" + resendSMSCount.size() +
				'}';
	}

	public OrderInfo getOrder(Integer orderId) {
		return oMap.get(orderId);
	}

	public boolean updateOrderInCache(OrderInfo orderInfo){
		try {
			if(oMap.containsKey(orderInfo.getOrder().getOrderId())){
				oMap.put(orderInfo.getOrder().getOrderId(),orderInfo);
			}
		}catch (Exception e){
			LOG.info("Error in updating item in cache : {}",e);
		}
		return false;
	}

	public IMap<DispenserRecipeVariantKey, ConsumptionCodeData> getDispenserConsumptionCodeDataMap() {
		return dispenserConsumptionCodeDataMap;
	}

	public void clearDispenserConsumptionCodeDataMap() {
		this.dispenserConsumptionCodeDataMap.clear();
	}

	public ConsumptionCodeData getDispenserConsumptionCodeData(DispenserRecipeVariantKey key) {
		return dispenserConsumptionCodeDataMap.get(key);
	}
}
