package com.stpl.tech.kettle.service.controller;

import com.google.common.io.Files;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportProvider;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.reports.model.UnitConsumptionItemKettleRevamp;
import com.stpl.tech.kettle.reports.model.UnitSettlementReportKettleRevamp;
import com.stpl.tech.kettle.reports.view.ScmReportView;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.CondimentItemData;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdNameValue;
import com.stpl.tech.master.domain.model.IdNameValueMap;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.readonly.domain.model.ProductPriceVO;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.recipe.model.CondimentsData;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.TreeSet;
import java.util.stream.Collectors;

@Component
public class ItemConsumptionHelper {

	private static final Logger LOG = LoggerFactory.getLogger(ItemConsumptionHelper.class);

	@Autowired
	private RecipeCache recipeCache;
	@Autowired
	private MasterDataCache dataCache;

	@Autowired
	private TaxDataCache taxDataCache;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private MetadataCache cache;

	public Map<Integer, ProductQuantityData> getCriticalConsumption(Order o, Integer deliveryUnitId) {

		if (OrderStatus.CANCELLED.equals(o.getStatus())
				&& !"NO_WASTAGE".equals(o.getWastageType())
				&& (Objects.isNull(o.getNonWastageItemMap()) || o.getNonWastageItemMap().isEmpty())) {
			return new HashMap<>();
		}
		Map<Integer, ProductQuantityData> finalData = new HashMap<>();
		for (OrderItem item : o.getOrders()) {
			if (item.getRecipeId() == 0) {
				continue;
			}
			int prevQuantity = item.getQuantity();
			Integer nonWasteQty = getQuantityOfNonWastageItem(o.getNonWastageItemMap(), item.getItemId());
			if(OrderStatus.CANCELLED.equals(o.getStatus())
					&& !"NO_WASTAGE".equals(o.getWastageType())
					&& !"COMBO".equals(item.getCode())){
				if(nonWasteQty.equals(0)){
					continue;
				}

				item.setQuantity(nonWasteQty);
			}
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			boolean taxable = dataCache.getProduct(item.getProductId()).isTaxableCogs();
			Map<Integer, Consumable> map = new HashMap<>();
			boolean pickDineInConsumablesFlag = dataCache.pickDineInConsumablesProduct(o.getUnitId(), item.getProductId(), item.getDimension());
            parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(), taxable, "INVENTORY", null, false, pickDineInConsumablesFlag);
			if (map != null && map.size() > 0) {
				for (Integer key : map.keySet()) {
					// if (criticalProducts.contains(key)) {
					// let consumption enabled for non critical products also
					Consumable consumable = map.get(key);
					if (!finalData.containsKey(key)) {
						finalData.put(key, new ProductQuantityData(key, consumable.getQuantity(), consumable.getUom()));
					} else {
						finalData.get(key).addQuantity(consumable.getQuantity());
					}
					// }
				}
			}
			item.setQuantity(prevQuantity);
		}
		return finalData;
	}

	public void addConsumption(Order o, Map<Integer, Consumable> map, Integer deliveryUnitId, boolean consumptionBasedOnItemRecipe) {

		if (TransactionUtils.isSkipOrder(o)) {
			return;
		}
		calculateConsumption(o, map, deliveryUnitId, consumptionBasedOnItemRecipe);
	}

	public void calculateConsumption(Order o, Map<Integer, Consumable> map, Integer deliveryUnitId, boolean consumptionBasedOnItemRecipe) {
		for (OrderItem item : o.getOrders()) {
			Integer nonWasteQty = getQuantityOfNonWastageItem(o.getNonWastageItemMap(), item.getItemId());
			if(nonWasteQty.equals(item.getQuantity())){
				continue;
			}
			int prevQuantity = item.getQuantity();
			item.setQuantity(prevQuantity - nonWasteQty);
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			Integer recipeId = null;
			if (consumptionBasedOnItemRecipe && Objects.nonNull(item.getRecipeProfile()) && item.getRecipeId() != 0) {
				recipeProfile = item.getRecipeProfile();
				recipeId = item.getRecipeId();
			} else {
				consumptionBasedOnItemRecipe = false;
			}
			boolean taxable = dataCache.getProduct(item.getProductId()).isTaxableCogs();
			boolean pickDineInConsumablesFlag = dataCache.pickDineInConsumablesProduct(o.getUnitId(), item.getProductId(), item.getDimension());
			parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(), taxable, "BOOK_WASTAGE", recipeId,
					consumptionBasedOnItemRecipe, pickDineInConsumablesFlag);
			item.setQuantity(prevQuantity);
		}
	}

	private Integer getQuantityOfNonWastageItem(Map<Integer,Integer> nonWastageItemMap,int itemId){
		if(Objects.isNull(nonWastageItemMap) || !nonWastageItemMap.containsKey(itemId)){
			return 0;
		}
		return Math.max(nonWastageItemMap.get(itemId),0);
	}

	public List<CondimentItemData> getCondimentItemQuantity(Order o, String Source){
		Map<Integer,CondimentItemData>  condimentItemMap= new HashMap<>();
		List<CondimentItemData> condimentItemList = new ArrayList<>();
		for(OrderItem item:o.getOrders()){
			if(Objects.nonNull(item.getProductCategory()) && Objects.nonNull(item.getProductCategory().getId()) && item.getProductCategory().getId() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE &&
					Objects.nonNull(item.getComposition()) && Objects.nonNull(item.getComposition().getMenuProducts()) && item.getComposition().getMenuProducts().size()>0) {

				for (OrderItem comboItem : item.getComposition().getMenuProducts()) {
					if (Objects.nonNull(comboItem.getQuantity()) && Objects.nonNull(comboItem.getQuantity()) && Objects.nonNull(comboItem.getRecipeId()) && Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(comboItem.getRecipeId())) &&
							Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(comboItem.getRecipeId()).get(Source)) && Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(comboItem.getRecipeId()).get(Source).getGroupId()) &&
							Objects.nonNull(dataCache.getGroupItemCondimentMapping(recipeCache.getRecipeToSrcGroupCondiment(comboItem.getRecipeId()).get(Source).getGroupId()))) {
						CondimentsData recipeCondimentData = recipeCache.getRecipeToSrcGroupCondiment(comboItem.getRecipeId()).get(Source);

						for (CondimentItemData condiment : dataCache.getGroupItemCondimentMapping(recipeCondimentData.getGroupId())) {
							if (Objects.nonNull(condiment.getItemId()) && Objects.nonNull(condiment.getQuantity()) && Objects.nonNull(condiment.getName())) {
								if (!condimentItemMap.containsKey(condiment.getItemId())) {
									BigDecimal qty = AppUtils.multiply(AppUtils.multiply(new BigDecimal(item.getQuantity()), new BigDecimal(recipeCondimentData.getQuantity())), new BigDecimal(condiment.getQuantity()));
									condiment.setQuantity(qty.intValue());
									condimentItemMap.put(condiment.getItemId(), condiment);
								} else {
									CondimentItemData data = condimentItemMap.get(condiment.getItemId());
									BigDecimal qty = AppUtils.multiply(AppUtils.multiply(new BigDecimal(item.getQuantity()), new BigDecimal(recipeCondimentData.getQuantity())), new BigDecimal(condiment.getQuantity()));
									Integer totalQty = data.getQuantity() + (qty.intValue());
									data.setQuantity(totalQty);
									condimentItemMap.put(condiment.getItemId(), data);
								}
							}
						}
					}
				}
			}
			else if(Objects.nonNull(item.getQuantity()) && Objects. nonNull(item.getRecipeId()) && Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(item.getRecipeId())) &&
					Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(item.getRecipeId()).get(Source)) && Objects.nonNull(recipeCache.getRecipeToSrcGroupCondiment(item.getRecipeId()).get(Source).getGroupId()) &&
					Objects.nonNull(dataCache.getGroupItemCondimentMapping(recipeCache.getRecipeToSrcGroupCondiment(item.getRecipeId()).get(Source).getGroupId()))){
				CondimentsData recipeCondimentData = recipeCache.getRecipeToSrcGroupCondiment(item.getRecipeId()).get(Source);

				for(CondimentItemData condiment:dataCache.getGroupItemCondimentMapping(recipeCondimentData.getGroupId())){
					if(Objects.nonNull(condiment.getItemId()) && Objects.nonNull(condiment.getQuantity()) && Objects.nonNull(condiment.getName()) ){
						if(!condimentItemMap.containsKey(condiment.getItemId())){
							BigDecimal qty = AppUtils.multiply(AppUtils.multiply(new BigDecimal(item.getQuantity()),new BigDecimal(recipeCondimentData.getQuantity())),new BigDecimal(condiment.getQuantity()));
							condiment.setQuantity(qty.intValue());
							condimentItemMap.put(condiment.getItemId(),condiment);
						}
						else {
							CondimentItemData data = condimentItemMap.get(condiment.getItemId());
							BigDecimal qty = AppUtils.multiply(AppUtils.multiply(new BigDecimal(item.getQuantity()),new BigDecimal(recipeCondimentData.getQuantity())),new BigDecimal(condiment.getQuantity()));
							Integer totalQty = data.getQuantity()+(qty.intValue());
							data.setQuantity(totalQty);
							condimentItemMap.put(condiment.getItemId(),data);
						}
					}
				}

			}
		}
		
		Map<String,CondimentItemData> totalItemCodimentMap = new HashMap<>();

		for (Integer condimentItemId : condimentItemMap.keySet()){
			CondimentItemData data = condimentItemMap.get(condimentItemId);
			if(Objects.nonNull(data.getName())){
				if(!totalItemCodimentMap.containsKey(data.getName())){
					totalItemCodimentMap.put(data.getName(),data);
				}
				else {
					CondimentItemData itemObj = totalItemCodimentMap.get(data.getName());
					itemObj.setQuantity(itemObj.getQuantity()+ data.getQuantity());
					totalItemCodimentMap.put(data.getName(),itemObj);
				}
			}
		}

		for (String condimentName : totalItemCodimentMap.keySet()){
			condimentItemList.add(totalItemCodimentMap.get(condimentName));
		}

		return condimentItemList;
	}

	public List<OrderItemConsumable> getConsumption(Order o, Integer deliveryUnitId) {
		List<OrderItemConsumable> rlist = new ArrayList<>();
		for (OrderItem item : o.getOrders()) {
			Map<Integer, Consumable> map = new HashMap<>();
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			boolean taxable = dataCache.getProduct(item.getProductId()).isTaxableCogs();
			boolean pickDineInConsumablesFlag = dataCache.pickDineInConsumablesProduct(o.getUnitId(), item.getProductId(), item.getDimension());
			parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(), taxable, null, null, false, pickDineInConsumablesFlag);
			for (Consumable consumable : map.values())
				rlist.add(new OrderItemConsumable(item, consumable));
		}

		return rlist;
	}

	public Map<IdCodeName, IdNameValueMap> getConsumptionMap(Order o, Integer deliveryUnitId) {
		Map<IdCodeName, IdNameValueMap> itemMap = new HashMap<>();
		for (OrderItem item : o.getOrders()) {
			boolean taxable = dataCache.getProduct(item.getProductId()).isTaxableCogs();
			IdCodeName product = new IdCodeName(item.getProductId(), item.getProductName(), item.getDimension());

				IdNameValueMap dataObject = itemMap.get(product);

				if (dataObject == null) {
					dataObject = new IdNameValueMap();
				IdNameValue v = new IdNameValue(item.getProductId(), item.getProductName(), item.getDimension());
				v.setValue(v.getValue().add(new BigDecimal(item.getQuantity())));
				dataObject.setValue(v);

				itemMap.put(product, dataObject);
			} else {
				dataObject.getValue()
						.setValue(dataObject.getValue().getValue().add(new BigDecimal(item.getQuantity())));
			}

			Map<IdCodeName, IdNameValue> scmMap = dataObject.getValueMap();

			Map<Integer, Consumable> map = new HashMap<>();
			String recipeProfile = getRecipeProfile(item.getProductId(), o.getUnitId(), deliveryUnitId, o.getSource(),
					item.getDimension(), o.getBrandId());
			boolean pickDineInConsumablesFlag = dataCache.pickDineInConsumablesProduct(o.getUnitId(), item.getProductId(), item.getDimension());
			parseItem(o, item, o.getSource(), map, o.getUnitId(), deliveryUnitId, recipeProfile, o.getBrandId(), taxable, null, null, false, pickDineInConsumablesFlag);
			for (Consumable consumable : map.values()) {

				IdCodeName scmKey = new IdCodeName(consumable.getProductId(), consumable.getName(),
						consumable.getUom());

				IdNameValue valueData = scmMap.get(scmKey);

				if (valueData == null) {
					valueData = new IdNameValue(consumable.getProductId(), consumable.getName(), consumable.getUom());
					valueData.setValue(consumable.getQuantity());
					scmMap.put(scmKey, valueData);
				} else {
					valueData.setValue(valueData.getValue().add(consumable.getQuantity()));
				}
			}
		}

		return itemMap;
	}

	private void parseItem(Order o, OrderItem item, String source, Map<Integer, Consumable> map, int unitId,
						   Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable, String parsingFor, Integer recipeId,
						   boolean consumptionBasedOnItemRecipe, boolean pickDineInConsumablesFlag) {
		parseComposition(o, item, source, map, unitId, deliveryUnitId, recipeProfile, brandId, taxable, parsingFor, consumptionBasedOnItemRecipe, pickDineInConsumablesFlag);
		addSupplements(item.getTakeAway(), item.getProductId(), item.getDimension(), new BigDecimal(item.getQuantity()),
				source, map, recipeProfile, taxable, recipeId,Objects.nonNull(item.getMilkVariant()), pickDineInConsumablesFlag);
	}

	private void parseComposition(Order o, OrderItem item, String source, Map<Integer, Consumable> map, int unitId,
								  Integer deliveryUnitId, String recipeProfile, int brandId, boolean taxable, String parsingFor,
								  boolean consumptionBasedOnItemRecipe, boolean  pickDineInConsumablesFlag) {
        String consumptionHelperProfile = Objects.nonNull(item.getMilkVariant()) ?
				AppUtils.getMilkVariantPaidAddonPrefix(item.getMilkVariant().getProductName()) + recipeProfile : recipeProfile;
		Consumable c = null;
		OrderItemComposition composition = item.getComposition();
		if (composition != null) {
			// Variants
			if (composition.getVariants() != null && !composition.getVariants().isEmpty()) {
				if (DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile).getProducts().contains(item.getProductId())) {
					List<Consumable> consumptions = DesiChaiConsumptionHelper.getInstance(consumptionHelperProfile)
							.getConsumption(item, composition.getVariants());
					for (Consumable con : consumptions) {
						add(con, map, taxable);
					}

				} else {
					for (IngredientVariantDetail detail : composition.getVariants()) {
						c = createConsumable(detail, item.getQuantity(),taxable);
						add(c, map, taxable);
					}

				}
			}

			// products
			if (composition.getProducts() != null && !composition.getProducts().isEmpty()) {
				for (IngredientProductDetail detail : composition.getProducts()) {
					c = createConsumable(detail, item.getQuantity(), taxable);
					add(c, map, taxable);
				}
			}

			// Add-ons
			LOG.info("parse Composition Addons() {}", composition.getAddons());
			if (composition.getAddons() != null && !composition.getAddons().isEmpty()) {
				for (IngredientProductDetail detail : composition.getAddons()) {
					// Add-ons are menu products
					addSupplements(item.getTakeAway(), detail.getProduct().getProductId(),
							detail.getDimension().getCode(),
							detail.getQuantity().multiply(new BigDecimal(item.getQuantity())), source, map,
							recipeProfile, taxable, null,false, pickDineInConsumablesFlag);
				}
			}

			// Menu Products
			if (composition.getMenuProducts() != null && !composition.getMenuProducts().isEmpty()) {
				for (OrderItem i : composition.getMenuProducts()) {
					Integer nonWasteQty = getQuantityOfNonWastageItem(o.getNonWastageItemMap(), i.getItemId());
					int prevQuantity = i.getQuantity();
					if(Objects.nonNull(parsingFor)){
						if("BOOK_WASTAGE".equals(parsingFor)){
							if(nonWasteQty.equals(item.getQuantity())){
								continue;
							}
							i.setQuantity(prevQuantity - nonWasteQty);
						} else if ("INVENTORY".equals(parsingFor)
								&& OrderStatus.CANCELLED.equals(o.getStatus())
								&& !"NO_WASTAGE".equals(o.getWastageType())) {
							if(nonWasteQty.equals(0)){
								continue;
							}
							i.setQuantity(nonWasteQty);
						}
					}

					String profile = getRecipeProfile(i.getProductId(), unitId, deliveryUnitId, source,
							i.getDimension(), brandId);
					Integer recipeId = null;
					if (consumptionBasedOnItemRecipe && Objects.nonNull(item.getRecipeProfile()) && item.getRecipeId() != 0) {
						profile = item.getRecipeProfile();
						recipeId = item.getRecipeId();
					} else {
						consumptionBasedOnItemRecipe = false;
					}
					boolean taxable1 =dataCache.getProduct(i.getProductId()).isTaxableCogs();
					parseItem(o, i, source, map, unitId, deliveryUnitId, profile, brandId, taxable1, parsingFor,
							recipeId, consumptionBasedOnItemRecipe, pickDineInConsumablesFlag);
					i.setQuantity(prevQuantity);
				}
			}
		}
	}

	private void addSupplements(Boolean takeAway, int id, String dia, BigDecimal qty, String source,
								Map<Integer, Consumable> map, String recipeProfile, boolean taxable, Integer recipeId , boolean containsMilkVariant, boolean pickDineInConsumablesFlag) {
		LOG.info("Looking Consumption for pickDineInConsumablesFlag {}, id {}, dia {}, qty {}, source {}, profile {}", pickDineInConsumablesFlag, id, dia,
				qty, source, recipeProfile);
		RecipeDetail r;
		if (Objects.isNull(recipeId)) {
			r = recipeCache.getRecipe(id, dia, recipeProfile);
		} else {
			r = recipeCache.getRecipe(recipeId);
		}
		if (r == null) {
			r = recipeCache.getRecipe(id, dia, AppConstants.DEFAULT_RECIPE_PROFILE);
		}
		if (r != null) {
			//LOG.info("Found Consumption for takeaway {}, id {}, dia {}, qty {}, source {}, profile {}", takeAway, id, dia, qty,
			//		source, recipeProfile);
			r.getIngredient().getComponents().stream().filter(component -> !containsMilkVariant ||
							Objects.isNull(component.getProduct()) || AppConstants.SCM_MILK_PRODUCT_ID != component.getProduct().getProductId()
							).
					forEach(p -> add(createConsumable(p, qty, taxable), map, taxable));
			switch (UnitCategory.valueOf(source)) {
			case COD:
				addSupplementaryItem(qty, map, r.getDeliveryConsumables(), taxable);
				break;
			case CAFE:
				if (takeAway != null && takeAway) {
					addSupplementaryItem(qty, map, r.getTakeawayConsumables(), taxable);
				} else if(pickDineInConsumablesFlag){
					addSupplementaryItem(qty, map, r.getDineInConsumables(), taxable);
				}
				break;
			case TAKE_AWAY:
				addSupplementaryItem(qty, map, r.getTakeawayConsumables(), taxable);
				break;
			default:
				break;
			}
		}
	}

	private String getRecipeProfile(int productId, int unitId, int deliveryUnitId, String source, String dimension,
			int brandId) {
		String recipeProfile = null;

		Product product = dataCache.getProduct(productId);
		if (product.getClassification() == ProductClassification.FREE_ADDON
				|| product.getClassification() == ProductClassification.PAID_ADDON) {
			recipeProfile = AppConstants.DEFAULT_RECIPE_PROFILE;
		} else {
			if (UnitCategory.COD.equals(UnitCategory.valueOf(source))) {
				// TODO check this if it breaks anything
				if (brandId == AppConstants.CHAAYOS_BRAND_ID) {
					try {
						recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
					} catch (Exception e) {
						recipeProfile = recipeCache.getUnitProductProfile(deliveryUnitId, productId, dimension);
					}

				} else {
					try {
						recipeProfile = recipeCache.getUnitProductProfile(deliveryUnitId, productId, dimension);
					} catch (Exception e) {
						recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
					}
				}
			} else {
				recipeProfile = recipeCache.getUnitProductProfile(unitId, productId, dimension);
			}
		}
		if (recipeProfile == null) {
			LOG.error(String.format(
					"############# Could Not Find Recipe Profile for Unit Id %s, Delivery Unit Id %s, Product Id %s, Dimension Id %s, Order Source %s, Brand Id %s ",
					unitId, deliveryUnitId, productId, dimension, source, brandId));
			return AppConstants.DEFAULT_RECIPE_PROFILE;
		}
		return recipeProfile;
	}

	private void addSupplementaryItem(BigDecimal qty, Map<Integer, Consumable> map,
			List<IngredientProductDetail> ingredients, boolean taxable) {

		Consumable c = null;
		for (IngredientProductDetail i : ingredients) {
			c = createConsumable(i, qty, taxable);
			add(c, map, taxable);
		}
	}

	private Consumable createConsumable(IngredientProductDetail detail, int qty, boolean taxable) {
		return createConsumable(detail, new BigDecimal(qty), taxable);
	}

	private Consumable createConsumable(IngredientProductDetail detail, BigDecimal qty, boolean taxable) {
		Consumable c = new Consumable();
		c.setProductId(detail.getProduct().getProductId());
		if(taxable){
			c.setTaxableQuantity(detail.getQuantity().multiply(qty));
		}
		c.setQuantity(detail.getQuantity().multiply(qty));
		c.setUom(detail.getUom() == null ? null : detail.getUom().name());
		c.setName(detail.getProduct().getName());
		return c;
	}

	private Consumable createConsumable(IngredientVariantDetail detail, int qty, boolean taxable) {
		return createConsumable(detail, new BigDecimal(qty),taxable);
	}

	private Consumable createConsumable(IngredientVariantDetail detail, BigDecimal qty, boolean taxable) {
		Consumable c = new Consumable();
		c.setProductId(detail.getProductId());
		if(taxable){
			c.setTaxableQuantity(detail.getQuantity().multiply(qty));
		}
		c.setQuantity(detail.getQuantity().multiply(qty));
		c.setUom(detail.getUom() == null ? null : detail.getUom().name());
		c.setName(detail.getAlias());
		return c;
	}

	private void add(Consumable c, Map<Integer, Consumable> map, boolean taxable) {
		Consumable e = map.get(c.getProductId());
		if (e != null) {

			if (taxable) {
				e.setTaxableQuantity(AppUtils.add(e.getTaxableQuantity(), c.getQuantity()));
			}
			e.setQuantity(e.getQuantity().add(c.getQuantity()));
		} else {
			map.put(c.getProductId(), c);
		}
	}

	public ReportFileData writeToSCMRepotFile(ConsumptionData data, EnvironmentProperties props) {

		WorkbookContext workbookCtx = WorkbookContextFactory.useXlsx().createWorkbook();
		scmReportSummaryView(workbookCtx, data);
		return generateSCMReport(data, props, workbookCtx);
	}

	private void scmReportSummaryView(WorkbookContext workbookCtx, ConsumptionData data) {
		ScmReportView view = new ScmReportView("SCM Report", "SCM");
		view.render(workbookCtx, data);

	}

	private ReportFileData generateSCMReport(ConsumptionData data, EnvironmentProperties props,
			WorkbookContext workbookCtx) {
		ReportFileData scmReport = getSCMReportFileData(data, props);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(scmReport.getFilePath()));
		} catch (IOException e) {
			LOG.error("Error in writing SCM report for unit " + data.getUnitId(), e);
			scmReport.setGenerated(false);
		}
		return scmReport;
	}

	private ReportFileData getSCMReportFileData(ConsumptionData data, EnvironmentProperties props) {
		String fileName = "SCMReport-" + data.getUnitId() + "-" + AppUtils.getCurrentTimeISTStringWithNoColons()
				+ ".xlsx";
		String fileDir = props.getBasePath() + "/" + data.getUnitId() + "/reports/scm/";
		File file = new File(fileDir);
		if (file != null && !file.exists()) {
			boolean madeDirectories = file.mkdirs();
			LOG.error("made directories for the scm report  {} ::: {}", fileDir, madeDirectories);
		}
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.SUPPLY_CHAIN, AppConstants.EXCEL_MIME_TYPE, fileName,
				filePath, true);
		return fileData;

	}

	public List<UnitConsumptionItemKettleRevamp> getUnitConsumptionData(List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions){
		List<String> nameSplit = null;
		String name = null;
		List<UnitConsumptionItemKettleRevamp> consumptionList = new ArrayList<>();
		for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : productConsumptions) {
			for (ProductDimensionConsumption<ProductPriceConsumption> product : source.getAllItems()) {
				if (product.getQuantity() > 0 || product.getComplimentaryQuantity() > 0
						|| product.getCompositeQuantity() > 0) {

					name = product.getName();
					if (!AppConstants.NO_DIMENSION_STRING.equals(product.getDimension())) {
						name = name + " " + product.getDimension();
					}

					//nameSplit = AppUtils.wordList(name, 20);
					UnitConsumptionItemKettleRevamp itemConsumption = UnitConsumptionItemKettleRevamp.builder().item(name.trim()).source(source.getName()).quantity(product.getQuantity()).complimentaryQuantity(product.getComplimentaryQuantity()).compositeQuantity(product.getCompositeQuantity()).build();
					consumptionList.add(itemConsumption);
				}
			}
		}
		return consumptionList;
	}

	public UnitSettlementReportKettleRevamp getSetlementReportDataRevamp(UserSessionDetail userSession, List<Order> orders) {
		int unitId = userSession.getUnitId();
		Unit unit = dataCache.getUnit(unitId);
		SettlementReportInputData data = new SettlementReportInputData(AppUtils.getCurrentDate(), unit, orders, false,
				null, null,null);
		SettlementReportProvider settlementProvider = new SettlementReportProvider(dataCache, cache);
		settlementProvider.process(data,
				new TreeSet<>(taxDataCache.getSaleTaxations().get(unit.getLocation().getState().getId())));
		Collection<SettlementReport> products = settlementProvider.getByPaymentType();
		// return UnitSettlementReportKettleRevamp.builder().unitId(unit.getId()).unitName(unit.getName()).generationTime(AppUtils.getCurrentTimeISTString()).noOfOrders(orders.size()).report(products).basePath(props.getBasePath()).build();
		List<SettlementReport> productList =products.stream().filter(settlement->((Objects.nonNull(settlement.getAmount()) && BigDecimal.ZERO.compareTo(settlement.getAmount()) != 0) || (Objects.nonNull(settlement.getGrossAmount()) && BigDecimal.ZERO.compareTo(settlement.getGrossAmount()) != 0) || (Objects.nonNull(settlement.getDiscountAmount()) && BigDecimal.ZERO.compareTo(settlement.getDiscountAmount()) != 0) || (Objects.nonNull(settlement.getExtraVouchers()) && BigDecimal.ZERO.compareTo(settlement.getExtraVouchers()) != 0) || (Objects.nonNull(settlement.getTotal()) && BigDecimal.ZERO.compareTo(settlement.getTotal()) != 0))).collect(Collectors.toList());
		return UnitSettlementReportKettleRevamp.builder().unitId(unit.getId()).unitName(unit.getName()).generationTime(AppUtils.getCurrentTimeISTStringWithoutMS()).noOfOrders(orders.size()).report(productList).basePath(props.getBasePath()).build();
	}
}
