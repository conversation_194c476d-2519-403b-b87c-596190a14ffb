/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.REPORT_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.InputStream;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import com.itextpdf.text.DocumentException;
import com.itextpdf.tool.xml.XMLWorker;
import com.itextpdf.tool.xml.html.Tags;
import com.itextpdf.tool.xml.parser.XMLParser;
import com.itextpdf.tool.xml.pipeline.css.CSSResolver;
import com.itextpdf.tool.xml.pipeline.css.CssResolverPipeline;
import com.itextpdf.tool.xml.pipeline.end.PdfWriterPipeline;
import com.itextpdf.tool.xml.pipeline.html.HtmlPipeline;
import com.itextpdf.tool.xml.pipeline.html.HtmlPipelineContext;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.domain.model.ReportType;
import com.stpl.tech.kettle.domain.model.RevenueCertificateResponse;
import com.stpl.tech.kettle.reports.process.RevenueCertificateTemplate2;
import com.stpl.tech.kettle.service.process.HeaderFooterPageEvents;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.amazonaws.util.IOUtils;
import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfCopy;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.stpl.tech.kettle.core.data.vo.RevenueCertificateData;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.reports.process.RevenueCertificateNotification;
import com.stpl.tech.kettle.reports.process.RevenueCertificateTemplate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AttachmentData;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + REPORT_METADATA_ROOT_CONTEXT)
public class ReportExecutionResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(ReportExecutionResources.class);

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private PosMetadataService posMetadataService;

	@Autowired
	private ExpenseReportResource expenseReportResource;

	@Autowired
	private ReportingService reportingService;

	@Autowired
	private MasterDataCache masterDataCache;

	@RequestMapping(value = "report/expense/execute/wow", method = RequestMethod.GET)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean executeWOWExpenseReport() throws Exception {
		LOG.info("Request for Google Report Service");
		try {
			expenseReportResource.generateUnitExpenseReport(IterationType.WOW, AppUtils.getCurrentDate(),
					"Requested By Admin Panel");
			return true;
		} catch (Exception e) {
			LOG.error("Error while google reporting", e);
			throw e;
		}
	}

	@RequestMapping(value = "report/expense/execute/mom", method = RequestMethod.GET)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean executeMOMExpenseReport() throws Exception {
		LOG.info("Request for Google Report Service");
		try {
			expenseReportResource.generateUnitExpenseReport(IterationType.MOM, AppUtils.getCurrentDate(),
					"Requested By Admin Panel");
			return true;
		} catch (Exception e) {
			LOG.error("Error while google reporting", e);
			throw e;
		}
	}

	@Scheduled(cron = "0 30 9 1 * *", zone = "GMT+05:30")
	@RequestMapping(value = "report/email",method = RequestMethod.GET)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void monthlyRevenueCertificate() {
		try {
			//if (!AppUtils.isDev(props.getEnvironmentType())) {
				Date d = AppUtils.getCurrentTimestamp();
				int month = AppUtils.getMonth(d);
				int year = AppUtils.getYear(d);
				if (month == 1) {
					month = 12;
					year = year - 1;
				} else {
					month = month - 1;
				}
//				sendCronMail(month, year, "Gross");
//				sendCronMail(month, year, "DineInDelivery");
				sendCronMail(month,year);
			//}
		} catch (Exception e) {
			new ErrorNotification("Revenue Certificate Generation Failure", e.getMessage(), e,
					props.getEnvironmentType()).sendEmail();
		}
	}



	public void sendCronMail(int month, int year) throws Exception {
		try {
			String[] revenueCertificateEmails = {"<EMAIL>"};
			List<Integer> exclusionList = new ArrayList<>(Arrays.asList(26255));
			List<RevenueCertificateData> list = reportingService.getRevenueCertificateData(month, year,null,exclusionList);
			List<RevenueCertificateData> list2 = reportingService.getRevenueCertificateData(month, year,26255,new ArrayList<Integer>());

			Map<Integer, List<RevenueCertificateData>> revenueCertificateDataMap = new HashMap<>();
			List<RevenueCertificateData> revenueCertificateDataList = null;
			for (RevenueCertificateData revenueCertificateData : list) {
				revenueCertificateDataList = revenueCertificateDataMap.get(revenueCertificateData.getUnitId());
				if (Objects.isNull(revenueCertificateDataList)) {
					revenueCertificateDataList = new ArrayList<>();
					revenueCertificateDataList.add(revenueCertificateData);
					revenueCertificateDataMap.put(revenueCertificateData.getUnitId(), revenueCertificateDataList);
				} else {
					revenueCertificateDataList.add(revenueCertificateData);
				}
			}
			for (RevenueCertificateData revenueCertificateData : list2) {
				revenueCertificateDataList = revenueCertificateDataMap.get(revenueCertificateData.getUnitId());
				if (Objects.isNull(revenueCertificateDataList)) {
					revenueCertificateDataList = new ArrayList<>();
					revenueCertificateDataList.add(revenueCertificateData);
					revenueCertificateDataMap.put(revenueCertificateData.getUnitId(), revenueCertificateDataList);
				} else {
					revenueCertificateDataList.add(revenueCertificateData);
				}
			}
			String path = props.getBasePath() + "/" + "zipFiles/";
			File pathFolder = new File(path);

			if (!pathFolder.exists()) {
				if (pathFolder.mkdirs()) {
					LOG.info("Directory Created {}", path);
				} else {
					LOG.error("error creating directory {}", path);
				}
			}
			File tempZipFile = new File(path + "Revenue_Certificates.zip");
			if (tempZipFile.createNewFile()) {
				LOG.info("file Created {}", tempZipFile.getName());
			} else {
				LOG.error("error creating file {}", tempZipFile.getName());
			}
			FileOutputStream fos = new FileOutputStream(tempZipFile);
			ZipOutputStream zipOut = new ZipOutputStream(fos);
			for (Map.Entry<Integer, List<RevenueCertificateData>> ele : revenueCertificateDataMap.entrySet()) {
				Integer unitId = (Integer) ele.getKey();
				List<RevenueCertificateData> value = ele.getValue();
				String filePath = props.getBasePath() + File.separator + "revenueCertificate" + File.separator + unitId + "_" + year + "_"
						+ month + "_"  + "_certificate.pdf";

				RevenueCertificateTemplate2 template = null;
				Document document = new Document(PageSize.A4);
				File file = new File(filePath);
				if (!file.exists()) {
					file.getParentFile().mkdirs();
				}
				PdfCopy copy = new PdfCopy(document, new FileOutputStream(filePath));
				document.open();
				Map<Integer, PdfReader> obj = new HashMap<>();
				UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(unitId);
				String unitName = "Cafe";
				if (ubd != null) {
					unitName = ubd.getName();
				}
				obj.put(unitId, null);
//				if (type.equals("Gross")) {
//					getRevenueCertificateTemplate(obj.get(unitId), copy, template, value, month, year, unitName, false);
//				} else if (type.equals("DineInDelivery")) {
//					getRevenueCertificateTemplate(obj.get(unitId), copy, template, value, month, year, unitName, true);
//				}
				getNewRevenueCertificateTemplate(obj.get(unitId), copy, template, value, month, year, unitName);

				document.close();

				//zip converter code
				File fileToZip = new File(String.valueOf(file));
				FileInputStream fis = new FileInputStream(fileToZip);
				ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
				zipOut.putNextEntry(zipEntry);
				byte[] bytes = new byte[1024];
				int length;
				while ((length = fis.read(bytes)) >= 0) {
					zipOut.write(bytes, 0, length);
				}
				fis.close();
			}
			zipOut.close();
			fos.close();

			// zip uploading to s3
			File file = new File(String.valueOf(tempZipFile));
			MultipartFile multipartFile = convertFileToMultipartFile(file);
			FileDetail s3File = posMetadataService.saveRevenueCertificate(null, null, null, multipartFile, props.getS3ReportBucket(), null);
			String revenueCertificateUrl = s3File.getUrl().substring(0, 4) + s3File.getUrl().substring(5);
			RevenueCertificateNotification notification = new RevenueCertificateNotification(props.getEnvironmentType(),
					month, year, revenueCertificateEmails, null, "cron");
			notification.setRevenueCertificateUrl(revenueCertificateUrl);
			notification.sendEmail();
		} catch (Exception e) {
			LOG.error("Error while sending mail of revenue certificate zip url", e);
			throw e;
		}
	}

	@RequestMapping(value = "report/revenueCertificate", method = RequestMethod.GET)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void generateRevenueCertificate(HttpServletResponse response, @RequestParam int month, @RequestParam int year,
										   @RequestParam(required = false) String type ,@RequestParam(required = false) String reportType ,@RequestParam(required = false) Integer unitId) throws Exception {
		LOG.info("Request to generate revenue certificate");
		try {
			List<Integer> exclusionList = new ArrayList<>(Arrays.asList(26255));
	//		if(!exclusionList.contains(unitId)){
			List<RevenueCertificateData> list = reportingService.getRevenueCertificateData(month, year,unitId,exclusionList);
//			}else {
//				List<RevenueCertificateData> list2 = reportingService.getRevenueCertificateData(month, year, 26255, new ArrayList<Integer>());
//			}
			Map<Integer, List<RevenueCertificateData>> revenueCertificateDataMap = new HashMap<>();
			List<RevenueCertificateData> revenueCertificateDataList = null;
			for (RevenueCertificateData revenueCertificateData : list) {
				revenueCertificateDataList = revenueCertificateDataMap.get(revenueCertificateData.getUnitId());
				if (revenueCertificateDataList == null) {
					revenueCertificateDataList = new ArrayList<>();
					revenueCertificateDataList.add(revenueCertificateData);
					revenueCertificateDataMap.put(revenueCertificateData.getUnitId(), revenueCertificateDataList);
				} else {
					revenueCertificateDataList.add(revenueCertificateData);
				}
			}
//			for (RevenueCertificateData revenueCertificateData : list2) {
//				revenueCertificateDataList = revenueCertificateDataMap.get(revenueCertificateData.getUnitId());
//				if (revenueCertificateDataList == null) {
//					revenueCertificateDataList = new ArrayList<>();
//					revenueCertificateDataList.add(revenueCertificateData);
//					revenueCertificateDataMap.put(revenueCertificateData.getUnitId(), revenueCertificateDataList);
//				} else {
//					revenueCertificateDataList.add(revenueCertificateData);
//				}
//			}
			String path = props.getBasePath() + "/" + "zipFiles/";
			File pathFolder = new File(path);
			if (!pathFolder.exists()) {
				pathFolder.mkdirs();
			}
			certificateGeneration(response, month, year, type, path, revenueCertificateDataMap,reportType);
		} catch (Exception e) {
			LOG.error("Error while generating revenue certificate", e);
			throw e;
		}
	}

	public boolean certificateGeneration(HttpServletResponse response, int month, int year, String type, String path, Map<Integer, List<RevenueCertificateData>> revenueCertificateDataMap,String reportType) throws Exception {
		try {
			File tempZipFile = new File(path);
			boolean isConsildated = true;
			if(type!=null || reportType.equals(ReportType.SEGREGATE.toString())){
				isConsildated = false;
			} else if (reportType.equals(ReportType.CONSOLIDATED.toString()) || reportType == null) {
				isConsildated = true;
			}
			if(!isConsildated) {
				tempZipFile = new File(path + type + "_Revenue_Certificates.zip");
			}
			else if(isConsildated){
				tempZipFile = new File(path + "Revenue_Certificates");
			}

			tempZipFile.createNewFile();
			FileOutputStream fos = new FileOutputStream(tempZipFile);
			ZipOutputStream zipOut = new ZipOutputStream(fos);

			for (Map.Entry<Integer, List<RevenueCertificateData>> ele : revenueCertificateDataMap.entrySet()) {
				Integer unitId = (Integer) ele.getKey();
				List<RevenueCertificateData> value = ele.getValue();
				String filePath = null;
				if(!isConsildated) {
					filePath = props.getBasePath() + File.separator + "revenueCertificate" + File.separator + unitId + "_" + year + "_"
							+ month + "_" + type + "_certificate.pdf";
				}
				else if (isConsildated) {
					filePath = props.getBasePath() + File.separator + "revenueCertificate" + File.separator + unitId + "_" + year + "_"
							+ month + "_" + "_certificate.pdf";
				}
				RevenueCertificateTemplate template = null;
				RevenueCertificateTemplate2 template2 = null;
				Document document = new Document(PageSize.A4);
				File file = new File(filePath);
				if (!file.exists()) {
					file.getParentFile().mkdirs();
				}
				PdfCopy copy = new PdfCopy(document, new FileOutputStream(filePath));
				document.open();
				Map<Integer, PdfReader> obj = new HashMap<>();
				UnitBasicDetail ubd = masterDataCache.getUnitBasicDetail(unitId);
				String unitName = "Cafe";
				if (ubd != null) {
					unitName = ubd.getName();
				}
				obj.put(unitId, null);
				if(!isConsildated) {
					if (type.equals("Gross")) {
						getRevenueCertificateTemplate(obj.get(unitId), copy, template, value, month, year, unitName, false);
					} else if (type.equals("DineInDelivery")) {
						getRevenueCertificateTemplate(obj.get(unitId), copy, template, value, month, year, unitName, true);
					}
				} else if (isConsildated) {
					getNewRevenueCertificateTemplate(obj.get(unitId), copy, template2, value, month, year, unitName);

				}
				document.close();

				//zip converter code
				File fileToZip = new File(String.valueOf(file));
				FileInputStream fis = new FileInputStream(fileToZip);
				ZipEntry zipEntry = new ZipEntry(fileToZip.getName());
				zipOut.putNextEntry(zipEntry);
				byte[] bytes = new byte[1024];
				int length;
				while ((length = fis.read(bytes)) >= 0) {
					zipOut.write(bytes, 0, length);
				}
				fis.close();
			}
			zipOut.close();
			fos.close();
			downLoadZipFile(response, tempZipFile, month, year);
			return true;
		} catch (Exception e) {
			LOG.error("Error while generating certificate", e);
			throw e;
		}
	}

	public void downLoadZipFile(HttpServletResponse response, @RequestParam File tempZipFile, @RequestParam int month, @RequestParam int year)
			throws IOException {
		if (tempZipFile != null) {
			response.setContentType(AppConstants.ZIP_MIME_TYPE);
			response.addHeader("Content-Disposition", "attachment; filename=" + tempZipFile.getName() + ".zip");
			byte[] bytesArray = new byte[(int) tempZipFile.length()];
			response.setContentLength(bytesArray.length);
			try {
				OutputStream outputStream = response.getOutputStream();
				InputStream inputStream = new FileInputStream(tempZipFile);
				int counter = 0;
				while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
					outputStream.write(bytesArray, 0, counter);
					outputStream.flush();
				}
				outputStream.close();
				inputStream.close();
			} catch (IOException e) {
				LOG.error("Encountered error while writing file to response stream", e);
				throw e;
			} finally {
				response.getOutputStream().flush();
				if (tempZipFile.delete()) {// delete the temporary file created after completing request
					LOG.info("temporary file deleted {}", tempZipFile.getName());
				} else {
					LOG.error("error deleting file {}", tempZipFile.getName());
				}
			}
		}
	}

	@RequestMapping(value = "report/revenueCertificateMails", method = RequestMethod.POST, consumes = "multipart/form-data")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public RevenueCertificateResponse generateRevenueCertificateMails(@RequestParam(value = "file") MultipartFile file, @RequestParam(value = "month") int month, @RequestParam(value = "year") int year) {
		RevenueCertificateResponse response = new RevenueCertificateResponse();
		try {
			LOG.info("Request to send Revenue Certificate Mails {}", file.getOriginalFilename());
			List<AttachmentData> attachments = new ArrayList<>();
			AttachmentData revenueCertificateDetail = new AttachmentData();
			InputStream inputStream = file.getInputStream();
			revenueCertificateDetail.setAttachment(org.apache.commons.io.IOUtils.toByteArray(inputStream));
			String[] ccMails = {"<EMAIL>"};
			String[] bccMails = new String[0];
			response.setFileName(file.getOriginalFilename());
			revenueCertificateDetail.setFileName(file.getOriginalFilename());
			String unitIdString = file.getOriginalFilename().split("_")[0];
			revenueCertificateDetail.setContentType(AppConstants.PDF_MIME_TYPE);
			Integer unitId = Integer.valueOf(unitIdString);
			if (Objects.nonNull(unitId)) {
				Unit unit = masterDataCache.getUnit(unitId);
				if (Objects.isNull(unit)) {
					String message = "No data found for unitId " + unitId;
					response.setStatus(false);
					response.setMessage(message);
					LOG.info(message);
					return response;
				}
				String unitName = unit.getName();
				String revenueCertificateEmail = unit.getRevenueCertificateEmail();
//				boolean isRevenueCertificateGenerationEnable = unit.isRevenueCertificateGenerationEnable();
				String[] revenueCertificateEmails = new String[0];
				if (Objects.nonNull(revenueCertificateEmail)) {
					revenueCertificateEmails = revenueCertificateEmail.split(",");
				}
				RevenueCertificateNotification notification = new RevenueCertificateNotification(props.getEnvironmentType(),
						month, year, revenueCertificateEmails, unitName, "uploading");
				attachments.add(revenueCertificateDetail);
				notification.SendRawEmailWithCC(attachments, ccMails, bccMails);
				String message = "Mail sent successfully for unit " + unitId;
				LOG.info(message);
				response.setStatus(true);
				response.setMessage(message);
				return response;
			}
			String message = "No data found for unitId " + unitId;
			response.setStatus(false);
			response.setMessage(message);
			LOG.info(message);
			return response;
		} catch (Exception e) {
			LOG.error("Error while sending Revenue Certificate Mails", e);
			response.setStatus(false);
			response.setFileName(file.getOriginalFilename());
			response.setMessage(String.valueOf(e));
			return response;
		}
	}
	
	private MultipartFile convertFileToMultipartFile(File f) {
		FileInputStream input = null;
		try {
			input = new FileInputStream(f);
			return new MockMultipartFile("file",
					f.getName(), "text/plain", IOUtils.toByteArray(input));
		} catch (IOException e) {
			LOG.error("Error while converting file to multipartFile :: {}",e);
		}
		return null;
	}

	private void getRevenueCertificateTemplate(PdfReader reader , PdfCopy copy , RevenueCertificateTemplate template, List<RevenueCertificateData> certificates, Integer month, Integer year, String unitName, boolean isBreakdown) {
		template = new RevenueCertificateTemplate(props.getBasePath(), certificates, month, year, unitName,isBreakdown );

		HeaderFooterPageEvents event = null;
		try {
			event = new HeaderFooterPageEvents();

			Document document = new Document(PageSize.A4, 36,36,20+ event.getHeaderTableHeight(),36);
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			PdfWriter writer = PdfWriter.getInstance(document, baos);
			writer.setPageEvent(event);
			document.open();

			//				------------------------------New Code Starts --------------------------------------->
			CSSResolver cssResolver =
					XMLWorkerHelper.getInstance().getDefaultCssResolver(false);
			HtmlPipelineContext htmlContext = new HtmlPipelineContext(null);
			htmlContext.setTagFactory(Tags.getHtmlTagProcessorFactory());

			template.getContent();


			PdfWriterPipeline pdf = new PdfWriterPipeline(document, writer);
			HtmlPipeline html = new HtmlPipeline(htmlContext, pdf);
			CssResolverPipeline css = new CssResolverPipeline(cssResolver, html);

			XMLWorker worker = new XMLWorker(css, true);
			XMLParser p = new XMLParser(worker);
			p.parse(new FileInputStream(template.getFilepath()));

//				old line
//				XMLWorkerHelper.getInstance().parseXHtml(writer, document, new FileInputStream(template.getFilepath()));
			document.close();
			reader = new PdfReader(baos.toByteArray());
			copy.addDocument(reader);
			reader.close();
		} catch (DocumentException e) {
			LOG.error("Error generating document:", e);
		} catch (IOException | TemplateRenderingException e) {
			LOG.error("Exception while parsing template:", e);
		}

	}

	private void getNewRevenueCertificateTemplate(PdfReader reader , PdfCopy copy , RevenueCertificateTemplate2 template, List<RevenueCertificateData> certificates, Integer month, Integer year, String unitName) {
			template = new RevenueCertificateTemplate2(props.getBasePath(), certificates, month, year, unitName );
		HeaderFooterPageEvents event = null;
		try {
			event = new HeaderFooterPageEvents();

			Document document = new Document(PageSize.A4, 36,36,20+ event.getHeaderTableHeight(),36);
			ByteArrayOutputStream baos = new ByteArrayOutputStream();
			PdfWriter writer = PdfWriter.getInstance(document, baos);
			writer.setPageEvent(event);
			document.open();

			//				------------------------------New Code Starts --------------------------------------->
			CSSResolver cssResolver =
					XMLWorkerHelper.getInstance().getDefaultCssResolver(true);
			HtmlPipelineContext htmlContext = new HtmlPipelineContext(null);
			htmlContext.setTagFactory(Tags.getHtmlTagProcessorFactory());

			template.getContent();


			PdfWriterPipeline pdf = new PdfWriterPipeline(document, writer);
			HtmlPipeline html = new HtmlPipeline(htmlContext, pdf);
			CssResolverPipeline css = new CssResolverPipeline(cssResolver, html);

			XMLWorker worker = new XMLWorker(css, true);
			XMLParser p = new XMLParser(worker);
			p.parse(new FileInputStream(template.getFilepath()));

//				old line
//				XMLWorkerHelper.getInstance().parseXHtml(writer, document, new FileInputStream(template.getFilepath()));
			document.close();
			reader = new PdfReader(baos.toByteArray());
			copy.addDocument(reader);
			reader.close();
		} catch (DocumentException e) {
			LOG.error("Error generating document:", e);
		} catch (IOException | TemplateRenderingException e) {
			LOG.error("Exception while parsing template:", e);
		}

	}



}
