package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.core.service.GamifiedOfferService;
import com.stpl.tech.kettle.data.dao.GamifiedOfferDao;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.GameLeaderBoardResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferRequest;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import lombok.extern.slf4j.Slf4j;
import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.GAMIFIED_OFFER_RESOURCE;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.core.service.GamifiedOfferService;
import com.stpl.tech.kettle.data.dao.GamifiedOfferDao;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.GameLeaderBoardResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferRequest;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + GAMIFIED_OFFER_RESOURCE)
@Slf4j
public class GamifiedOfferResource extends AbstractResources {

    @Autowired
    private GamifiedOfferService gamifiedOfferService;

    @Autowired
    private GamifiedOfferDao gamifiedOfferDao;

    @Autowired
    private DroolsDecisionService droolsOfferDecisionService;

    @Autowired
    private CSRFTokenService csrftokenService;

    @PostMapping(value = "offer-via-games", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<GamifiedOfferResponse> getOfferByGames(HttpServletRequest httpServletRequest, @RequestBody GamifiedOfferRequest request) {
        log.info("Request to create offer for request data : {}", JSONSerializer.toJSON(request));
        try {
            validateRequest(request.getAuthToken(), true);
            Integer unitId = getLoggedInUnit(httpServletRequest);
            request.setUnitId(unitId);
            return gamifiedOfferService.getGamifiedOffer(request,null);
        } catch (Exception e) {
            log.error("Error while creating offer for campaign token {} and contact number {}",
                    request.getCampaignToken(), request.getContactNumber(), e);
        }
        return new ArrayList<>();
    }

    @PostMapping(value = "offer-via-games-wa", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public GamifiedOfferResponse getOfferByGamesForWhatsApp(@RequestParam String contactNumber, @RequestParam String campaignToken,
                                                            @RequestParam String utmSource, @RequestParam String utmedium) {
        GamifiedOfferRequest request = new GamifiedOfferRequest();
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        request.setCampaignToken(campaignToken);
        request.setContactNumber(contactNumber);
        request.setUtmMedium(utmedium);
        request.setUtmSource(utmSource);
        log.info("Request to create offer for request data : {}", JSONSerializer.toJSON(request));
        try {
            return gamifiedOfferService.getGamifiedOffer(request,null).get(0);
        } catch (Exception e) {
            log.info("Error while creating offer for campaign token {} and contact number {}",
                    request.getCampaignToken(), request.getContactNumber(), e);
        }
        return new GamifiedOfferResponse();
    }

    @GetMapping(value = "get-leader-board", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public GameLeaderBoardResponse getLeaderBoard(@RequestParam String contact, @RequestParam String token, @RequestParam Boolean getRank) {
        log.info("Request to get leader board for contactNumber : {}, token :{}", contact, token);
        try {
            return gamifiedOfferService.getLeaderBoard(contact, token, getRank);
        } catch (Exception e) {
            log.info("Error while fetching leader board for contact number {}, token :{}", contact, token, e);
        }
        return new GameLeaderBoardResponse();
    }

    @GetMapping(value = "get-offer-string", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getOfferString(HttpServletRequest httpServletRequest, @RequestParam Integer customerId, @RequestParam boolean isPosOffer) throws IOException {
        DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(customerId, 1);
        log.info("Drools Customer properties for customer id  : {} is {}", customerId, JSONSerializer.toJSON(properties));
        return droolsOfferDecisionService.getOfferString(properties, isPosOffer,null,getLoggedInUnit(httpServletRequest)).getKey();
    }

    @PostMapping(value = "upload/local", consumes = MediaType.MULTIPART_FORM_DATA)
    public ResponseEntity<Object> uploadFile(@RequestBody MultipartFile file) {
        try {
            ClassLoader classLoader = getClass().getClassLoader();
            URL url = classLoader.getResource("drools/offers.xls");
            File path = new File(new URI(url.toString()));
            path.createNewFile();
            FileOutputStream output = new FileOutputStream(path);
            output.write(file.getBytes());
            output.close();
            return ResponseEntity.accepted().body("File is uploaded successfully1!");
        } catch (Exception e) {
            return ResponseEntity.accepted().body("File is uploaded successfully2!");
        }
    }

    private void validateRequest(String token, boolean removeToken)
            throws AuthenticationFailureException {
        if (csrftokenService.contains(token)) {
            log.info("Found Token - " + token);
            if (removeToken) {
                removeToken(token);
            }
            return;
        } else {
            log.info("Not Found Token - " + token);
        }
        throw new AuthenticationFailureException("Invalid request");
    }

    public void removeToken(String token) {
        try {
            if (token != null) {
                csrftokenService.remove(token);
            }
        } catch (Exception e) {
            log.error("Error Removing Token", e);
        }
    }


    @PostMapping(value = "offer-via-drools", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<GamifiedOfferResponse> getOfferViaDrools(@RequestBody GamifiedOfferRequest request) {
        log.info("Request to create offer for request data : {}", JSONSerializer.toJSON(request));
        try {
            validateRequest(request.getAuthToken(), true);
            return gamifiedOfferService.getOfferViaDrools(request);
        } catch (Exception e) {
            log.info("Error while creating offer for campaign token {} and contact number {}",
                    request.getCampaignToken(), request.getContactNumber(), e);
        }
        return new ArrayList<>();
    }

    @PostMapping(value = "offer-via-scratch-card", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<GamifiedOfferResponse> getOfferByScratchCard(@RequestBody GamifiedOfferRequest request) {
        log.info("Request to create offer for request data : {}", JSONSerializer.toJSON(request));
        try {
            validateRequest(request.getAuthToken(), true);
            return gamifiedOfferService.getGamifiedOffer(request, AppConstants.SCRATCH_CARD);
        } catch (Exception e) {
            log.error("Error while creating offer for campaign token {} and contact number {}",
                    request.getCampaignToken(), request.getContactNumber(), e);
        }
        return new ArrayList<>();
    }

}
