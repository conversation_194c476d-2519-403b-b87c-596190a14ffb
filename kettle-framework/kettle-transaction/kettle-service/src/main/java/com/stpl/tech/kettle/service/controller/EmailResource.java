/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.notification.CharityNotification;
import com.stpl.tech.kettle.core.notification.CharityReceipt;
import com.stpl.tech.kettle.core.notification.DeliveryNotification;
import com.stpl.tech.kettle.core.notification.DeliveryNotificationReceipt;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.OrderEmailReceipt;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.ReceiptNotification;
import com.stpl.tech.kettle.core.notification.SubscriptionInfo;
import com.stpl.tech.kettle.core.notification.SubscriptionNotification;
import com.stpl.tech.kettle.core.notification.SubscriptionReceipt;
import com.stpl.tech.kettle.core.notification.VerificationEmailNotification;
import com.stpl.tech.kettle.core.notification.VerificationTemplate;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.SubscriptionManagementService;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.impl.CustomerDaoImpl;
import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.RandomStringGenerator;

@Component
public class EmailResource {

    @Autowired
    private OrderManagementService orderService;

    @Autowired
    private OrderSearchService orderSearchService;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private AuthorizationService authorizationService;

    @Autowired
    private MasterDataCache cache;

    @Autowired
    private OrderInfoCache orderCache;

    @Autowired
    private SubscriptionManagementService subscriptionService;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private NotificationService notificationService;
    @Autowired
    protected CustomerService customerService;

    @Autowired
    protected CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    private CustomerDao customerDao;

    private static final RandomStringGenerator generator = new RandomStringGenerator();

    private static final Logger LOG = LoggerFactory.getLogger(EmailResource.class);

    @Scheduled(fixedRate = 30000)
	public void sendReceiptEmails() {
		List<OrderEmailNotification> list = orderSearchService.getEmailEvents();
		if (list != null && !list.isEmpty()) {
			LOG.info("Found " + list.size() + " emails to be delivered in the queue");
		}
		for (OrderEmailNotification detail : list) {
            Brand brand;
            if(Objects.nonNull(detail.getBrandId())){
                brand=cache.getBrandMetaData().get(detail.getBrandId());
            }else {
                brand=cache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
            }
            if (detail.getEntryType().equals(OrderEmailEntryType.DISPATCH_DELAY.name())
					|| detail.getEntryType().equals(OrderEmailEntryType.DELIVERY_DELAY.name())) {
				sendDeliveryNotification(detail);
			} else if (detail.getEntryType().equals(OrderEmailEntryType.CHARITY_ORDER.name())) {
				sendCharityOrderNotification(detail);
			} else if (detail.getEntryType().equals(OrderEmailEntryType.VERIFICATION.name())) {
				try {
					sendVerificationNotification(detail, brand.getVerificationEmailTemplate());
				} catch (Exception e) {
					LOG.error("Encountered error while sending email to :::::: {}", detail.getEmailAddress(), e);
				}
			}
		}
	}

    private String getAuthorizationToken(OrderEmailNotification detail, String contactNumber)
        throws AuthenticationFailureException, UnsupportedEncodingException {
        authorizationService.expireAuthorizationRequest(detail.getEmailAddress());
        String token = generator.getRandonNumber(10);
        String decryptedString = contactNumber + "|" + detail.getEmailAddress() + "|" + token;
        String generatedToken = PasswordImpl.encrypt(decryptedString);
        generatedToken = URLEncoder.encode(generatedToken, "UTF-8");
        authorizationService.createEmailAuthorizationRequest(detail.getEmailAddress(), token, generatedToken);
        return generatedToken;
    }

	private void sendVerificationNotification(OrderEmailNotification detail, String verificationTemplate)
			throws AuthenticationFailureException, UnsupportedEncodingException, DataNotFoundException {
		if (!AppConstants.getValue(detail.getIsEmailVerified())) {
			String generatedToken = getAuthorizationToken(detail, detail.getContact());
            String fromEmail,recieptEmail;
            if(AppConstants.DOHFUL_BRAND_ID.equals(detail.getBrandId())){
                fromEmail =(AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                        + AppConstants.DOHFUL_RECEIPT;
                recieptEmail=props.getRecieptEmailDohful();
            }
            else{
                fromEmail=(AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                        + AppConstants.CHAAYOS_RECEIPT;
                recieptEmail=props.getRecieptEmail();
            }
			VerificationTemplate template = new VerificationTemplate(props.getBasePath(), props.getVerifyEmailPath(),
					generatedToken, verificationTemplate);
			VerificationEmailNotification notification = new VerificationEmailNotification(template,
					props.getEnvironmentType(), detail.getEmailAddress(), detail.getCustomerName(),
                    AppUtils.getFormattedEmail(fromEmail, recieptEmail), detail.getBrandId());
			try {
				notification.sendEmail();
				orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
						"Successfully Delivered the email");
			} catch (EmailGenerationException e) {
				LOG.error("Encountered error while generating email for :::: {}", detail.getEmailAddress(), e);
				orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
				sendFailureNotification(detail, e);
			}

		}

	}

    @Scheduled(fixedRate = 30000)
	public void createDelayDispatchNotificationEvents() {
		if (!props.getDispatchDelayEmailTrigger()) {
			return;
		}
		try {
			for (Map<UnitCategory, Set<Integer>> unitOrders : orderCache.getOrderByCategoryCache().values()) {
				if (unitOrders != null && unitOrders.size() > 0) {
					for (Set<Integer> categoryOrders : unitOrders.values()) {
						if (categoryOrders != null && categoryOrders.size() > 0) {
							for (Integer orderId : categoryOrders) {
								OrderInfo order = orderCache.getOrder(orderId);
								if (order != null && TransactionUtils.isCODOrder(order.getOrder().getSource())
										&& order.getOrder()
												.getDeliveryPartner() != TransactionConstants.DELIVERY_PARTNER_PICKUP) {
									if (OrderInfoCache.unsettledOrderStaus.contains(order.getOrder().getStatus())
											&& !orderCache.checkEmailIfDelivered(order.getOrder().getOrderId(),
													OrderEmailEntryType.DISPATCH_DELAY)) {
										int seconds = AppUtils.getSecondsDiff(order.getOrder().getBillCreationTime(),
												AppUtils.getCurrentTimestamp());
										if (seconds >= props.getDispactDelayEmailTriggerTimeGap()) {
											LOG.info("Adding dispatch delay notification for order with id "
													+ order.getOrder().getOrderId());
											Unit detail = cache.getUnit(order.getUnit().getId());
											orderService.generateOrderEmailEvent(OrderEmailEntryType.DISPATCH_DELAY,
													order.getOrder().getOrderId(), 1,
													getEmail(detail.getUnitEmail(), detail.getManagerEmail()), true,
													true, AppUtils.getCurrentTimestamp());
											orderCache.addEmailDetail(order.getOrder().getOrderId(),
													OrderEmailEntryType.DISPATCH_DELAY);
										}
									}
								}
							}
						}
					}
				}
			}
		} catch (Exception e) {
			LOG.error("Error while creating Dispatch Delay Event", e);
		}
	}

    @Scheduled(fixedRate = 30000)
    public void createDelayDeliveryNotificationEvents() {
        if (!props.getDeliveryDelayEmailTrigger() && !props.getDeliveryDelaySlackTrigger()) {
            return;
        }
        try {
            for (Map<UnitCategory, Set<Integer>> unitOrders : orderCache.getOrderByCategoryCache().values()) {
                if (unitOrders != null && unitOrders.size() > 0) {
                    for (Set<Integer> categoryOrders : unitOrders.values()) {
                        if (categoryOrders != null && categoryOrders.size() > 0) {
                            for (Integer orderId : categoryOrders) {
                            	OrderInfo order = orderCache.getOrder(orderId);
                                if (order != null && TransactionUtils.isCODOrder(order.getOrder().getSource()) && order.getOrder()
                                    .getDeliveryPartner() != TransactionConstants.DELIVERY_PARTNER_PICKUP) {
                                    if (OrderInfoCache.undeliveredOrderStaus.contains(order.getOrder().getStatus())
                                        && !orderCache.checkEmailIfDelivered(order.getOrder().getOrderId(),
                                        OrderEmailEntryType.DELIVERY_DELAY)) {
                                        int seconds = AppUtils.getSecondsDiff(order.getOrder().getBillCreationTime(),
                                            AppUtils.getCurrentTimestamp());
                                        if (order.getOrder().getOrders().size() < 6) {
                                            if (seconds >= props.getDeliveryDelayEmailTriggerTimeGap()) {
                                                LOG.info("Adding delivery delay notification for order with id "
                                                    + order.getOrder().getOrderId());
                                                generateEmailEvent(order);
                                            }
                                        } else {
                                            if (seconds >= props.getBulkDeliveryDelayEmailTriggerTimeGap()) {
                                                LOG.info("Adding bulk delivery delay notification for order with id "
                                                    + order.getOrder().getOrderId());
                                                generateEmailEvent(order);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Error while creating Delivery Delay Event", e);
        }
    }

    private void generateEmailEvent(OrderInfo order) {
        Unit detail = cache.getUnit(order.getUnit().getId());
        orderService.generateOrderEmailEvent(OrderEmailEntryType.DELIVERY_DELAY, order.getOrder().getOrderId(), 1,
            getEmail(detail.getUnitEmail(), detail.getManagerEmail()), true, true, AppUtils.getCurrentTimestamp());
        orderCache.addEmailDetail(order.getOrder().getOrderId(), OrderEmailEntryType.DELIVERY_DELAY);
    }

    private String getEmail(String email1, String email2) {
        return AppUtils.isProd(props.getEnvironmentType())
            ? email2 == null || !email2.endsWith("chaayos.com") ? email1 : email1 + "," + email2
            : "<EMAIL>";
    }

	public Boolean sendReceiptNotification(OrderEmailNotification detail) {
		String generatedToken = null;
		try {
			Integer customerId =  orderSearchService.getCustomerId(detail.getOrderId());
			long startTime = System.currentTimeMillis();
            if (detail.getEntryType().equals(OrderEmailEntryType.ORDER.name()) && !AppUtils.isValidEmail(detail.getEmailAddress())) {
                customerService.removeIncorrectEmailInfo(customerId);
                orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.INCORRECT_MAIL, "WRONG Mail address");
                return false;
            }
            if (detail.getEntryType().equals(OrderEmailEntryType.ORDER_CANCELLATION.name())) {
                String[] emailAddressList = detail.getEmailAddress().split(",");
                boolean emailFlag = false;
                for (String emailId : emailAddressList) {
                    if (!AppUtils.isValidEmail(emailId)) {
                        emailFlag = true;
                        break;
                    }
                }
                if (emailFlag) {
                    orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.INCORRECT_MAIL, "WRONG Mail address");
                    return false;
                }
            }
			if(AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerId)) {
                orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.NOT_TO_BE_DELIVERED,
                        "EMAIL NOT BE SEND");
                orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                        "Successfully Delivered the email");
                return false;
			}
			OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);

			if (!AppConstants.getValue(detail.getIsEmailVerified())) {
				if (orderInfo.getOrder().getCustomerId() > 5) {
					Customer customer = orderInfo.getCustomer();
					generatedToken = authorizationService.findAuthorizationToken(customer.getEmailId());
					if (generatedToken == null) {
						generatedToken = getAuthorizationToken(detail, customer.getContactNumber());
					}
				}
			}
			boolean cancellation = OrderEmailEntryType.ORDER_CANCELLATION.name().equals(detail.getEntryType());
			boolean isEmailVerified = AppConstants.getValue(detail.getIsEmailVerified());
			String verifyEmailLink = generatedToken == null || cancellation ? null : props.getVerifyEmailPath();
			String fromEmail = (AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + AppConstants.CHAAYOS_RECEIPT : "")
					+ AppConstants.CHAAYOS_RECEIPT;
			Unit unit = cache.getUnit(orderInfo.getOrder().getUnitId());

			String toEmail = cancellation ? detail.getEmailAddress() + "," + props.getRecieptEmail()
					: props.getRecieptEmail();
			CustomerEmailData customerEmailData = customerOfferManagementService
					.getCustomerEmailData(orderInfo.getCustomer().getId(), orderInfo.getOrder().getBrandId());
            LOG.info("Email From : {} ",AppUtils.getFormattedEmail(fromEmail, props.getRecieptEmail()));
			ReceiptNotification notification = new ReceiptNotification(
					new OrderEmailReceipt(props.getChaayosBaseUrl(), unit, orderInfo,
							AppUtils.getFormattedEmail(fromEmail, props.getRecieptEmail()), toEmail,
							props.getBasePath(), isEmailVerified, verifyEmailLink, generatedToken,
							props.getBillPromotion(), cancellation, customerEmailData),
					sendEmailToCustomer(orderInfo.getOrder()));
			notification.sendEmail();
			if (!cancellation && props.isKioskOrderPDFReceiptSMS() && orderInfo.getOrder().getCustomerId() > 5
					&& StringUtils.isNotBlank(orderInfo.getOrder().getSourceId())
					&& StringUtils.startsWith(orderInfo.getOrder().getSourceId(), "KSK")) {
				createReceiptPdf(notification.body(), orderInfo.getOrder().getGenerateOrderId(),
						orderInfo.getOrder().getCustomerName(), orderInfo.getCustomer().getContactNumber(), unit,
						orderInfo);
			}
			orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
					"Successfully Delivered the email");
			LOG.info("Email sent in {}: ", System.currentTimeMillis() - startTime);
		} catch (Exception e) {
			LOG.error("Error while sending email for order email productId " + detail.getOrderEmailId(), e);
			orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
			sendFailureNotification(detail, e);
			return false;
		}
		return true;
	}

    private void createReceiptPdf(String receiptContent, String orderId, String customerName, String contact,
                                  Unit unit, OrderInfo orderInfo) {
        try {
            if (StringUtils.isNotBlank(receiptContent) && StringUtils.isNotBlank(orderId)) {
                String kioskPath = props.getBasePath() + "/" + unit.getId() + "/kiosk/orders";
                File kioskFolder = new File(kioskPath);
                if (!kioskFolder.exists()) {
                    kioskFolder.mkdirs();
                }
                String fileName = "orderReceipt-" + orderId + ".pdf";
                String receiptPath = kioskPath + "/" + fileName;
                File pdfFile = new File(receiptPath);
                if (!pdfFile.exists()) {
                    pdfFile.createNewFile();
                }
                try (OutputStream outputStream = new FileOutputStream(pdfFile)) {
                    HtmlConverter.convertToPdf(receiptContent, outputStream);
                    outputStream.flush();

                    String baseDir = "kiosk/" + unit.getId() + "/orders";
                    try {
                        FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, pdfFile,
                            true);
                        if (s3File != null) {
                            String receiptLink = props.getReceiptDownloadBaseUrl() + "wcrt/or/" + orderId + "/dld";
                            sendOrderPlacedNotification(contact, customerName, receiptLink, orderId, unit.getName());
                        } else {
                            LOG.error("Error uploading report to S3.");
                        }
                        pdfFile.delete();
                    } catch (Exception e) {
                        LOG.error("Encountered error while uploading report to S3", e);
                        if(pdfFile.exists()) {
                            pdfFile.delete();
                        }
                    }

                } catch (IOException e) {
                    /*
                     * String errorMsg = "Unable to create receipt pdf "; LOG.error(errorMsg, e);
                     */
                    LOG.error("Exception Occurred while converting html to pdf");
                    if(pdfFile.exists()) {
                        pdfFile.delete();
                    }
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred while creating PDF of Receipt");
        }

    }

    public void sendOrderPlacedNotification(String customerNumber, String customerName, String receiptPDFUrl,
                                            String orderId, String unitName) {
        try {
            ShortUrlData shortUrl = SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(receiptPDFUrl);
            String message = String.format(
                "Dear %s, thank you for placing an order at Chaayos, %s. Download bill for #%s here %s",
                customerName, unitName, orderId, shortUrl.getUrl());
            notificationService.sendNotification("KIOSK_ORDER_PLACED", message, customerNumber,
                SolsInfiniWebServiceClient.getTransactionalClient(), true,null);
        } catch (Exception e) {
            LOG.error("Error while sending notification to the customer", e);
        }
    }

    private void sendFailureNotification(OrderEmailNotification detail, Exception e) {
        if (detail.getRetryCount() == props.getRetryCount()) {
            String errorMsg = "Unable to send email to Customer Email Id: " + detail.getEmailAddress()
                + " for Order Id: " + detail.getOrderId();
            new ErrorNotification("Receipt Email Failure", errorMsg, e, props.getEnvironmentType()).sendEmail();
        }
    }

    private boolean sendEmailToCustomer(Order order) {
        return order.getSubscriptionDetail() == null
            || (order.getSubscriptionDetail() != null && order.getSubscriptionDetail().isEmailNotification());
    }

    public Boolean sendSubscriptionReceiptNotification(OrderEmailNotification detail) {

        try {
            SubscriptionInfo orderInfo = subscriptionService.getSubscriptionInfo(detail.getOrderId());
            String fromEmail = (AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                + AppConstants.CHAAYOS_SUBSCRIPTION;
            SubscriptionNotification notification = new SubscriptionNotification(
                new SubscriptionReceipt(orderInfo, props.getBasePath(),
                    AppUtils.getFormattedEmail(fromEmail, props.getRecieptEmail()), props.getRecieptEmail()));
            notification.sendEmail();
            orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                "Successfully Delivered the subscription email");
        } catch (Exception e) {
            LOG.error("Error while sending email for subscription email productId " + detail.getOrderEmailId(), e);
            orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            sendFailureNotification(detail, e);
            return false;
        }
        return true;

    }

    public Boolean sendDeliveryNotification(OrderEmailNotification detail) {
        try {
            OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);
            String fromEmail = (AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                + AppConstants.CHAAYOS_DELIVERY;
            DeliveryNotification notification = new DeliveryNotification(
                OrderEmailEntryType.valueOf(detail.getEntryType()), props.getDeliverySupportEmailId(),
                props.getDeliverySupportSecondaryEmailId(),
                new DeliveryNotificationReceipt(orderInfo, props.getBasePath(),
                    AppUtils.getFormattedEmail(fromEmail, props.getDeliverySupportEmailId()),
                    detail.getEmailAddress(), OrderEmailEntryType.valueOf(detail.getEntryType())));
            if (props.getDeliveryDelayEmailTrigger()) {
                notification.sendEmail();
                orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                    "Successfully Delivered the delivery/dispatch delay email");
            }
            if (detail.getEntryType().equals(OrderEmailEntryType.DELIVERY_DELAY.name())
                && props.getDeliveryDelaySlackTrigger()) {
                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                    ApplicationName.KETTLE_SERVICE.name(), SlackNotification.DELAY_DELIVERY_CHANNEL,
                    String.valueOf(orderInfo.getUnit().getManagerId()), notification);
            }
        } catch (Exception e) {
            LOG.error("Error while sending delivery/dispatch email for order email id " + detail.getOrderEmailId(), e);
            orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            sendFailureNotification(detail, e);
            return false;
        }
        return true;
    }

    public Boolean sendCharityOrderNotification(OrderEmailNotification detail) {
        try {
            OrderInfo orderInfo = orderSearchService.getOrderReceipt(detail.getOrderId(), false, null);
            String fromEmail = (AppUtils.isDev(props.getEnvironmentType()) ? EnvType.DEV.name() + " " : "")
                + AppConstants.CHAAYOS_RECEIPT;
            CharityNotification notification = new CharityNotification(
                new CharityReceipt(orderInfo, props.getBasePath(),
                    AppUtils.getFormattedEmail(fromEmail, props.getRecieptEmail()), detail.getEmailAddress()));
            notification.sendEmail();
            orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.SUCCESS,
                "Successfully Delivered the charity order email");
        } catch (Exception e) {
            LOG.error("Error while sending email for charity order  " + detail.getOrderEmailId(), e);
            orderService.updateStatus(detail.getOrderEmailId(), EmailStatus.FAILED, e.getMessage());
            sendFailureNotification(detail, e);
            return false;
        }
        return true;
    }

    /*
     * // @Scheduled(fixedRate =120000)
     *
     * @Scheduled(cron = "0 0 9,16 * * *", zone = "GMT+05:30") public void
     * sendHourlyInventoryEmail() throws DataNotFoundException,
     * EmailGenerationException {
     * LOG.info("Sending stockout notification::::::::::::::"); if
     * (props.getSendStockOutNotifications()) {
     * sendInventoryMailByCategory(UnitCategory.CAFE,
     * InventoryThresholdType.STOCK_OUT);
     * sendInventoryMailByCategory(UnitCategory.DELIVERY,
     * InventoryThresholdType.STOCK_OUT); } }
     */

    /*
     * // @Scheduled(fixedRate =120000)
     *
     * @Scheduled(cron = "0 0 10 * * *", zone = "GMT+05:30") public void
     * sendDailyProbableStockOutInventoryEmail() throws DataNotFoundException,
     * EmailGenerationException {
     * LOG.info("Sending probable stockout notification::::::::::::::"); if
     * (props.getSendStockOutNotifications()) {
     * sendInventoryMailByCategory(UnitCategory.CAFE,
     * InventoryThresholdType.PROBABLE_STOCK_OUT);
     * sendInventoryMailByCategory(UnitCategory.DELIVERY,
     * InventoryThresholdType.PROBABLE_STOCK_OUT); } }
     */

    /*
     * private void sendInventoryMailByCategory(UnitCategory category,
     * InventoryThresholdType thresholdType) throws DataNotFoundException,
     * EmailGenerationException { Map<String, Set<ProductInventory>>
     * stockedOutInventoryList = unitManagement
     * .getStockedOutUnitInventoryByCategory(category, thresholdType); boolean
     * sendNotification = true; LOG.info("(sendNotification value::::::::::::::" +
     * sendNotification); if (sendNotification) { InventoryStockOutNotification
     * notification = new InventoryStockOutNotification(stockedOutInventoryList,
     * thresholdType, category, props.getBasePath()); InventoryStockOutEmail email =
     * new InventoryStockOutEmail(notification, props); email.sendEmail(); } }
     */
}
