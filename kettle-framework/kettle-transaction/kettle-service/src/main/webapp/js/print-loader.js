function loadQZPrinter(){

		qz.security.setCertificatePromise(function(resolve, reject) {

		/*var cetificate = "-----BEGIN CERTIFICATE-----\r\n"
				+ "MIIFDTCCAvegAwIBAgIBADALBgkqhkiG9w0BAQUwgZgxCzAJBgNVBAYTAlVTMQsw\r\n"
				+ "CQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0cmllcywgTExDMRswGQYDVQQL\r\n"
				+ "DBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMMEHF6aW5kdXN0cmllcy5jb20x\r\n"
				+ "JzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1c3RyaWVzLmNvbTAeFw0xNzA4\r\n"
				+ "MjAyMTAwMDBaFw0xODA4MjYyMTAwMDBaMIHbMQswCQYDVQQGDAJJTjESMBAGA1UE\r\n"
				+ "CAwJTmV3IERlbGhpMRIwEAYDVQQHDAlOZXcgRGVsaGkxKjAoBgNVBAoMIVN1bnNo\r\n"
				+ "aW5lIFRlYWhvdXNlIFByaXZhdGUgTGltaXRlZDEqMCgGA1UECwwhU3Vuc2hpbmUg\r\n"
				+ "VGVhaG91c2UgUHJpdmF0ZSBMaW1pdGVkMSowKAYDVQQDDCFTdW5zaGluZSBUZWFo\r\n"
				+ "b3VzZSBQcml2YXRlIExpbWl0ZWQxIDAeBgkqhkiG9w0BCQEMEW1vaGl0QGNoYWF5\r\n"
				+ "b3MuY29tMIIBIDALBgkqhkiG9w0BAQEDggEPADCCAQoCggEBAO1/q4r1qFrwoIDn\r\n"
				+ "BmkfIR9Hun7hoO6gI4d3ZY3Wttk/mYgsdVqM4iCPnHXD2Bd5RHfeR2+1euIBdBGt\r\n"
				+ "t+QODFT9dZW9Qm/TZ+pMxtDA77z06K6giNBzshdEiHZ6rLrUXqSF4DmCG83W7dtM\r\n"
				+ "B2Fpn/kUSV4xSazbE6AsDL5Zs0MPmoLUFk4YyKb0k/hKXN23I2vGt7xSVvwATHiz\r\n"
				+ "mtig8c1os9Ku9bo5pv9PNaJ1hV7QsWYpNtz5TtRT7i3KmxMUOsU2UNoTs7D0mEUe\r\n"
				+ "z3eS4ZOkXWeVK/Pf8aFQV1pFNiTSZdch6qLaknJNdslKALynoI6wb7Gzv9A+6oSV\r\n"
				+ "F5XD3f0CAwEAAaMjMCEwHwYDVR0jBBgwFoAUkKZQt4TUuepf8gWEE3hF6Kl1VFww\r\n"
				+ "CwYJKoZIhvcNAQEFA4ICAQBOsfeMV1PotUcJqxDhCh7bO56lCC7JTRnbN+l16x8/\r\n"
				+ "C1dQMNyR04ebGsdVu9e8K6SVrCGJOByGFqd/T8W2cStNValkabYhhUrL6/KqTHrY\r\n"
				+ "eDwnMsUOKfHZhZf/IDyldHus1JQBkxvHn0TwVDQOi/QUnawD3f1Cy8+zOxt+3hN5\r\n"
				+ "Za4xK92Rp4i2YCaJSBifZe2+gk5B8Tfy4zW5Jet4b0LXGwtKzHNCyzWZ9Ma2YgBf\r\n"
				+ "lXMV7ZK3/HwAvQkXGuumqwSTpXr3VIgKj5otOcepUTOpq8DJJ20zkL4VYuVwhuB1\r\n"
				+ "wz2yPIoPFTB8RoWOEoO+RBC6hz4rLAY3UEh553x2GyjEtRZ62J8TV96N6MvYpi1s\r\n"
				+ "4CwZtxLVqQbtmdlSVPEUy7AO42stz8sG0eqMO+r1F68HgR6u7J0qQhEqtjjg/8G1\r\n"
				+ "hzU2ePrjqBGKoIjAK/cH16LbORHG0UwtodTn36VRa6ahVqS6q5fK7USp0XBr4A6B\r\n"
				+ "hrydm2SGxYYIiJJLiRYljDI6rL3Aickx7+xtipy2/F8gFfTNi7vLqdWXTXNl3Cii\r\n"
				+ "LPnxtpxSnze5pH+sIC5OVcJUiNlZLxAVEAk4/+G1WSskSkRIUAvDiQq9JzGj6E9H\r\n"
				+ "HsV6HxO58YeWSSAUOhMSy3anw+9lTBQlxXEIybb9tcWu55pTX6p82LXPPDueW/lK\r\n" + "8g==\r\n"
				+ "-----END CERTIFICATE-----\r\n" + "--START INTERMEDIATE CERT--\r\n"
				+ "-----BEGIN CERTIFICATE-----\r\n"
				+ "MIIFEjCCA/qgAwIBAgICEAAwDQYJKoZIhvcNAQELBQAwgawxCzAJBgNVBAYTAlVT\r\n"
				+ "MQswCQYDVQQIDAJOWTESMBAGA1UEBwwJQ2FuYXN0b3RhMRswGQYDVQQKDBJRWiBJ\r\n"
				+ "bmR1c3RyaWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcG\r\n"
				+ "A1UEAwwQcXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBx\r\n"
				+ "emluZHVzdHJpZXMuY29tMB4XDTE1MDMwMjAwNTAxOFoXDTM1MDMwMjAwNTAxOFow\r\n"
				+ "gZgxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0\r\n"
				+ "cmllcywgTExDMRswGQYDVQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMM\r\n"
				+ "EHF6aW5kdXN0cmllcy5jb20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1\r\n"
				+ "c3RyaWVzLmNvbTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBANTDgNLU\r\n"
				+ "iohl/rQoZ2bTMHVEk1mA020LYhgfWjO0+GsLlbg5SvWVFWkv4ZgffuVRXLHrwz1H\r\n"
				+ "YpMyo+Zh8ksJF9ssJWCwQGO5ciM6dmoryyB0VZHGY1blewdMuxieXP7Kr6XD3GRM\r\n"
				+ "GAhEwTxjUzI3ksuRunX4IcnRXKYkg5pjs4nLEhXtIZWDLiXPUsyUAEq1U1qdL1AH\r\n"
				+ "EtdK/L3zLATnhPB6ZiM+HzNG4aAPynSA38fpeeZ4R0tINMpFThwNgGUsxYKsP9kh\r\n"
				+ "0gxGl8YHL6ZzC7BC8FXIB/0Wteng0+XLAVto56Pyxt7BdxtNVuVNNXgkCi9tMqVX\r\n"
				+ "xOk3oIvODDt0UoQUZ/umUuoMuOLekYUpZVk4utCqXXlB4mVfS5/zWB6nVxFX8Io1\r\n"
				+ "9FOiDLTwZVtBmzmeikzb6o1QLp9F2TAvlf8+DIGDOo0DpPQUtOUyLPCh5hBaDGFE\r\n"
				+ "ZhE56qPCBiQIc4T2klWX/80C5NZnd/tJNxjyUyk7bjdDzhzT10CGRAsqxAnsjvMD\r\n"
				+ "2KcMf3oXN4PNgyfpbfq2ipxJ1u777Gpbzyf0xoKwH9FYigmqfRH2N2pEdiYawKrX\r\n"
				+ "6pyXzGM4cvQ5X1Yxf2x/+xdTLdVaLnZgwrdqwFYmDejGAldXlYDl3jbBHVM1v+uY\r\n"
				+ "5ItGTjk+3vLrxmvGy5XFVG+8fF/xaVfo5TW5AgMBAAGjUDBOMB0GA1UdDgQWBBSQ\r\n"
				+ "plC3hNS56l/yBYQTeEXoqXVUXDAfBgNVHSMEGDAWgBQDRcZNwPqOqQvagw9BpW0S\r\n"
				+ "BkOpXjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAJIO8SiNr9jpLQ\r\n"
				+ "eUsFUmbueoxyI5L+P5eV92ceVOJ2tAlBA13vzF1NWlpSlrMmQcVUE/K4D01qtr0k\r\n"
				+ "gDs6LUHvj2XXLpyEogitbBgipkQpwCTJVfC9bWYBwEotC7Y8mVjjEV7uXAT71GKT\r\n"
				+ "x8XlB9maf+BTZGgyoulA5pTYJ++7s/xX9gzSWCa+eXGcjguBtYYXaAjjAqFGRAvu\r\n"
				+ "pz1yrDWcA6H94HeErJKUXBakS0Jm/V33JDuVXY+aZ8EQi2kV82aZbNdXll/R6iGw\r\n"
				+ "2ur4rDErnHsiphBgZB71C5FD4cdfSONTsYxmPmyUb5T+KLUouxZ9B0Wh28ucc1Lp\r\n" + "rbO7BnjW\r\n"
				+ "-----END CERTIFICATE-----";*/

		var cetificate = "-----BEGIN CERTIFICATE-----\n"
                         "MIIFXzCCA0egAwIBAgIQNzkyMDI0MDgyNzA3MzkxODANBgkqhkiG9w0BAQsFADCB\n"+
                         "mDELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAk5ZMRswGQYDVQQKDBJRWiBJbmR1c3Ry\n"+
                         "aWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcGA1UEAwwQ\n"+
                         "cXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBxemluZHVz\n"+
                         "dHJpZXMuY29tMB4XDTI0MDgyNzA3MzkxOFoXDTI1MDgyNzA4MzY0MFowggEZMQsw\n"+
                         "CQYDVQQGDAJJTjESMBAGA1UECAwJTmV3IERlbGhpMRIwEAYDVQQHDAlOZXcgRGVs\n"+
                         "aGkxKjAoBgNVBAoMIVN1bnNoaW5lIFRlYWhvdXNlIFByaXZhdGUgTGltaXRlZDEq\n"+
                         "MCgGA1UECwwhU3Vuc2hpbmUgVGVhaG91c2UgUHJpdmF0ZSBMaW1pdGVkMSowKAYD\n"+
                         "VQQDDCFTdW5zaGluZSBUZWFob3VzZSBQcml2YXRlIExpbWl0ZWQxIDAeBgkqhkiG\n"+
                         "9w0BCQEMEW1vaGl0QGNoYWF5b3MuY29tMTwwOgYDVQQNDDNyZW5ld2FsLW9mLWI0\n"+
                         "Y2Y3NGUxNmNhOGYzNmZhNGZlODc5OGQxMDA3NTIzNWVhMGNiNGUwggEgMAsGCSqG\n"+
                         "SIb3DQEBAQOCAQ8AMIIBCgKCAQEAzsYTcav+qBl6CaeENLQIWecqOFXBE4qiEnjp\n"+
                         "psnanLeYYkZgGOmCVsyjY/UItfOVqMvN/IK5+o6vgO4s5Aerf0gstgy7sVO6/MX1\n"+
                         "EYu9mX1xwpWjHuI6zLoXpgR4Lh7EE15Q6AFgy6wpnUl4MM2SUjqAUW39M4gDbQra\n"+
                         "ht1vmungQVNgKWjw9N6IQEZFXEpvUOo8rOwSg7Gs/jYCT5bckqjuNmf1yrDeI1CH\n"+
                         "a5Ttq64Z03pckaKYimuWJiB5Kxe5VLoQojiIJDiJmTBhGHVkVG1gmQjvbfxN9wE5\n"+
                         "uEe7phmnfC81lsHgrQDoAgUb2qe/WOrjhcKz//+HKsBp+CLbiQIDAQABoyMwITAf\n"+
                         "BgNVHSMEGDAWgBSQplC3hNS56l/yBYQTeEXoqXVUXDANBgkqhkiG9w0BAQsFAAOC\n"+
                         "AgEAoMXk1YAQG6msnI6FvaU/qJEtx66KWoA1YWsUrB86obORQfyII9XwTtluJuOc\n"+
                         "SQ/mWpdafzbdhAScu+OHGbib3UsVrimaNIiyY/VU5at5Qc5NE+U4gRuPdV4T6vzm\n"+
                         "oz78OgeVIJ018NlYgxdU3rK3P31849mgrrGLPiVFmpyJEvP2Wzgg/E12l/xBCObQ\n"+
                         "JMNgJqvt3vaO2LcPlf1y3WCttAA+HnuDmUvXGXC9pfRxR0U1QefRokWyKvHID/2p\n"+
                         "u9c2LsTkdEonNFJZO6JDqHQ2OZr9TtVbIr/OXn6l1Gn2AAN8wSMjg+O7H1snTxIw\n"+
                         "EK6bvLdPG/dQwWfQFJy52GSMzMJCuxpVyLSQxYUP6XvNot2wlXe1Yq/++IqUo1jk\n"+
                         "Yxwo/vr/gDrUmNurzxNkaWBRPpoBh1O2EHj/GNbYfvlD6UkK4v697l2VbRXwmKmw\n"+
                         "pCJE+tGRzpotSKbGnG4Kv4leeB1M7/Tco3lvRL82Q2feidgurRmhl+i+aP2Rykd6\n"+
                         "JsBlQ2QHlqvR91fzkmlLomMJzxHJlxvMmEy1mQARwrV4GbYvbliH3N82m31H2Xu5\n"+
                         "UgD9Adsc8P7pN8woV871aeCPgX2ar4E9jQU8gtz6OW1LKBVEoSTJSz2kAIYl8UcJ\n"+
                         "fl3yA1ZPHOiY9dwD8FISGPSjKWhjyVDLY9bP6LhFv+1+nAM=\n"+
                         "-----END CERTIFICATE-----\n"+
                         "--START INTERMEDIATE CERT--\n"+
                         "-----BEGIN CERTIFICATE-----\n"+
                         "MIIFEjCCA/qgAwIBAgICEAAwDQYJKoZIhvcNAQELBQAwgawxCzAJBgNVBAYTAlVT\n"+
                         "MQswCQYDVQQIDAJOWTESMBAGA1UEBwwJQ2FuYXN0b3RhMRswGQYDVQQKDBJRWiBJ\n"+
                         "bmR1c3RyaWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcG\n"+
                         "A1UEAwwQcXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBx\n"+
                         "emluZHVzdHJpZXMuY29tMB4XDTE1MDMwMjAwNTAxOFoXDTM1MDMwMjAwNTAxOFow\n"+
                         "gZgxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0\n"+
                         "cmllcywgTExDMRswGQYDVQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMM\n"+
                         "EHF6aW5kdXN0cmllcy5jb20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1\n"+
                         "c3RyaWVzLmNvbTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBANTDgNLU\n"+
                         "iohl/rQoZ2bTMHVEk1mA020LYhgfWjO0+GsLlbg5SvWVFWkv4ZgffuVRXLHrwz1H\n"+
                         "YpMyo+Zh8ksJF9ssJWCwQGO5ciM6dmoryyB0VZHGY1blewdMuxieXP7Kr6XD3GRM\n"+
                         "GAhEwTxjUzI3ksuRunX4IcnRXKYkg5pjs4nLEhXtIZWDLiXPUsyUAEq1U1qdL1AH\n"+
                         "EtdK/L3zLATnhPB6ZiM+HzNG4aAPynSA38fpeeZ4R0tINMpFThwNgGUsxYKsP9kh\n"+
                         "0gxGl8YHL6ZzC7BC8FXIB/0Wteng0+XLAVto56Pyxt7BdxtNVuVNNXgkCi9tMqVX\n"+
                         "xOk3oIvODDt0UoQUZ/umUuoMuOLekYUpZVk4utCqXXlB4mVfS5/zWB6nVxFX8Io1\n"+
                         "9FOiDLTwZVtBmzmeikzb6o1QLp9F2TAvlf8+DIGDOo0DpPQUtOUyLPCh5hBaDGFE\n"+
                         "ZhE56qPCBiQIc4T2klWX/80C5NZnd/tJNxjyUyk7bjdDzhzT10CGRAsqxAnsjvMD\n"+
                         "2KcMf3oXN4PNgyfpbfq2ipxJ1u777Gpbzyf0xoKwH9FYigmqfRH2N2pEdiYawKrX\n"+
                         "6pyXzGM4cvQ5X1Yxf2x/+xdTLdVaLnZgwrdqwFYmDejGAldXlYDl3jbBHVM1v+uY\n"+
                         "5ItGTjk+3vLrxmvGy5XFVG+8fF/xaVfo5TW5AgMBAAGjUDBOMB0GA1UdDgQWBBSQ\n"+
                         "plC3hNS56l/yBYQTeEXoqXVUXDAfBgNVHSMEGDAWgBQDRcZNwPqOqQvagw9BpW0S\n"+
                         "BkOpXjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAJIO8SiNr9jpLQ\n"+
                         "eUsFUmbueoxyI5L+P5eV92ceVOJ2tAlBA13vzF1NWlpSlrMmQcVUE/K4D01qtr0k\n"+
                         "gDs6LUHvj2XXLpyEogitbBgipkQpwCTJVfC9bWYBwEotC7Y8mVjjEV7uXAT71GKT\n"+
                         "x8XlB9maf+BTZGgyoulA5pTYJ++7s/xX9gzSWCa+eXGcjguBtYYXaAjjAqFGRAvu\n"+
                         "pz1yrDWcA6H94HeErJKUXBakS0Jm/V33JDuVXY+aZ8EQi2kV82aZbNdXll/R6iGw\n"+
                         "2ur4rDErnHsiphBgZB71C5FD4cdfSONTsYxmPmyUb5T+KLUouxZ9B0Wh28ucc1Lp\n"+
                         "rbO7BnjW\n"+
                         "-----END CERTIFICATE-----";

		resolve(cetificate);
	});

	/*var privateKey = "-----BEGIN PRIVATE KEY-----\r\n" +
	"MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQDtf6uK9aha8KCA\r\n" +
	"5wZpHyEfR7p+4aDuoCOHd2WN1rbZP5mILHVajOIgj5x1w9gXeUR33kdvtXriAXQR\r\n" +
	"rbfkDgxU/XWVvUJv02fqTMbQwO+89OiuoIjQc7IXRIh2eqy61F6kheA5ghvN1u3b\r\n" +
	"TAdhaZ/5FEleMUms2xOgLAy+WbNDD5qC1BZOGMim9JP4SlzdtyNrxre8Ulb8AEx4\r\n" +
	"s5rYoPHNaLPSrvW6Oab/TzWidYVe0LFmKTbc+U7UU+4typsTFDrFNlDaE7Ow9JhF\r\n" +
	"Hs93kuGTpF1nlSvz3/GhUFdaRTYk0mXXIeqi2pJyTXbJSgC8p6COsG+xs7/QPuqE\r\n" +
	"lReVw939AgMBAAECggEAWhYSYKxcBtq9VwfKw7ceHjpcAGLfY4R01Z6xc8LdKrzY\r\n" +
	"JPIQNZ+46nbu9zCpWAYUtY8n87SX/wBkIYyOcz/s55QYtQrMF5TOFu62f1qf1UHk\r\n" +
	"vNCB9OfdxrulM6PGvzttBuUNL2Mt3r9HC+bj6cxh9N4AEm2Jr7qN0DTFRLLLr4SR\r\n" +
	"fPSMmNdCHTiLtMRFVI3x3tGqOiNlQ1IY9HHtXaJmX//8bcobO9JglV1FTKXdkN/D\r\n" +
	"4w6G9d/cwCbz7qHSlO8l2IWkgnVJ7gTjRHT0BWDfHUV1ZNF43Kgms8wEiVtErabM\r\n" +
	"+oKoDiWdD6SS3iIkko9W/jptBiGOv7GKh90DIm+aVQKBgQD/svtMYtj9JWoSKRw1\r\n" +
	"swoGAMma9cJkhr+laE9Hp1geirKTG9Lwfpd46tWWU8XUf1XYYnY8uDpqqPfQA1fu\r\n" +
	"a2+Et7i9sJspkIihK17jcnHPdaB5rXvRFg5/CnRQi0m1TdMZkF7xnZiCKvagoiGN\r\n" +
	"dB9Kkv8/q3MusZKeW1+mhcGbkwKBgQDtxzTTynDli4C230F95iEeUPhilp5Jx0ND\r\n" +
	"9cMkL7DQrpSCrAU2aDKDg/lSbDgwlQgZQXZ3JdbOqrV+d6pdP7Uo0WLKChg8+wVf\r\n" +
	"b+v446QT8EPlRNDJpEByai3SIUbC5H48ZMDduOv60ztq8/Sx+IcViCybhTYVrJPe\r\n" +
	"PtGfU8M6LwKBgF4aSZ/AFSl89n9+f7JHlN3GtmuAHK2QZcGDFQJC1QxQcQ3wxH3b\r\n" +
	"qPVIP5jWdc0Rm89J1UfdaSpNyv3T7S2CfoUydBvjfgca+R2Pb5l3SelW2dtN/G5j\r\n" +
	"lqwsdcgaquK9pH4ZAsga/sCQ/Tj60ixT2xw/4w5Uk8xQb6w15orLWQhfAoGBAMa5\r\n" +
	"h+A+QG7IUdIE8yJ61FEXERCsmyxEOmaxHt++cANo9Tvk8xJOsXETOqv1QFzyIWui\r\n" +
	"N5VK7aWaLMe+0YbPuOOdY/hByZIlpY7m5owEDVyQoN4bznfILAizzwngv9ggyZRG\r\n" +
	"DJ8Cb08ZfX5c17RqZtPLFKUCB3rD2m2TiPSlN/QHAoGAXU7DAHfF2gp7GstIzR5z\r\n" +
	"YfRY2qN0UxzlCRrJKsBDvahWAqB6kUDahqTyssyEwvO2MT07H5DMoEYIzKcUUYk+\r\n" +
	"qnYxogjs9s3l8wD+L8pGlHDA9vWzjrN21kIdTzd6G/dI/Bwx6cKM2+rtiDrKfCHW\r\n" +
	"hH10Bm+ZG3ualI2J9NzsDCk=\r\n" +
	"-----END PRIVATE KEY-----";*/

	var privateKey = "-----BEGIN PRIVATE KEY-----\n" +
        "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCgyjIMeQOq77ux\n" +
        "No5IHGbXVVBDfrtssbxqje0evB1Q8add8NaPwPvt+c+uk2fXl/RCJ3R26HsFD3dI\n" +
        "S2lw2aJuQ2oJ0O5k9vArkgLHdgpKuB2gjOOjbDMT2PBpl6ISC13KhdRTOSXlx5i8\n" +
        "Klqkfz/AQgnEchtALOHFO+Wvz3CtEmZE5JHyZX8G79b0qD6gOivACn821mJrKjpU\n" +
        "7hwiZ8KCz8q1S7CYgbOl/va6Nj+TQn9U2tXX+FXVQEffHfsknXhvQYw4y5uyEuJl\n" +
        "7o2QuzxX5FLVYVYKEdaKgYQIpHZzcg6upSfpqmz6L7+1y5LM9lLYXsux1eXmJKvz\n" +
        "22QBsnyNAgMBAAECggEABnGWord+fTfOeg+MEVmVXoJUV+Fzket9N/j4Ryp1806S\n" +
        "l5oardf8+1XEVq72HaarkDN5avgt++8u0ivMLeIEQi57SKmXRYkRlBwYdpVlXAa3\n" +
        "uz2CNERWYgXS5qxqv0CLhvL+O21CIUyyiIdFqtYlqtG1XKp6Ve6mh/YP96UqKGlm\n" +
        "IwX5C75sez29tKgHOscbS+xZro6qEeOuc8v6Lj583PQXcJ/DppPPI3fOrfXM7nEe\n" +
        "Xoq3AnTQ3SiIA4sYTdvmZCvu8LPROSw3bKF2RIF2zmxkNMo2i9FuffmMmlbz440x\n" +
        "UINxMULfMHMgpy3LX5CBQrSrImHdRQMc0fyz8+Jw6QKBgQDZ8FDETfPLo873xN8F\n" +
        "hLozpTfLSjK6xYhEYfrStHcp0vX3OkgdwKmdLeMfqO7mp0g3FxFRvF/Xp1I1qQus\n" +
        "KrJOXw5T1PW+o+WGz1Fuc1iUa4sJcLzhRyDni2MmnFGGHET6VPlib4T7yNLjwWs4\n" +
        "I3YDVO5Xd/7TMcfnUBe9oMHHeQKBgQC83tiHrXJq/ZtRLEloqOfsTs9DzQZdU/4F\n" +
        "f3AFCjoDqL9+sSnm2KTetocwtPFjaKeauhp+TLjQYfUSgSVvk6SvOfFmamfmGtzH\n" +
        "vUnVjTHbgS0YYUNdy712gcRA29FHpE74z5Hjk28LviEGtu8cpMDUR/sKia93/Tm4\n" +
        "HPFtODMUtQKBgQCHv1U/gOKLBERlNh5IPK0SgCWpnVTN2z4Ogg0Oc1QKHBoS3R2Q\n" +
        "UrITckKXNM1ho+6qSdcC11fHZrQhMmNP4ORhRnoRI4PSNBpChx3Ms1IIWJgo0C0G\n" +
        "ERzgvISMxy9MXo1kc8tGUfo+Lo+VR6O2OvhGCP/QQbDa8ZX3cX7oIZqx2QKBgFiL\n" +
        "EG/q/Gz0H9BaNr/+D1xVRg2/HHC8Y6YoLFfwh2oXvk6it8YlYoWWhKJs8Y1tDsYW\n" +
        "4Ty8sxfeFIpsSSSZzubm/yjIUZUqmr55GxgmbW0+jemUD4T1cwkPpPXTF7XCbdNv\n" +
        "XDGYeMLEi68eK9L92firFiHGkqBp8plB2F0fr7LRAoGBALmHsz1ureCXtbXJE8XP\n" +
        "GaPUpvM7lgPbv//TNv/NRH/I6I1qLo42/iCvMwHDDtWScWKIsovPBkY8XXCQHj0t\n" +
        "9bqmNi/mr5oOAuKh7E/h3GgrbWHeLly5zaMOGZr/ji12Qbuj1i1Hg5oqsl05pWKi\n" +
        "ndVwitLkyfcs0uWq3Ltoeljv\n" +
        "-----END PRIVATE KEY-----";

        qz.security.setSignaturePromise(function(toSign) {
            return function(resolve, reject) {
	try {
                    var pk = KEYUTIL.getKey(privateKey);
                    var sig = new KJUR.crypto.Signature({"alg": "SHA1withRSA"});
                    sig.init(pk);
                    sig.updateString(toSign);
                    var hex = sig.sign();
                    resolve(stob64(hextorstr(hex)));
	} catch (err) {
                    try{
		$.ajax({
			method : 'POST',
			url : '/kettle-service/print/v1/printer/secure/url/for',
			data : toSign,
			async : false,
                            success : resolve
		});
                    }catch (e){
                        reject(e);
	}
}
		};
        });

        launchQZ();
	}

// / Connection ///
function launchQZ() {
	if (!qz.websocket.isActive()) {
		// Retry 5 times, pausing 1 second between each attempt
		startConnection({
			retries : 5,
			delay : 1
		});
}
}

function startConnection(config) {
	if (!qz.websocket.isActive()) {
		qz.websocket.connect(config).then(function() {
			console.log("QZ Connected!");
		});
	} else {
		console.log("An active connection with QZ already exists.");
			}
}

