<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div flash-message="5000" ></div>
<img src="images/logo.png" class="brandImg center-block" style="margin-top: 20px; width: 200px;">
<div class="col-md-6 col-md-offset-4 col-lg-4 col-lg-offset-4 col-xs-6 col-xs-offset-4" style="background-color:cornsilk;
 margin-top: 20px;border:1px solid #8a6d3b;padding: 10px;" data-ng-init="init()">
    <!--<h2 class="text-center" style="margin-top: 20px">Login</h2>-->
    <div class="row">
	    <div class="col-xs-12" id="login-pills" data-ng-hide="autoConfig">
		    <tabset  type="pills" style=" margin-top: 20px">
		        <tab ng-repeat="outlet in vm.outletType" heading={{outlet.name}} 
		        					select="outletTypeTabSelected(outlet.code)"></tab>
		    </tabset>
		</div>
    </div>

    <div class="row" data-ng-show="autoConfig">
        <div class="col-xs-12">
            <p class="alert alert-info text-center" style="padding: 5px 10px;">{{unitName}} (T-{{selectedTerminalId}}) {{screenName}}</p>
        </div>
    </div>

    <form novalidate name="loginForm" ng-submit="vm.login()" role="form" style="margin: 25px 30px 30px;">

        <div class="form-group">
            <label for="userId">User iD</label>
<!--            &nbsp;&nbsp;&nbsp;-->
            <input type="number" name="userId" id="userId"
                   class=" col-xs-offset-2"
                   style="width: 200px;"
                   ng-minlength="6" ng-maxlength="6"
                   ng-model="vm.userId" auto-fill-sync required ng-change="outletForEmployeeId()"/>
        </div>

        <div ng-messages="loginForm.userId.$error" style="color:maroon" role="alert">
            <div ng-if="loginForm.userId.$touched">
                <div ng-message="required">User iD field can't be empty</div>
                <div ng-message="minlength">User iD is too short</div>
                <div ng-message="maxlength">User iD is too long</div>
            </div>
        </div>

        <div class="form-group">
            <label for="password">Passcode</label>
            <input type="password" name="password" id="password"
                   style="width: 200px;"
                   class="col-xs-offset-2"
                   ng-model="vm.password" auto-fill-sync required />
        </div>

        <div ng-messages="loginForm.password.$error" style="color:maroon" role="alert">
            <div ng-if="loginForm.password.$touched">
                <div ng-message="required">Passcode field can't be empty</div>
            </div>
        </div>


        <div class="form-group" data-ng-hide="autoConfig">
            <label for="unit">Unit</label>
            <dropdown-select dd-model="sampleModel" dd-data="vm.outletList" dd-change="unitChange(sampleModel)" dd-label="name" >
            </dropdown-select>
        </div>

        <div class="form-group" data-ng-hide="autoConfig">
            <label for="unit">Screen Type</label>
<!--            &nbsp;&nbsp;-->
            <select class="col-xs-offset-1"
                    id="screen"
                    style="width: 200px;line-height:2.8em; font-size: 18px"
                    ng-model="screenType"
                    ng-change ="isCustomerScreen()" required>
                <option value="POS">Transaction</option>
                <option ng-if="(isDelivery || isCafe || isTakeaway) && !isWorkStationEnabled()" value="ASSEMBLY">Assembly</option>
            </select>
        </div>


        <!-- <div class="form-group">

            <input type="text"
                   id="unit"
                   ng-model="unitName"
                   typeahead="outlet.name as outlet.name for outlet in getOutlet($viewValue)"
                   typeahead-min-length="2"
                   placeholder="Units"
                   typeahead-loading="isLoading"
                   typeahead-no-results="noResults"
                   typeahead-on-select="onSelect($item, $model, $label)"
                   class="text-center col-xs-12"
                   style="line-height:2.2em; font-size: 14px;margin-bottom: 20px;"
                   autocomplete="off">
        </div> -->
        <div ng-show="noResults">
            <i class="glyphicon glyphicon-remove"></i> No Results Found
        </div>

        <div class="row productRow" ng-model = "terminalArray" collapse="terminalArray.length < 2">
            <button ng-repeat="terminal in terminalArray" class='btn btn-lg'
                    ng-class="{addOnButton: !terminal.checked, addOnButtonSelected: terminal.checked}"
                    type="button"
                    ng-click="selectedTerminal($index)">{{terminal.name}}</button>
        </div>


        <!-- <div class="checkbox" collapse="!isPOS">
          <label>
            <input type="checkbox" value="" id="isCustomerScreen"
                   ng-model="vm.isCustomerScreen"
                  ng-change ="isCustomerScreen()">
            Customer Screen
          </label>
        </div> -->

        <div class="form-actions">
            <button type="submit" ng-disabled=" vm.dataLoading" class="btn btn-primary">Login</button>
            <button type="button" ng-click="vm.passcodeChangeModalOpen()" class="btn btn-info">Change Passcode</button>
            <button type="button" ng-show="vm.localStorageLength>0" ng-click="vm.clearSiteData()" class="btn btn-danger">Clear Site Data</button>
        </div>


    </form>
</div>
