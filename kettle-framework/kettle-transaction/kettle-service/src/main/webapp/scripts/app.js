/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

/**
 * @ngdoc overview
 * @name posApp
 * @description
 * # posApp
 *'buttonInput':{
                        templateUrl:'scripts/modules/States/posButtonInput.html'
                    },
 * Main module of the application.
 */

var posApp = angular.module('posApp', [
    'ngAnimate',
    'ngCookies',
    'ngResource',
    'ngSanitize',
    'restangular',
    'ui.router',
    'ui.bootstrap',
    'ngMessages',
    'ui.grid',
    'ui.grid.edit',
    'flash',
    'timer',
    'angularSpinner',
    '720kb.datepicker',
    /*'blockUI',*/
    'pubnub.angular.service',
    'ngIdle',
    'DropDownSelect'
]);

posApp.value('version', '2.8');
posApp.config(['$stateProvider', '$urlRouterProvider',"KeepaliveProvider", "IdleProvider",
    function ($stateProvider, $urlRouterProvider,KeepaliveProvider, IdleProvider) {
        $stateProvider
            .state('posMain', {
                url: '/pos',
                templateUrl: window.version + 'scripts/modules/States/newPOS.html',
                controller: 'posCtrl'
            })
            .state('login', {
                url: '/login',
                templateUrl: window.version + 'scripts/modules/States/loginView.html',
                controller: 'LoginController',
                controllerAs: 'vm'
            })
            .state('cover', {
                url: '/cover',
                templateUrl: window.version + 'scripts/modules/States/coverView.html',
                controller: 'coverController',
                controllerAs: 'cc'
            })
            .state('orderSearch', {
                url: '/orderSearch',
                templateUrl: window.version + 'scripts/modules/States/orderSearch.html',
                controller: 'orderSearchController'
            })
            .state('orderRefundSearch', {
                url: '/orderRefundSearch',
                templateUrl: window.version + 'scripts/modules/States/orderRefundSearch.html',
                controller: 'orderRefundSearchCtrl'
            })
            .state('CS_ThankYou', {
                url: '/csthankyou',
                templateUrl: window.version + 'scripts/modules/States/CS_ThankYou.html',
                controller: 'customerController'

            })
            .state('CS_PhLogin', {
                url: '/cslogin',
                templateUrl: window.version + 'scripts/modules/States/CS_PhLogin.html',
                controller: 'customerController'
            })
            .state('CS_PhRegister', {
                url: '/csregister',
                templateUrl: window.version + 'scripts/modules/States/CS_register.html',
                controller: 'customerController'
            })
            .state('CS_Profile', {
                url: '/csprofile',
                templateUrl: window.version + 'scripts/modules/States/CS_Profile.html',
                controller: 'customerController'
            })
            .state('orderSummary', {
                url: '/orderSummary',
                templateUrl: window.version + 'scripts/modules/States/orderSummary.html',
                controller: 'orderSummaryCtrl'
            })
            .state('COD_cover', {
                url: '/CODCover',
                templateUrl: window.version + 'scripts/modules/States/COD/COD_coverview.html',
                controller: 'COD_coverController'
            })
            .state('pendingRefunds', {
                url: '/pendingRefunds',
                templateUrl: window.version + 'scripts/modules/States/COD/pendingRefunds.html',
                controller: 'pendingRefundsCtrl'
            })
            .state('COD_customerSearch', {
                url: '/CODCSlookup',
                templateUrl: window.version + 'scripts/modules/States/COD/COD_customerSearch.html',
                controller: 'COD_cslookup'
            }).state('COD_customerOrders', {
            url: '/customerOrders',
            templateUrl: window.version + 'scripts/modules/States/COD/COD_customerOrders.html',
            controller: 'COD_customerOrderCtrl'
        })
            .state('viewSDP', {
                url: '/viewSDP',
                templateUrl: window.version + 'scripts/modules/States/COD/viewSDP.html',
                controller: 'viewSDPCtrl'
            })
            .state('cafePartnerStatus', {
                url: '/cafePartnerStatus',
                templateUrl: window.version + 'scripts/modules/States/COD/cafePartnerStatus.html',
                controller: 'cafePartnerStatusCtrl'
            })
            .state('assembly', {
                url: '/assembly',
                templateUrl: window.version + 'scripts/modules/States/assembly.html',
                controller: 'AssemblyController'
            })
            .state('inventory', {
                url: '/inventory',
                templateUrl: window.version + 'scripts/modules/States/inventory.html',
                controller: 'InventoryController'
            })
            .state('unithealth', {
                url: '/unithealth',
                templateUrl: window.version + 'scripts/modules/States/unitHealth.html',
                controller: 'UnitHealthController'
            })
            .state('openOrderSummary', {
                url: '/openOrderSummary',
                templateUrl: window.version + 'scripts/modules/States/COD/openOrderSummary.html',
                controller: 'openOrderSummaryCtrl'
            })
            .state('subscriptionSearch', {
                url: '/subscriptionSearch',
                templateUrl: window.version + 'scripts/modules/States/subscriptionSearch.html',
                controller: 'subscriptionSearchCtrl'
            })
            .state('subscriptionOrderSearch', {
                url: '/subscriptionOrderSearch',
                templateUrl: window.version + 'scripts/modules/States/subscriptionOrderSearch.html',
                controller: 'subscriptionOrderSearchCtrl'
            })
            .state('subscriptionOrderByUnit', {
                url: '/subscriptionOrderByUnit',
                templateUrl: window.version + 'scripts/modules/States/subscriptionOrderByUnit.html',
                controller: 'subscriptionOrderByUnitCtrl'
            })
            .state('pullManagement', {
                url: '/pullManagement',
                templateUrl: window.version + 'scripts/modules/States/pullManagement.html',
                controller: 'pullManagementCtrl'
            }).state('openTakeawayOrders', {
            url: '/openTakeawayOrders',
            templateUrl: window.version + 'scripts/modules/States/openTAOrders.html',
            controller: 'openTAOrdersCtrl'
        }).state('openDeliveryOrders', {
            url: '/openDeliveryOrders',
            templateUrl: window.version + 'scripts/modules/States/openDeliveryOrders.html',
            controller: 'openDeliveryOrdersCtrl'
        }).state('pullSettlement', {
            url: '/pullSettlement',
            templateUrl: window.version + 'scripts/modules/States/pullSettlement.html',
            controller: 'pullSettlementCtrl'
        }).state('pullSettlementsView', {
            url: '/pullSettlementsView',
            templateUrl: window.version + 'scripts/modules/States/pullSettlementsView.html',
            controller: 'pullSettlementsViewCtrl'
        }).state('paymentRefund', {
            url: '/paymentRefund',
            templateUrl: window.version + 'scripts/modules/States/paymentRefund.html',
            controller: 'paymentRefundController'
        }).state('swiggyOrder', {
            url: '/swiggyOrderView',
            templateUrl: window.version + 'scripts/modules/States/COD/swiggyOrderView.html',
            controller: 'swiggyOrderViewCtrl'
        }).state('amazonOrder', {
            url: '/amazonOrderView',
            templateUrl: window.version + 'scripts/modules/States/COD/amazonOrderView.html',
            controller: 'amazonOrderViewCtrl'
        }).state('cafeTimingsDashboard', {
            url: '/cafeTimingsDashboard',
            templateUrl: window.version + 'scripts/modules/States/COD/cafeTimingsDashboard.html',
            controller: 'cafeTimingsDashboardCtrl'
        }).state('manageRider', {
            url: '/manageRider',
            templateUrl: window.version + 'scripts/modules/States/manageRider.html',
            controller: 'manageRiderController'
        }).state('expenseTracking', {
            url: '/expenseTracking',
            templateUrl: window.version + 'scripts/modules/States/expenseTracking.html',
            controller: 'expenseTrackingController'
        }).state('PnLReport', {
            url: '/PnLReport',
            templateUrl: window.version + 'scripts/modules/States/PnLReport.html',
            controller: 'PnLReportController'
        }).state('partnerOrderDashboard', {
            url: '/partnerOrderDashboard',
            templateUrl: window.version + 'scripts/modules/States/COD/partnerOrderDashboard.html',
            controller: 'partnerOrderDashboardCtrl'
        }).state('partnerCafeDashboard', {
            url: '/partnerCafeDashboard',
            templateUrl: window.version + 'scripts/modules/States/COD/partnerCafeDashboard.html',
            controller: 'partnerCafeDashboardCtrl'
        }).state('table', {
            url: '/table',
            templateUrl: window.version + 'scripts/modules/States/table.html',
            controller: 'tableCtrl'
        }).state('tableSummary', {
            url: '/tableSummary',
            templateUrl: window.version + 'scripts/modules/States/tableSummary.html',
            controller: 'tableSummaryCtrl'
        });

        $urlRouterProvider.otherwise('/cover');

        // setting inactivity timer
        IdleProvider.idle(3*60*60);// in sec

    }])
    .service('fileService', [function () {
        var service = this;
        service.file = null;
        service.push = function (file) {
            service.file = file;
        };
        service.getFile = function () {
            return service.file;
        };
    }])
    .service('AuthService', [function () {
        var service = this;
        service.authorization = null;
        service.getAuthorization = getAuthorization;
        service.setAuthorization = function (authorization) {
            service.authorization = authorization;
        };

        function getAuthorization() {
            return service.authorization;
        }

        return service;
    }])
    /*.service('modalInterceptor', ['$rootScope', '$q', 'version', function ($rootScope, $q, version) {
        var service = this;
        service.request = function (request) {
            if (request.url.substr(-5) == '.html' && request.url.indexOf("views") != -1) {
                $rootScope.showModalLoader = true;
                request.params = {
                    v: version
                };
            }
            return $q.resolve(request);
        };
        service.response = function (response) {
            $rootScope.showModalLoader = false;
            return $q.resolve(response);
        };
        service.responseError = function (responseError) {
            $rootScope.showModalLoader = false;
            return $q.resolve(responseError);
        };
        return service;
    }])*/
    .service('authInterceptor', ['$rootScope', 'AuthService', '$location', function ($rootScope, AuthService, $location) {
        var service = this;
        service.request = function (config) {
            config.headers.auth = AuthService.getAuthorization();
            if (config.method == "POST" && config.data == undefined) {
                config["data"] = {};
            }
            return config;
        };
        service.responseError = function (response) {
            $rootScope.showFullScreenLoader = false;
            if (response.status === 401) {
                AuthService.setAuthorization(null);
                // AuthenticationService.ClearCredentials();
                $location.path('/login');
            }
            return response;
        };
    }]).filter('yesNo', function () {
    return function (input) {
        return input == undefined || input == null || !input ? 'no' : 'yes';
    };
})
    .config(['$httpProvider', function ($httpProvider) {
        $httpProvider.interceptors.push('authInterceptor');
        //$httpProvider.interceptors.push('modalInterceptor');
    }])

    .run(['$rootScope', '$state', '$interval', 'posAPI', '$stateParams', '$location', '$cookieStore', '$http', 'AppUtil', 'AuthService',
        function ($rootScope, $state, $interval, posAPI, $stateParams, $location, $cookieStore, $http, AppUtil, AuthService) {
            $rootScope.$state = $state;
            $rootScope.$stateParams = $stateParams;
            $rootScope.showFullScreenLoader = false;
            $rootScope.showModalLoader = false;
            $rootScope.isMqtt = false;
            if ($rootScope.globals == null || angular.isUndefined($rootScope.globals)) {
                $rootScope.globals = $cookieStore.get('globals') || {};
            }
            //console.log($rootScope.globals)
            $rootScope.$on('$locationChangeStart', function (event, next, current) {
                // redirect to login page if not logged in and trying to access a restricted page
                var restrictedPage = $.inArray($location.path(), ['/login']) === -1;
                var body = document.getElementsByTagName("BODY")[0];
                var currentUrl = $location.url();
                var loggedIn = ($rootScope.globals.currentUser != null) && !angular.isUndefined($rootScope.globals.currentUser);
                //angular.isUndefined($rootScope.globals.currentUser)?AuthService.setAuthorization(null):AuthService.setAuthorization($rootScope.globals.currentUser.jwtToken);
                if (currentUrl == '/cslogin' || currentUrl == '/csprofile' || currentUrl == '/csthankyou' || currentUrl == '/csregister') {
                    body.style.backgroundColor = '#F9F8F5';
                }
                //console.log(loggedIn);
                if (restrictedPage && !loggedIn && currentUrl != '/unithealth') {
                    $location.path('/login');
                } else {
                    if ($location.path().length) {
                        AppUtil.previousLocation.push($location.path());
                    } else {
                        $location.path(AppUtil.previousLocation[AppUtil.previousLocation.length - 1]);
                    }
                }
            });
        }])
    .directive('fileModel', ['$parse', 'fileService', function ($parse, fileService) {
        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                element.bind('change', function () {
                    scope.$apply(function () {
                        var accepts = attrs.accept;
                        var file = element[0].files[0];
                        console.log("file is ::::::", file, attrs);
                        if (accepts === file.type) {
                            if (file.size <= (500000 * 4)) {
                                fileService.push(file);
                            } else {
                                bootbox.alert("Please upload a file of less than 1MB");
                            }
                        } else {
                            bootbox.alert("Please upload a proper file. Only " + accepts + " files are allowed.");
                        }
                    });
                });
            }
        };
    }])
    .directive('otpModel', function () {
        return {
            restrict: 'E',
            controller: ['$scope', '$timeout', 'socketUtils', 'AppUtil', function ($scope, $timeout, socketUtils, AppUtil) {
                $scope.checkOTPStatus = function () {
                    $scope.showLoadingOnButton = true;
                    socketUtils.emitMessage({GIFT_CARDS_OTP_CHECK: AppUtil.customerSocket});
                    $timeout(function () {
                        $scope.showLoadingOnButton = false;
                    }, 3000);
                };
            }],
            template: '<div class="btn btn-primary"style="width:150px" data-ng-click="checkOTPStatus()">'
            + '<span data-ng-show="!showLoadingOnButton">Refresh OTP Status</span>'
            + '<i data-ng-show="showLoadingOnButton" class="fa fa-refresh fa-spin" style="font-size:17px"></i></div>'
        };
    })
    .directive('blink', ['$timeout', function ($timeout) {
        return {
            restrict: 'E',
            transclude: true,
            scope: {},
            controller: ['$scope', '$element', function ($scope, $element) {
                function showElement() {
                    $element.css("display", "inline");
                    $timeout(hideElement, 1000);
                }

                function hideElement() {
                    $element.css("display", "none");
                    $timeout(showElement, 1000);
                }

                showElement();
            }],
            template: '<span ng-transclude></span>',
            replace: true
        };
    }])
    .directive("aclMenu", ['$rootScope', function ($rootScope){
    function link(scope, element, attributes) {
        var aclData = $rootScope.aclData;
        if(aclData!=null){
            if(aclData.menu!=null && aclData.menu[attributes.aclMenu]!=null){
                element.show();
            }else{
                element.hide();
            }
        }
        /*scope.$watch(attributes.acl, function(value, oldValue) {
         heart(value);
         }, true);*/
    }
    return({
        link: link,
        restrict: "A"
    });
    }])
    .directive("aclSubMenu", ['$rootScope', function ($rootScope){
        function link(scope, element, attributes) {
            var aclData = $rootScope.aclData;
            if(aclData!=null){
                if(aclData.subMenu!=null && aclData.subMenu[attributes.aclSubMenu]!=null){
                    element.show();
                }else{
                    element.hide();
                }
            }
        }
        return({
            link: link,
            restrict: "A"
        });
    }])
    .directive("aclAction", ['$rootScope', function ($rootScope){
        function link(scope, element, attributes) {
            var aclData = $rootScope.aclData;
            if(aclData!=null){
                if(aclData.action!=null && aclData.action[attributes.aclAction]!=null){
                    element.show();
                }else{
                    element.hide();
                }
            }
        }
        return({
            link: link,
            restrict: "A"
        });
    }]);

posApp.filter('getPercentageReport', function () {
    return function (num1, num2, num3) {
        if (num3) {
            if (isNaN(num1) || isNaN(num2) || num2 == 0) {
                return "0 %";
            } else {
                return parseFloat((parseInt(num1) / parseInt(num2)) * 100).toFixed(0) + " %";
            }
        } else {
            return "NA";
        }
    }
});

posApp.filter('getPercentageValue', function () {
    return function (num1, num2, num3) {
        if (num3) {
            if (isNaN(num1) || isNaN(num2) || num2 == 0) {
                return "0 %";
            } else {
                // return parseFloat((parseInt(num1) / parseInt(num2)) * 100).toFixed(0) + " %";
                return Math.round(num1) + " %";
            }
        } else {
            return "NA";
        }
    }
});

posApp.filter('secondsTimer', function () {
    return function (seconds) {
        return parseInt((seconds/60)+'', 0) + "min : " + (seconds%60) + "sec";
    }
});





