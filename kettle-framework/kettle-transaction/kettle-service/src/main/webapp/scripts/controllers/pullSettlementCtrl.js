/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('pullSettlementCtrl', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','$modal',
        function ($rootScope,$scope,AppUtil,$location,$http,posAPI,$modal) {

	    	$scope.init = function(){
	    		AppUtil.validate();
	    		$scope.backToCover = AppUtil.backToCover;
	    		$scope.openPulls = null;
	    		$scope.listLoading = false;
				$scope.minDate = null;
				$scope.maxDate = null;
	    	};
	    	
	    	$scope.getPaymentModes = function(){
	    		var modes = [];
	    		if(AppUtil.getTransactionMetadata().paymentModes!=null){
	    			AppUtil.getTransactionMetadata().paymentModes.forEach(function(v){
		    			if(v.generatePull){
		    				modes.push(v);
		    			}
		    		});
		    		$scope.paymentModes = angular.copy(modes);
		    		$scope.selectedPaymentMode = $scope.paymentModes[0];
	    		}else{
	    			$location.url('/login');
	    		}
	    	};
	    	
	    	$scope.getPaymentModes();
	    	
	    	$scope.getPullPackets = function(){
	    		$scope.listLoading = true;
	    		var obj = {
	    				unitId : $rootScope.globals.currentUser.unitId,
					paymentModeId: $scope.selectedPaymentMode == null ? 1 : $scope.selectedPaymentMode.id,
	    				statusList : ["CREATED"]
	    		};
	    		//var requestObj = AppUtil.GetRequest(obj);
	        	  posAPI.allUrl('/',AppUtil.restUrls.cashManagement.getOpenPullsForUnit)
	    		  .post(obj).then(function(response) {
	    			 if(response!=null){
	    				$scope.openPulls = response.plain();
	    				$scope.getSettlementDenominations();
						$scope.setValidPullTransferDates();
	    			 }
	 	    		 $scope.listLoading = false;
	    		  }, function(err) {
	    			  AppUtil.myAlert(err.data.errorMessage);
	  	    		  $scope.listLoading = false;
	    		  });
	    	};
	    	
	    	/*$scope.transferPull = function(pull){
	    		var decision = confirm("Are you sure you want to transfer this packet?");
	    		if(decision){
	    			$scope.selectedPull = pull;
	    			var requestObj = AppUtil.GetRequest($scope.selectedPull);
	    	    	posAPI.allUrl('/',AppUtil.restUrls.cashManagement.transferPull)
	    			.post(requestObj).then(function(response) {
	    		    	$scope.getPullPackets();
	    			}, function(err) {
	    				AppUtil.myAlert(err.data.errorMessage);
	    			});
	    		}
	    	}*/
	    	
	    	$scope.getSettlementDenominations = function(){
	    		//var requestObj = AppUtil.GetRequest($scope.selectedPaymentMode);
    	    	posAPI.allUrl('/',AppUtil.restUrls.cashManagement.getPullDenominationsForPaymentMode)
    			.post($scope.selectedPaymentMode).then(function(response) {
    				if(response!=null){
    					$scope.initSettlementDenominations = response.plain();
    				}
    			}, function(err) {
    				AppUtil.myAlert(err.data.errorMessage);
    			});
	    	};
	    	
	    	$scope.createTransfer = function(){
	    		$scope.setSettlementDenominations();
	    		$modal.open({
                    animation: true,
                    templateUrl: window.version+"scripts/modules/modals/transferPullModal.html" ,
                    controller: 'transferPullModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: "lg"
                });
	    	};
	    	
	    	$scope.hasTransfers = function(){
	    		var retVal = false;
	    		if($scope.openPulls!=null){
	    			$scope.openPulls.forEach(function(v){
		    			if(v.transfer===true){
		    				retVal = true;
		    			}
		    		});
		    		return retVal;
	    		}
	    	};
	    	
	    	$scope.setSettlementDenominations = function(){
	    		$scope.settlementTotalAmount = 0;
	    		$scope.settlementOriginalAmount = 0;
	    		$scope.settlementExtraAmount = 0;
	    		$scope.settlementDenominations = angular.copy($scope.initSettlementDenominations);
	    		$scope.openPulls.forEach(function(v){
	    			if(v.transfer===true){
	    				$scope.settlementDenominationsAdd(v.pullDenominations);
	    				$scope.settlementTotalAmount += v.pullAmount;
	    			}
	    		});
	    		$scope.settlementOriginalAmount = $scope.settlementTotalAmount;
	    		//Round off to the nearest hundred in case of cash
	    		if($scope.selectedPaymentMode.id == 1){
	    		    $scope.settlementTotalAmount = Math.ceil($scope.settlementTotalAmount/100)*100;
	    		    $scope.settlementExtraAmount = $scope.settlementTotalAmount - $scope.settlementOriginalAmount;
	    		}
	    		$scope.calculateSettlementTotal();
	    	};
	    	
	    	$scope.calculateSettlementTotal = function(v){
	    		$scope.settlementDenominations.forEach(function(v){
	    			v.totalAmount = (v.denominationDetail.bundleSize*v.denominationDetail.denominationValue*v.settlementPacketCount)+
	    			v.denominationDetail.denominationValue*v.settlementLooseCurrencyCount;
	    		});
    		};
	    	
	    	$scope.settlementDenominationsAdd = function(pullDenominations){
	    		pullDenominations.forEach(function(v){
	    			$scope.settlementDenominations.forEach(function(sv){
	    				if(v.denominationDetail.denominationId===sv.denominationDetail.denominationId){
	    					sv.packetCount += v.packetCount;
	    					sv.looseCurrencyCount += v.looseCurrencyCount;
	    	    			sv.settlementPacketCount = sv.packetCount;
	    	    			sv.settlementLooseCurrencyCount = sv.looseCurrencyCount;
	    				}
	    			});
	    		});
	    	};

			$scope.setValidPullTransferDates = function () {
				var obj = {
					unitId : $rootScope.globals.currentUser.unitId,
					paymentMode : $scope.selectedPaymentMode==null?1:$scope.selectedPaymentMode.id,
				};
				posAPI.allUrl('/', AppUtil.restUrls.cashManagement.getValidPullTransferDates)
					.post(obj).then(function (response) {
					if (response != null) {
						$scope.minDate = response.min;
						$scope.maxDate = response.max;
					}
				}, function (err) {
					AppUtil.myAlert(err.data.errorMessage);
				});
			};

	}]).controller('transferPullModalCtrl', ['$http', '$rootScope', '$scope', '$modalInstance','AppUtil','posAPI', 'fileService',
	function ($http, $rootScope, $scope, $modalInstance,AppUtil,posAPI, fileService) {
    
    $scope.init = function(){
		$scope.editedDenominations = [];
    	$scope.setEditedDenominations();
    	$scope.settlementServiceProvider='BANK_PICKUP';
    	$scope.getDenominationSum();
    	if($scope.initSettlementDenominations.length==0 && $scope.selectedPaymentMode.type!="COUPON"){
    		$scope.denominationSum = $scope.settlementTotalAmount;
    	}
    	$scope.loading = false;
		$scope.couponDenominations = null;
		if($scope.selectedPaymentMode.type=="COUPON"){
    		$scope.transferCouponDenominations();
    	}
		$scope.gettingCouponDenoms = false;
		$scope.showSettlementSlip = $scope.selectedPaymentMode.needsSettlementSlip;
		$scope.uploading = false;
		$scope.uploadDone = false;
		$scope.slipNumber = null;
		$scope.ticketNumber = null;
		$scope.serialNumber = null;
		$scope.settlementDate = null;
	};
    
    $scope.ok = function(receiptPath) {
		if($scope.denominationSum!==$scope.settlementTotalAmount){
			bootbox.alert("Settlement amount don't match!");
			$scope.loading=false;
		}else if($scope.showSettlementSlip && !$scope.uploadDone){
			bootbox.alert("Upload slip before submitting settlement");
			$scope.loading=false;
		}else{
    		$scope.submitSettlement(receiptPath==undefined ? null : receiptPath);
    	}
    };
    $scope.cancel = function() {
    	$modalInstance.dismiss("cancel");
    };
    
    $scope.setEditedDenominations  = function(){
    	$scope.settlementDenominations.forEach(function(v){
    		if(v.packetCount==null || v.looseCurrencyCount==null){
    			v.packetCount=0;
    			v.looseCurrencyCount=0;
    			v.settlementPacketCount=0;
    			v.settlementLooseCurrencyCount=0;
    			v.totalAmount=0;
    		}
    		$scope.editedDenominations.push(v);
    	});
    };
    
    $scope.submitSettlement = function(receiptPath){
    	var settlementObj = {
    			settlementUnit : $scope.openPulls[0].pullPacketUnit,
    			settlementTime : null,
    			settlementServiceProvider : $scope.settlementServiceProvider,
    			settlementAmount : null,
    			settlementType : $scope.selectedPaymentMode,
    			settlementServiceProviderReceipt : null,
    			unsettledAmount : null,
    			totalAmount : $scope.settlementTotalAmount,
    			originalAmount : $scope.settlementOriginalAmount,
    			extraAmount : $scope.settlementExtraAmount,
    			closingAmount : null,
    			settlementStatus : "UNSETTLED",
    			settlementClosingReceipt : null,
    			settlementReceiptPath : receiptPath,
    			pullDetails : $scope.selectedPullPackets(),
    			settlementDenominations : $scope.getFinalDenominations(),
				settlementDate : $scope.settlementDate.toLocaleString(),
				serialNumber : $scope.serialNumber,
				ticketNumber : $scope.ticketNumber,
				slipNumber : $scope.slipNumber
		};
    	//console.log(settlementObj);
    	$scope.loading = true;
    	//var requestObj = AppUtil.GetRequest(settlementObj);
    	posAPI.allUrl('/',AppUtil.restUrls.cashManagement.transferPull)
		.post(settlementObj).then(function(response) {
			if(response!=null){
				AppUtil.mySuccessAlert("Transfer created successfully!");
				$scope.getPullPackets();
			}else{
				AppUtil.mySuccessAlert("Error in creating transfer!");
			}
	    	$modalInstance.close();
		}, function(err) {
			AppUtil.myAlert(err.data.errorMessage);
	    	$modalInstance.close();
		});
    };
    
    $scope.selectedPullPackets = function(){
    	var pullPackets = [];
    	$scope.openPulls.forEach(function(v){
    		if(v.transfer===true){
    			pullPackets.push(v);
    		}
    	});
    	return pullPackets;
    };
    
    $scope.getDenominationSum = function(){
    	$scope.denominationSum = 0;
    	$scope.editedDenominations.forEach(function(v){
    		$scope.denominationSum += v.totalAmount;
    	});
    };
    
    $scope.calculateTotal = function(denom){
    	denom.totalAmount = (denom.denominationDetail.bundleSize*denom.denominationDetail.denominationValue*denom.settlementPacketCount)+
    	denom.denominationDetail.denominationValue*denom.settlementLooseCurrencyCount;
    	$scope.getDenominationSum();
    };
    
    $scope.getFinalDenominations = function(){
		var finalDenoms = [];
    	if($scope.selectedPaymentMode.type=="COUPON"){
    		finalDenoms = $scope.couponDenominations;
    	}else{
        	$scope.editedDenominations.forEach(function(v){
        		if(v.settlementLooseCurrencyCount>0 || v.settlementPacketCount>0){
        			var item = {
    	    			denominationDetail : v.denominationDetail,
    	    			looseCurrencyCount : v.settlementLooseCurrencyCount,
    	    			packetCount : v.settlementPacketCount,
    	    			totalAmount : v.totalAmount
    	    			
    	    		};
    	    		finalDenoms.push(item);
        		}
        	});
    	}
    	return finalDenoms;    	
    };
    
    $scope.transferCouponDenominations = function(){
    	$scope.gettingCouponDenoms = true;
    	var pullIds = [];
    	$scope.openPulls.forEach(function(v){
			if(v.transfer===true){
				pullIds.push(v.pullPacketId);
			}
		});
    	//var requestObj = AppUtil.GetRequest({pullId:pullIds,paymentModeId:$scope.selectedPaymentMode.id});
    	posAPI.allUrl('/',AppUtil.restUrls.cashManagement.transferCouponDenominations)
		.post({pullId:pullIds,paymentModeId:$scope.selectedPaymentMode.id}).then(function(response) {
			if(response!=null){
				response = response.plain();
				$scope.couponDenominations = response;
				$scope.denominationSum = 0;
		    	$scope.couponDenominations.forEach(function(v){
		    		$scope.denominationSum += v.totalAmount;
		    	});
			}
			$scope.gettingCouponDenoms = false;
		}, function(err) {
			AppUtil.myAlert(err.data.errorMessage);
			$scope.gettingCouponDenoms = false;
		});
    };
    


	$scope.uploadSettlement = function(){
		var date = $scope.settlementDate;
		var paymentMode = ($scope.selectedPaymentMode == null) ? 1 : $scope.selectedPaymentMode.id;
		if($scope.settlementServiceProvider === 'BANK_PICKUP') {
			var slipNumber = $scope.slipNumber;
			var imageUrl = fileService.getFile();
			var ticketNumber = $scope.ticketNumber ;
			var serialNumber = $scope.serialNumber ;
			var message = null;
			if(imageUrl == null){
				message = "Please upload a valid image less than 500kb";
			}else if(date==null || date=="") {
				message = "Please provide settlement date!";
			}
			else if(slipNumber==null || slipNumber=="") {
				message = "Please provide settlement slip number!";
			}
			else if(ticketNumber==null || ticketNumber=="") {
				message = "Please provide settlement ticket number!";
			}
			else if(serialNumber==null || serialNumber=="") {
				message = "Please provide settlement serial number!";
			}
			if(message !== null){
				bootbox.alert(message);
			}else {
				var url = AppUtil.restUrls.cashManagement.uploadTransferSlip;
				var fd = new FormData();
				fd.append('unitId', $rootScope.globals.currentUser.unitId);
				fd.append('slipNumber', slipNumber);
				fd.append('file', imageUrl);
				fd.append('slipDate', date.toLocaleDateString());
				fd.append('paymentMode',paymentMode);

				$scope.loading = true;
				$http.post(url, fd, {
					transformRequest: angular.identity,
					headers: {'Content-Type': undefined}
				}).success(function (response) {
					if (Object.keys(response).length > 0) {
						$scope.uploadDone = true;
						$scope.ok(response.receiptPath);
					} else {
						bootbox.alert("Upload failed! Try again later");
					}
				}).error(function (response) {
					$scope.loading = false;
					bootbox.alert("Upload failed! Try again later");
				});

			}
		}else{
			if(date==null || date=="") {
				bootbox.alert("Please provide settlement date!");
			}else {
				$scope.ok(null);
			}
		}
	};

		$scope.setSettlementProvider = function(provider) {
			$scope.settlementServiceProvider = provider;
			$scope.settlementDate = null;
			$scope.slipNumber = null;
			$scope.ticketNumber = null;
			$scope.serialNumber = null;
			$scope.showSettlementSlip = ($scope.settlementServiceProvider==='BANK_PICKUP')? true : false;
			};
}]);
