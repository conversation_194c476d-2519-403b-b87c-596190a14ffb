'use strict';

angular
    .module('posApp')
    .controller(
        'partnerOrderIdModalCtrl',
        ['$scope', '$rootScope','$modalInstance', 'AppUtil','$location', function ($scope, $rootScope, $modalInstance, AppUtil,$location) {

            $scope.init = function () {
                $rootScope.partnerName=null;
                $scope.isCOD=AppUtil.isCOD;
                $rootScope.channelPartners=["ZOMATO","SWIGGY"];
            };

            $scope.enterExternOrderId = function (channelPartnerOrder) {
                if (channelPartnerOrder != null && channelPartnerOrder != "") {
                    $scope.channelPartnerOrder = channelPartnerOrder;
                } else {
                    $scope.channelPartnerOrder = null;
                }
            };
            $scope.externPartnerName = function(channelPartnerName){
                if (channelPartnerName != null && channelPartnerName != "") {
                    $rootScope.partnerName = channelPartnerName;
                }
                else{
                    $rootScope.partnerName = null;
                }
            }
            $scope.setChannelPartnerOrderId = function (orderId) {
                $modalInstance.close(orderId);
            }
            $scope.goToCvrScreen = function (allow) {
                if (allow) {
                    $location.url('/CODCover');
                    $modalInstance.close();
                }

            }

        }]);

