/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {

    'use strict';

    angular.module('posApp').controller('coverController', coverController);

    coverController.$inject = ['$location', '$scope', '$http', '$rootScope',
        '$cookieStore', 'AuthenticationService', '$modal', 'AppUtil',
        'socketUtils', '$timeout', '$interval', 'posAPI', 'PrintService',
        'coverUtils', 'APIJson', 'desiChaiService', '$window','Idle'];

    function coverController($location, $scope, $http, $rootScope,
                             $cookieStore, AuthenticationService, $modal, AppUtil, socketUtils,
                             $timeout, $interval, posAPI, PrintService, coverUtils, APIJson, desiChaiService, $window,Idle) {


        $scope.init = function () {
            $scope.liveInventory = AppUtil.hasLiveInventory();
            $scope.envType = null;
            $scope.unitDetails = AppUtil.getUnitDetails();
            $scope.tableService = $scope.unitDetails.tableService;
            $scope.tableServiceType = $scope.unitDetails.tableServiceType;
            $scope.extendedTableService = AppUtil.hasExtendedTableService();
            $scope.disableForTakeaway = AppUtil.disableForTakeaway;
            $scope.isDefaultPasscode = $rootScope.isDefaultPasscode;
            $scope.isEmptyObject = AppUtil.isEmptyObject;
            $scope.screenHeight = window.innerHeight;
            $scope.isAndroid = AppUtil.isAndroid;
            $scope.cafeBrand = {};
            $scope.matchesGst=false;
            $scope.unitFamily = $rootScope.globals.currentUser.unitFamily;
            AppUtil.refreshRedeemLock();

            // $scope.getCafePartnerStatus($scope.unitDetails.id);
            // $scope.getKettleDayCloseStatus($scope.unitDetails.id);
            // $scope.getSumoDayCloseStatus($scope.unitDetails.id);
            $scope.getPnLStatusMap($scope.unitDetails.id);
            $scope.getAutoApplicableOfferForUnit($scope.unitDetails.id);
            $scope.isCafe = AppUtil.isCafe();
            $scope.isDelivery = AppUtil.isDelivery();
            $scope.isTakeaway = AppUtil.isTakeaway();

            $scope.currentUser = $rootScope.globals.currentUser;

            $scope.lastThreeOrderArray = [];

            $scope.pnlStatusMap = {};

            $scope.DayCloseReasonList = [];
            $scope.unitPullTransfers=[];
            $scope.showPullTransferOptions=false;

            // getting current status of pairing
            checkSocketStatus();

            $scope.getPendingSpecialRos();

            if (AppUtil.isEmptyObject(AppUtil.getTransactionMetadata())) {
                coverUtils.logOut();
            }

            if ($cookieStore.get("lastThreeOrders") != undefined
                && $cookieStore.get("lastThreeOrders").unitId == $scope.unitDetails.id) {
                //console.log("inside if statement of unitId check");
                $rootScope.lastThreeOrders = $cookieStore
                    .get("lastThreeOrders").orders;
                $scope.lastThreeOrderArray = $rootScope.lastThreeOrders;
            }

            $scope.isManager = $scope.currentUser.designation.name == "Manager" ? true : false;
            $scope.giftCardOfferInfo = AppUtil.giftCardOffers;
            timerClear();
            coverUtils.initDiscount();


            if (AppUtil.hasLiveInventory()) {
                $scope.getInventoryData();
                if($location.url()==='/cover'){
                    if ($scope.unitDetails != null && $scope.unitDetails.id != null) {
                         $scope.getCafePartnerStatus($scope.unitDetails.id);
                    }
                }
                $timeout(function () {
                    $scope.getExpiry();
                }, 10000);
            }

            $timeout(function () {
                $scope.getStockOuts();
            }, 10000);

            $scope.analyticsTimerSeconds =  Math.round((new Date() - $rootScope.analyticsRefreshTime)/1000);
            $scope.analyticsIntervalHandle = $interval(function () {
                $scope.analyticsTimerSeconds++;
                if($scope.analyticsTimerSeconds >= (AppUtil.analyticsRefreshInterval * 60)) {
                    AppUtil.refreshAnalyticsData(true);
                    $scope.analyticsTimerSeconds =  Math.round((new Date() - $rootScope.analyticsRefreshTime)/1000);
                }
            },1000)
            // start inactivity timer
            Idle.watch();
        };

        $rootScope.$on('IdleStart', function() {
            // the user appears to have gone idle
            socketUtils.disconnect();
            coverUtils.logOut();
        });

        $scope.redirectToOrderRefundSearch = function() {
            $location.url('/orderRefundSearch');
        };

        $scope.$on('$destroy', function() {
            $interval.cancel($scope.analyticsIntervalHandle);
        });

        $scope.refreshAnalyticsData = function (silent) {
            AppUtil.refreshAnalyticsData(silent);
            $scope.analyticsTimerSeconds =  Math.round((new Date() - $rootScope.analyticsRefreshTime)/1000);
        }

        var interval = $interval(function () {
            if($location.url()==='/cover'){
                $scope.getCafePartnerStatus($scope.unitDetails.id);
            }
        }, 300000); //after every 5 minutes

        $scope.getCafePartnerStatus = function (unitId) {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.cafeLookUp.getCafeStatusForUnit,
                params: {unitId: unitId}
            }).then(function (response) {
                $scope.cafe = response.data;
                $scope.cafeBrand.swiggyChaayos = $scope.cafe[1].swiggyStatus;
                $scope.cafeBrand.swiggyGnT = $scope.cafe[3].swiggyStatus;
                $scope.cafeBrand.zomatoChaayos = $scope.cafe[1].zomatoStatusFromGetApi;
                $scope.cafeBrand.zomatoGnT = $scope.cafe[3].zomatoStatusFromGetApi;
                $rootScope.$broadcast('timer-start',"");

                // if( $scope.cafeBrand.swiggyChaayos===false || $scope.cafeBrand.zomatoChaayos===false || $scope.cafeBrand.zomatoGnT===false){
                //     bootbox.alert("<h3>Cafe is OffLine on Partner Side !!!!! <br/>Please Check </h3>");
                // }
            }), function (error) {
                console.log("error", error);
            }
        }

        $scope.getAutoApplicableOfferForUnit = function () {
            if (localStorage.getItem("autoApplicableOfferForUnit") == null) {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.offers.getAutoApplicableOfferForUnit+"?unitId="+$rootScope.globals.currentUser.unitId
                }).then(function (response) {
                    $scope.autoApplicaleOffer = response.data;
                    if (response.status == 200 && !AppUtil.isEmptyObject(response.data)){
                        localStorage.setItem("autoApplicableOfferForUnit", JSON.stringify($scope.autoApplicaleOffer));
                    }
                }), function (error) {
                    console.log("error" + error);
                }
            } else {
                var offerObj = JSON.parse(localStorage.getItem("autoApplicableOfferForUnit"));
                if (!(new Date(offerObj.startDate)<=new Date()<=new Date(offerObj.endDate))){
                    localStorage.removeItem("autoApplicableOfferForUnit");
                }
            }
        }

        $scope.getPnLStatusMap = function (unitId) {

            if (localStorage.getItem("PnLStatus") !== null) {
                var detail = angular.fromJson(localStorage.getItem("PnLStatus"));
                if (detail.date === AppUtil.getCurrentDate('yyyy-mm-dd')) {
                    $scope.pnlStatusMap = angular.fromJson(localStorage.getItem("PnLStatus"));
                    $scope.pnlStatusMapdata = $scope.pnlStatusMap.data;
                } else {
                    $http({
                        method: 'GET',
                        url: AppUtil.restUrls.posMetaData.getPnLStatusMap,
                        params: {unitId: unitId}
                    }).then(function (response) {
                        $scope.pnlStatusMap.date = AppUtil.getCurrentDate('yyyy-mm-dd');
                        $scope.pnlStatusMap.data = response.data;
                        $scope.pnlStatusMapdata = $scope.pnlStatusMap.data;
                        localStorage.setItem("PnLStatus", JSON.stringify($scope.pnlStatusMap));
                    }), function (error) {
                        console.log("error" + error);
                    }
                }
            } else {
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.posMetaData.getPnLStatusMap,
                    params: {unitId: unitId}
                }).then(function (response) {
                    $scope.pnlStatusMap.date = AppUtil.getCurrentDate('yyyy-mm-dd');
                    $scope.pnlStatusMap.data = response.data;
                    $scope.pnlStatusMapdata = $scope.pnlStatusMap.data;
                    localStorage.setItem("PnLStatus", JSON.stringify($scope.pnlStatusMap));
                }), function (error) {
                    console.log("error" + error);
                }
            }
        }

        $scope.getInventoryData = function () {
            $scope.getSnapShot();
            $scope.getExpiry();
        };

        $rootScope.$watch('lastThreeOrders', function (newVal) {
                if ($cookieStore.get("lastThreeOrders") != undefined
                    && $cookieStore.get("lastThreeOrders").unitId == $scope.unitDetails.id) {
                    $rootScope.lastThreeOrders = newVal;
                    $scope.lastThreeOrderArray = $rootScope.lastThreeOrders;
                }
            }
        );


        $scope.isDev = function () {
            return $location.host().indexOf("prod") != -1 ? false : true;
        };

        $scope.getKettleDayCloseStatus = function (unitId) {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.posMetaData.getKettleDayCloseStatus,
                params: {unitId: unitId}
            }).then(function (response) {
                $scope.kettleDayCloseDone = response.data;
                localStorage.setItem("kettleDayCloseDone", JSON.stringify($scope.kettleDayCloseDone));
                $scope.orderStart();
            }), function (error) {
                console.log("error", error);
            }
        }

        $scope.getSumoDayCloseStatus = function (unitId) {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.scmService.getSumoDayCloseStatus,
                params: {unitId: unitId}
            }).then(function (response) {
                $scope.sumoDayCloseDone = response.data;
                localStorage.setItem("sumoDayCloseDone", JSON.stringify($scope.sumoDayCloseDone));
                $scope.orderStart();
            }), function (error) {
                console.log("error", error);
            }
        }
        function kettleAlert() {
            bootbox.confirm({
                message: 'Kindly Do Previous Day Close For Kettle!',
                classname : "rubberBand animated",
                buttons: {
                    confirm: {
                        label: 'Refresh to Start Order',
                        className: 'btn-success'
                    },
                    cancel: {
                        label: 'Close',
                        className: 'btn-danger'
                    }
                },
                callback: function (result) {
                    if(result){
                        $scope.getKettleDayCloseStatus($scope.unitDetails.id);
                    }
                }
            });
        }

        function sumoAlert() {
            bootbox.confirm({
                message: 'Kindly Do Previous Day Close For Sumo!',
                classname : "rubberBand animated",
                buttons: {
                    confirm: {
                        label: 'Refresh to Start Order',
                        className: 'btn-success'
                    },
                    cancel: {
                        label: 'Close',
                        className: 'btn-danger'
                    }
                },
                callback: function (result) {
                    if(result){
                        $scope.getSumoDayCloseStatus($scope.unitDetails.id);
                    }
                }
            });
        }
        $scope.orderStart = function () {
            if (AppUtil.transactionMetadata.disableCafeToOperate) {
                var kettle = JSON.parse(localStorage.getItem("kettleDayCloseDone"));
                var sumo = JSON.parse(localStorage.getItem("sumoDayCloseDone"));
                if (_.isEmpty(kettle)) {
                    kettleAlert();
                    return;
                } else if (!kettle.dayCloseStatus) {
                    kettleAlert();
                    return;
                } else if (_.isEmpty(sumo) || (!sumo.dayCloseStatus)) {
                    sumoAlert();
                    return;
                }
            }
            if($scope.unitDetails.posVersion !=1){
               bootbox.alert("<h3>Ordering is Disabled ..!</h3>");
               console.log("cscsd ",AppUtil.restUrls.kettleUIV2.kettleUIV2Url);
                return;
            }
            if (!AppUtil.isAndroid && !AppUtil.checkIsMonk() && !$rootScope.printerStatus) {
                bootbox.alert("<h3>Qz is not running!<br/>Please run Qz Tray</h3>");
            } else if (AppUtil.getPendingStockCalendarEvent() == true) {
                bootbox.alert("<h3>You have pending weekly/monthly SuMo Inventory, close it</h3>");
            } else {
                AppUtil.getUnitProductsData(function () {
                    if (desiChaiService.getDesiChaiProducts().length == 0) {
                        desiChaiService.setRecipes();
                    }
                    //$location.url('/pos');

                    $rootScope.orderType = "order";
                    AppUtil.sendOrderStart();
                    if(AppUtil.unitDetails.categoryMap) {
                        var categoryMap = AppUtil.unitDetails.categoryMap;
                            categoryMap[1].push(99999);
                        localStorage.setItem("categoryMap", JSON.stringify(categoryMap));
                        console.log(localStorage.getItem("categoryMap"));
                    }else if(!localStorage.getItem("categoryMap")) {
                            localStorage.setItem("categoryMap", JSON.stringify({}));
                        }
                    $scope.goToPosScreen();
                });


                // console.log(AppUtil.unitDetails.categoryMap);
                // if(AppUtil.unitDetails.categoryMap) {
                //     var categoryMap = AppUtil.unitDetails.categoryMap;
                //     categoryMap[1].push(99999);
                //     localStorage.setItem("categoryMap", JSON.stringify(categoryMap));
                //
                // }
                // else if(!localStorage.getItem("categoryMap")) {
                //     localStorage.setItem("categoryMap", JSON.stringify({}));
                // }

            }
        };


        $scope.checkTable = function () {
            if (!AppUtil.isAndroid && !AppUtil.checkIsMonk() && !$rootScope.printerStatus) {
                bootbox.alert("<h3>Qz is not running!<br/>Please run Qz Tray</h3>");
            } else {
                AppUtil.getUnitProductsData(function () {
                    if (desiChaiService.getDesiChaiProducts().length == 0) {
                        desiChaiService.setRecipes();
                    }
                    $rootScope.orderType = "order";
                    $scope.goToTableScreen();
                });
            }
        };


        $scope.employeeMealStart = function () {
            AppUtil.getUnitProductsData(function () {
                desiChaiService.setRecipes();
                //$location.url('/pos');
                $rootScope.orderType = "employee-meal";
                $scope.goToPosScreen();
            });
        };

        $scope.paidEmployeeMealStart = function () {
            AppUtil.getUnitProductsData(function () {
                desiChaiService.setRecipes();
                //$location.url('/pos');
                AppUtil.getEmployeeMealProductsData(function () {
                    $rootScope.orderType = "paid-employee-meal";
                    $scope.goToPosScreen();
                });
            });
        };

        $scope.pendingMilkRoInterval = $interval(function () {
            $scope.getPendingSpecialRos(false, true);
        }, 900000); //interval to run for every 15 minutes

        function clearIntervalOfMilkAndBread() {
            $interval.cancel($scope.pendingMilkRoInterval);
        }

        $scope.showPendingMilkBreadAlert = function (data,hardRefresh, timer) {
            var currentDate = new Date(data.maxLimitTime);
            var dateOptions = {
                hour: 'numeric',
                minute: 'numeric',
                hour12: true,
                day: 'numeric',
                month: 'short',
                year: 'numeric'
            };
            var formattedDate = currentDate.toLocaleString('en-US', dateOptions);
            var msg = "You have <b>"+ data.roIds.length + "</b> pending Milk GR's.<br> You will be unable to punch any Chai order after <b>" + formattedDate + "</b><br>" +
                 "Log in to SUMO to complete your pending milk GR's.<b>Contact your AM/DAM if having any issues.</b>";
            if (hardRefresh != undefined && hardRefresh != null && hardRefresh) {
                bootbox.alert(msg);
                return;
            }
            if (timer != undefined && timer != null && timer) {
                bootbox.alert(msg);
                $rootScope.lastMilkBreadAlertTime = new Date();
                return;
            }
            if ($rootScope.lastMilkBreadAlertTime != undefined && $rootScope.lastMilkBreadAlertTime != null) {
                var timeDiffInSec = Math.round((new Date() - $rootScope.lastMilkBreadAlertTime) / 1000);
                if (timeDiffInSec > 905) {
                    bootbox.alert(msg);
                    $rootScope.lastMilkBreadAlertTime = new Date();
                }
            } else {
                bootbox.alert(msg);
                $rootScope.lastMilkBreadAlertTime = new Date();
            }
        };

        $scope.milkCountDownInterval = $interval(function() {
            $scope.displayTimeMilkBread = "BLOCKED" ;
            var seconds = 0;
            if ($rootScope.pendingMilkBread != undefined && $rootScope.pendingMilkBread != null) {
                if (!$rootScope.pendingMilkBread.receivingDone) {
                    var max = new Date($rootScope.pendingMilkBread.maxLimitTime);
                    var currentTime = new Date();
                    if (max > currentTime) {
                        var currTime = new Date().getTime() / 1000;
                        var maxLimit = new Date(max).getTime() / 1000;
                        seconds = maxLimit - currTime;
                    } else {
                        $scope.displayTimeMilkBread = "BLOCKED";
                        $interval.cancel($scope.milkCountDownInterval);
                    }

                    var hours = Math.floor(seconds / 3600);
                    var remainingSeconds = seconds % 3600;
                    var minutes = Math.floor(remainingSeconds / 60);
                    remainingSeconds = (remainingSeconds % 60).toFixed(0);

                    if (remainingSeconds < 10) {
                        remainingSeconds = "0" + remainingSeconds;
                    }
                    if (minutes < 10) {
                        minutes = "0" + minutes;
                    }

                    $scope.displayTimeMilkBread = hours + " H : " + minutes + " M: " + remainingSeconds + " S";
                    if (seconds === 0) {
                        $scope.displayTimeMilkBread = "BLOCKED";
                        $interval.cancel($scope.milkCountDownInterval);
                    }
                } else {
                    $interval.cancel($scope.milkCountDownInterval);
                }
            }
        }, 1000);

        $scope.getPendingSpecialRos = function (hardRefresh, timer) {
            if ($rootScope.pendingMilkAndBread != undefined && $rootScope.pendingMilkAndBread != null && $rootScope.pendingMilkBread.receivingDone) {
                clearIntervalOfMilkAndBread();
                return;
            }
            sendPendingMilkBreadRosRequest(function (result) {
                if (result == null) {
                    $scope.pendingMilkAndBread = {
                        "pendingRos" : {},
                        "receivingDone": true
                    };
                } else {
                    $scope.pendingMilkAndBread = result;
                    $scope.pendingMilkAndBread["timeString"] = AppUtil.formatDate(result.maxLimitTime, "yyyy-MM-dd hh:mm:ss");
                }
                $rootScope.pendingMilkBread = angular.copy($scope.pendingMilkAndBread);
                if ($scope.pendingMilkAndBread.receivingDone) {
                    clearIntervalOfMilkAndBread();
                } else {
                    $scope.showPendingMilkBreadAlert($scope.pendingMilkAndBread, hardRefresh, timer);
                }
            });
        };

        $scope.getStockOuts = function () {
            $scope.inventoryAndGR = {};
            if ($scope.unitDetails == null && $scope.unitDetails.id == null) {
                 return;
             }
            posAPI.allUrl('/', AppUtil.restUrls.posMetaData.stockouts)
                .post($scope.unitDetails.id).then(function (response) {
                $scope.inventoryAndGR['stockouts'] = response;
                sendPendingGRRequest(function (result) {
                    if (result != null) {
                        $scope.inventoryAndGR['grs'] = result;
                    } else {
                        $scope.inventoryAndGR['grs'] = {pendingGR: 0, specialGR: 0};
                    }
                }, 0);
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        $scope.getSnapShot = function () {
            if ($scope.unitDetails == null && $scope.unitDetails.id == null) {
                return;
            }
            $scope.snapShotData = null;
            var url;
            var unitZone=JSON.parse(localStorage.getItem('unitDetails')).unitZone;
            var InventoryAPI=APIJson.urls.inventory;
            if(unitZone!=null){
                url=InventoryAPI.baseurl+"/"+unitZone.toLowerCase()+APIJson.POST_FIX+InventoryAPI.scmSnapShot;
            }else{
                url=InventoryAPI.baseurl+"/north"+APIJson.POST_FIX+InventoryAPI.scmSnapShot;
            }
            posAPI.allUrl('/', url).customGET("", {unitId: $scope.unitDetails.id})
                .then(function (response) {
                    $scope.snapShotData = response;
                    $scope.recalculateSnapShot();
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        };

        $scope.getExpiry = function () {
            if ($scope.unitDetails == null && $scope.unitDetails.id == null) {
                return;
            }
            $scope.expiryData = [];
            var url;
            var unitZone=JSON.parse(localStorage.getItem('unitDetails')).unitZone;
            var InventoryAPI = APIJson.urls.inventory;
            if(unitZone!=null){
                console.log(unitZone.toLowerCase());
                url=InventoryAPI.baseurl+"/"+unitZone.toLowerCase()+InventoryAPI.postfix+InventoryAPI.scmProductsExpiry;
            }else{
                url=InventoryAPI.baseurl+"/north"+APIJson.postfix+InventoryAPI.scmProductsExpiry;
            }
            posAPI.allUrl('/', url).customGET("", {unitId: $scope.unitDetails.id})
                .then(function (response) {
                    $scope.expiryData = response;
                    $scope.recalculateSnapShot();
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        };


        $scope.orderByCost = function (v1) {
            return v1.qty * v1.price;
        };

        $scope.recalculateSnapShot = function () {
            if (AppUtil.isEmptyObject($scope.expiryData)) {
                return;
            }
            if (AppUtil.isEmptyObject($scope.snapShotData)) {
                return;
            }
            var i = 0;
            var j = 0;
            var e = null;
            var s = null;
            var currentCost = 0;
            var currentCostChaayos = 0;
            var currentCostGnT = 0;
            var reChaayosGnt = new RegExp(".*gnt.*");
            var chaayosSpecific = {};
            chaayosSpecific.inventory = [];
            var gntSpecific = {};
            gntSpecific.inventory = [];
            var result = {};
            result.inventory = [];
            for (i in $scope.expiryData) {
                e = $scope.expiryData[i];
                if (!AppUtil.isEmptyObject(e)) {
                    for (j in $scope.snapShotData.inventory) {
                        s = $scope.snapShotData.inventory[j];
                        if (e.id == s.id) {
                            s.curQty = e.exQty;
                            currentCost = currentCost + (e.exQty * s.price);
                            result.inventory.push(s);
                            if (reChaayosGnt.test((s.name).toLowerCase())){
                                currentCostGnT = currentCost + (e.exQty * s.price);
                                gntSpecific.inventory.push(s);
                            }
                            else{
                                currentCostChaayos = currentCost + (e.exQty * s.price);
                                chaayosSpecific.inventory.push(s);
                            }
                        }
                    }
                }
            }
            result.currentCost = currentCost;
            result.currentCostChaayos = currentCostChaayos;
            result.currentCostGnT = currentCostGnT;
            result.totalCost = $scope.snapShotData.totalCost;
            result.percentageAchieved = (result.totalCost - result.currentCost) / result.totalCost * 100;
            result.percentageAchievedGnT = (result.totalCost - result.currentCostChaayos) / result.totalCost * 100;
            result.percentageAchievedChaayos = (result.totalCost - result.currentCostGnT) / result.totalCost * 100;
            $scope.snapShot = result;
            $scope.snapShotChaayos = chaayosSpecific;
            $scope.snapShotGnT = gntSpecific;
            if ($scope.snapShotGnT.inventory.length==0){
                $scope.snapShotGnT=null;
            }
            if ($scope.snapShotChaayos.inventory.length==0){
                $scope.snapShotChaayos=null;
            }
            console.log($scope.snapShotChaayos);
            console.log($scope.snapShotGnT);
        };

        $scope.complimentaryOrderStart = function () {
            AppUtil.getUnitProductsData(function () {
                AppUtil.setSelectedBrand(AppUtil.getBrandByBrandId(AppUtil.CHAAYOS_BRAND_ID));
                desiChaiService.setRecipes();
                //$location.url('/pos');
                $rootScope.orderType = "complimentary-order";
                $scope.goToPosScreen();
            });
        };

        $scope.goToPosScreen = function () {
            if (!AppUtil.isEmptyObject(AppUtil.unitDetails.products)) {
                $location.url('/pos');
            }
        };

        $scope.goToTableScreen = function () {
            if (!AppUtil.isEmptyObject(AppUtil.unitDetails.products)) {
                $location.url('/table');
            }
        };

        $scope.wastageOrderStart = function () {
            AppUtil.getUnitProductsData(function () {
                desiChaiService.setRecipes();
                $rootScope.orderType = "wastage-order";
                $location.url('/pos');
            });
        };

        $scope.$on('$destroy', function (event) {
            $interval.cancel($scope.stopTime);
            socketUtils.removeAllListeners();
        });


        function checkSocketStatus() {
            socketUtils.pairingDone(function (msg) {
                //console.log("pairing successful");
                $scope.customerScreen = msg;
                socketUtils.setPairingStatus(msg);
            });

            socketUtils.pairingFailed(function (msg) {
                //console.log("pairing failed");
                $scope.customerScreen = !msg;
                socketUtils.setPairingStatus(!msg);
            });

            $scope.customerScreen = socketUtils.getPairingStatus();
        }

        function timerClear() {
            $scope.$broadcast('timer-clear');
        }

        $scope.csLogOut = function () {
            var unitid = $rootScope.globals.currentUser.unitId;
            var reqObj = {
                unit: unitid,
                terminalId: $rootScope.globals.currentUser.terminalId
            };
            posAPI.allUrl('/', AppUtil.restUrls.customer.logout)
                .post(reqObj)
                .then(
                    function (response) {
                        //console.log(response);
                        if (response) {
                            AppUtil
                                .mySuccessAlert('Customer logged out successfully');
                        }
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
        };

        $scope.openOrders = function () {
            $location.url('/openTakeawayOrders');
        };

        $scope.openDeliveryOrders = function () {
            $location.url('/openDeliveryOrders');
        };

        $scope.manageRider = function () {
            $location.url('/manageRider');
        };

        $scope.trackExpenses = function () {
            $location.url('/expenseTracking');
        };

        $scope.paymentRefund = function () {
            $location.url('/paymentRefund');
        };

        $scope.loginFormsSystem = function () {
            console.log(AppUtil.restUrls.users.encryptAuth);
            posAPI.allUrl('/', AppUtil.restUrls.users.encryptAuth).post().then(function (response) {
                //TODO redirect to application
                $window.open(AppUtil.restUrls.forms.autoLogin + "?key=" + encodeURIComponent(response), '_blank');
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            })
        };

        $scope.openItemConsumptionModal = function () {
            bootbox.confirm("Are you Sure you want to generate item Consumption report?", function (result) {
                if (result == true) {
                    getItemConsumptionObj();
                }
            });
        };

        function getItemConsumptionObj() {
            var requestObj = $rootScope.globals.currentUser;
            var returnObj = null;
            posAPI.allUrl('/', AppUtil.restUrls.order.consumptionReport).post(
                requestObj).then(function (response) {
                returnObj = response.plain();
                openItemConsumptionModal(returnObj.data);
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);

            })
        }

        function openItemConsumptionModal(obj) {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/itemConsumptionModal.html',
                controller: 'itemConsumptionModal',
                backdrop: 'static',
                size: 'md',
                resolve: {
                    itemConsumptionObj: function () {
                        return obj;
                    }
                }
            });
        }

        $scope.supportLink = function () {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/supportLink.html',
                controller: 'supportLink',
                backdrop: 'static',
                size: 'lg'
            });
        };

        $scope.openSettlementTypeObj = function () {
            bootbox.confirm("Are you Sure you want to generate the settlement report?", function (result) {
                if (result == true) {
                    getSettlementTypeObj();
                }
            });
        };

        $scope.openTerminalSettlementTypeObj = function () {
            bootbox.confirm("Are you Sure you want to generate the settlement report?", function (result) {
                if (result == true) {
                    getTerminalSettlementTypeObj();
                }
            });
        };

        function getSettlementTypeObj() {
            $rootScope.showFullScreenLoader = true;
            var requestObj = $rootScope.globals.currentUser;
            var returnObj = null;
            posAPI.allUrl('/', AppUtil.restUrls.order.settlementReport)
                .post(requestObj).then(function (response) {
                returnObj = response.plain();
                openSettlementModal(returnObj.data);
                $rootScope.showFullScreenLoader = false;
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                $rootScope.showFullScreenLoader = false;
            })
        }

        function getTerminalSettlementTypeObj() {
            var requestObj = $rootScope.globals.currentUser;
            var returnObj = null;
            posAPI.allUrl('/', AppUtil.restUrls.order.terminalSettlementReport)
                .post(requestObj).then(function (response) {
                returnObj = response.plain();
                //console.log(returnObj.data);
                openSettlementModal(returnObj.data);
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);

            })
        }

        function openSettlementModal(obj) {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/settlementTypeModal.html',
                controller: 'settlementTypeModal',
                backdrop: 'static',
                size: 'md',
                resolve: {
                    settlementTypeObj: function () {
                        return obj;
                    }
                }
            });
        }

        function openPrintModal() {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/printModal.html',
                controller: 'printModal',
                backdrop: 'static',
                size: 'lg'
            });
        }

	function openRawPrintModal() {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/rawPrintModal.html',
                controller: 'rawPrintModal',
                backdrop: 'static',
                size: 'lg'
            });
        }
        $scope.generateManagerReport = function () {
            //var requestObj = $cookieStore.get('globals').currentUser;
            var returnObj = null;
            posAPI.allUrl('/', AppUtil.restUrls.posMetaData.allReports)
                .post($rootScope.globals.currentUser.unitId).then(function (response) {
                returnObj = response.plain();
                //console.log(returnObj);
                openManagersModal(returnObj);
            }, function (err) {
                //console.log(err);
            });
        };

        function openManagersModal(obj) {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/managersreportModal.html',
                controller: 'managersreportCtrl',
                backdrop: 'static',
                size: 'lg',
                resolve: {
                    managersreportObj: function () {
                        //console.log(obj);
                        return obj;
                    }
                }
            });
        }

        function last3Orders() {
            coverUtils.last3Orders();
        }

        $scope.openOrderSearch = function (generateOrderId) {
            var modalInstance = $modal.open({
                templateUrl: window.version + 'scripts/modules/modals/lastOrderModal.html',
                controller: 'lastOrderModalController',
                backdrop: 'static',
                resolve: {
                    generateOrderId: function () {
                        return generateOrderId;
                    }
                }
            });
            modalInstance.result.then(function () {
            }, function (err) {
            });
        };

        // logout
        $scope.logOut = function () {
            socketUtils.disconnect();
            coverUtils.logOut();
        };
        $scope.markAttendance = function () {
            if($scope.customerScreen) {
                socketUtils.emitMessage({ATTENDANCE_START: null});
            }else{
                bootbox.alert("Customer  screen is not connected!");
            }
        }

        function getSaturdays(year, month) {

            var day;
            var date;
            var saturdays = [];
            day = 1;
            date = new Date(year, month, day);
            while (date.getMonth() === month) {
                if (date.getDay() === 6) { // Sun=0, Mon=1, Tue=2, etc.
                    saturdays.push(new Date(year, month, day).getDate());
                }
                day += 1;
                date = new Date(year, month, day);
            }
            return saturdays;
        }


        // day close
        $scope.dayCloseModalOpen = function () {
            $scope.checkPendingGR(function (response) {

                if (response) {

                    var today = new Date();
                    var sat = getSaturdays(today.getFullYear(), today.getMonth())
                    var saturdays = [];
                    for(var i =0; i<sat.length;i++){
                        if(i%2!==0){
                            saturdays.push(sat[i]);
                        }
                    }
                    if(!(today.getDay() === 0 || saturdays.includes(today.getDate()))){
                        var url = AppUtil.restUrls.posMetaData.pendingUnitPullTransfer+'?unitId='+$rootScope.globals.currentUser.unitId;
                        console.log(url);
                        posAPI.allUrl('/',url).post()
                            .then(function (response) {
                                if(response.errorType != undefined){
                                    AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                                }
                                if(response.UnitPullTransfersList.length >0){
                                    $scope.showPullTransferOptions = true;
                                    $scope.unitPullTransfers = response.UnitPullTransfersList;
                                }
                            },function (err){
                                console.log(err);
                            });
                        posAPI.allUrl('/',AppUtil.restUrls.posMetaData.pullUnitReason).customGET()
                            .then(function (response){
                                $scope.DayCloseReasonList = response.UnitPullReasonList;
                                console.log($scope.DayCloseReasonList);
                            },function (err){
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                    }
                    $timeout(function () {

                        $modal.open({
                            animation: true,
                            templateUrl: window.version + 'views/dayCloseModal.html',
                            controller: 'DayCloseModalCtrl',
                            backdrop: 'static',
                            scope: $scope,
                            size: 'lg',
                            resolve: {
                                showPullTransferOptions: function () {
                                    return $scope.showPullTransferOptions;
                                },
                                DayCloseReasonList: function () {
                                    return $scope.DayCloseReasonList;
                                },
                                unitPullTransfers: function () {
                                    return $scope.unitPullTransfers;
                                }
                            }
                        });
                    }, 1000);
                } else {
                    bootbox.alert("You have pending GRs in SUMO. Please close all the pending GRs in Sumo first!");
                }
            });
        };

        // cancel day close
        $scope.cancelDayCloseModalOpen = function () {
            bootbox.confirm("Do you want to cancel the day close?", function (result) {
                if (result == true) {
                    cancelDayCloseRequest();
                    // bootbox.alert("Cancelled Successful");
                }
            });
        };

        function cancelDayCloseRequest() {
            $rootScope.showFullScreenLoader = true;

            var requestObj = {
                unitId: $rootScope.globals.currentUser.unitId,
                userId: $rootScope.globals.currentUser.userId
            };

            posAPI.allUrl('/', AppUtil.restUrls.posMetaData.cancelDayClose).post(requestObj)
                .then(function (response) {
                    console.log(response);
                    if (response.errorType != undefined) {
                        AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                        bootbox.alert(response.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        if (response) {
                            AppUtil.mySuccessAlert("Day Close Cancelled Successfully!");
                            bootbox.alert("Day Close Cancelled Successfully");
                            $rootScope.showFullScreenLoader = false;
                        } else {
                            AppUtil.myInfoAlert("Error while day close, Please consult Technical Team");
                            bootbox.alert("Error while day close, Please consult Technical Team");
                            $rootScope.showFullScreenLoader = false;
                        }
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
        };

        $scope.checkPendingGR = function (callback) {
            sendPendingGRRequest(function (result) {
                if (result == null) {
                    callback(true);
                } else {
                    var grs = 0;
                    Object.values(result).forEach(function (value) {
                        grs += value;
                    });
                    callback(grs <= 0);
                }
            }, -2);
        };

        function sendPendingMilkBreadRosRequest(callback) {
            $http({
                method: "GET",
                url: AppUtil.restUrls.scmService.getPendingSpecialRos,
                params: {
                    unitId: $rootScope.globals.currentUser.unitId,
                }
            }).then(function success(response) {
                if (response.status == 200) {
                    var result = response.data;
                    if (typeof callback == "function") {
                        callback(result);
                    }
                } else {
                    if (typeof callback == "function") {
                        callback(null);
                    }
                }
            }, function error(err) {
                if (typeof callback == "function") {
                    callback(null);
                }
                console.log("error occurred while getting Pending Milk and Bread :: ",err);
            });
        }

        function sendPendingGRRequest(callback, daysInPast) {
            var params = {
                unit: $rootScope.globals.currentUser.unitId,
                daysInPast: daysInPast
            };

            $http({
                method: "GET",
                url: AppUtil.restUrls.scmService.pendingGRs,
                params: params
            }).then(function success(response) {
                if (response.status == 200) {
                    var result = response.data;
                    if (typeof callback == "function") {
                        callback(result);
                    }
                } else {
                    if (typeof callback == "function") {
                        callback(null);
                    }
                }
            }, function error(err) {
                if (typeof callback == "function") {
                    callback(null);
                }
            });
        }

        // order search
        $scope.openOrderSearchScreen = function () {
            coverUtils.openOrderSearchScreen();
        };

        // order summary
        $scope.goToOrderSummary = function () {
            coverUtils.goToOrderSummary();
        };

        $scope.showMTDPnL = function () {
            $location.url('/PnLReport')
        };

        $scope.goToEmployeeMealSummary = function () {
            coverUtils.goToEmployeeMealSummary();
        };

        // partnerOrderDashboard
        $scope.partnerOrderDashboard = function () {
            $location.url('/partnerOrderDashboard');
        };

        // order summary
        $scope.goToCustomerOrderSummary = function () {
            AppUtil.orderSummaryObj = {};
            coverUtils.goToCustomerOrderSummary();
        };
        // test printer

        $scope.testPrinter = function () {
            coverUtils.testPrinter();
        };

        $scope.testPrint = function (htmlText) {
            openPrintModal();
        };

		$scope.testRawPrint = function () {
            openRawPrintModal();
        };

        $scope.openInventoryScreen = function () {
            coverUtils.openInventoryScreen();
        };

        $scope.showPullManagement = function () {
            $location.url('/pullManagement');
        };

        $scope.showPullSettlement = function () {
            $location.url('/pullSettlement');
        };

        $scope.showSettlementsViewScreen = function () {
            $location.url('/pullSettlementsView');
        };

        $scope.resetConfig = function () {
            AppUtil.removeAutoConfigData();
            $scope.logOut();
        };
        $scope.viewManualBillBookDetails = function () {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: window.version + 'views/manualBillBookDetailsModal.html',
                controller: 'manualBillBookController',
                backdrop: 'static',
                keyboard: false,
                scope: $scope,
                size: "lg"
            });
        };

        $scope.refreshProductsData = function () {
            AppUtil.clearProductCache();
        };

        $scope.meterReadingDetails = function () {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: window.version + 'views/meterReadingDetailsModal.html',
                controller: 'meterReadingDetailsController',
                backdrop: 'static',
                keyboard: false,
                scope: $scope,
                size: "lg"
            });
        };

        $scope.paymentRefund = function () {
            $location.url('/paymentRefund');
        };

        $scope.updateHandOverDate = function () {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: window.version + 'views/handOverDateModal.html',
                controller: 'handOverDateController',
                backdrop: 'static',
                keyboard: false,
                scope: $scope,
                size: "lg"
            });
        };
    }
})();

angular
    .module('posApp').controller('manualBillBookController', ['$rootScope', '$scope', 'AppUtil', '$http', '$modalInstance', 'posAPI',
    function ($rootScope, $scope, AppUtil, $http, $modalInstance, posAPI) {
        var unitId = $rootScope.globals.currentUser.unitId;

        function init() {
            // var reChaayos = new RegExp(".*GnT.*");
            // var strGnt = "kadai GnT";
            // var strChaayos = "Chaayos";
            // if (reChaayos.test(strGnt)){
            //     console.log("Valid");
            // }
            // else{
            //     console.log("NOt Valid");
            // }
            // if (reChaayos.test(strChaayos)){
            //     console.log("NOt Valid");
            // }
            // else{
            //     console.log("Valid");
            // }
            $scope.unitDetails = AppUtil.getUnitDetails();
            $scope.createdManualBillBookDetails = [];
            $scope.activatedManualBillBookDetails = [];
            getManualBillBookDetails();
            $scope.loader = {
                loading: false,
            };
            showloader();
        }

        function getManualBillBookDetails() {
            posAPI.allUrl('/', AppUtil.restUrls.manualBillBook.getDetailsForUnit)
                .post(unitId)
                .then(
                    function (response) {
                        var resp = response.plain();
                        for (var i = 0; i < resp.length; i++) {
                            if (resp[i].status == "CREATED") {
                                $scope.createdManualBillBookDetails.push(resp[i]);
                            } else {
                                resp[i].totalBill = parseInt(resp[i].endNo) - parseInt(resp[i].startNo) + 1;
                                $scope.activatedManualBillBookDetails.push(resp[i]);
                                resp[i].remainingBillCount = resp[i].totalBill - resp[i].usedBillCount;
                            }
                        }
                        hideloader();
                    },
                    function (err) {
                        hideloader();
                        AppUtil
                            .myAlert(err.data.errorMessage);
                    });
        };

        $scope.activateManualBillBook = function (manualBillBook) {
            bootbox.confirm("Are you sure to activate this Bill Book as this will deactivate your current Bill Book?", function (result) {
                if (result == true) {

                    posAPI.allUrl('/', AppUtil.restUrls.manualBillBook.updateBillForUnit)
                        .post(manualBillBook)
                        .then(
                            function (response) {
                                var resp = response;
                                if (resp) {
                                    init();
                                }
                            },
                            function (err) {
                                AppUtil
                                    .myAlert(err.data.errorMessage);
                            });
                }
            });
        };
        $scope.gstNoChangeListener=function(gstNo){
            $scope.gstNo=gstNo;
        }
        $scope.validateGst=function(){
            $scope.gstNo.toUpperCase();
            console.log($scope.unitDetails.tin);
            var result=angular.equals( $scope.gstNo.toUpperCase(),$scope.unitDetails.tin);
            $scope.matchesGst=result;
             if(!result){
                 bootbox.alert("GST number is not correct");
             }

        }
        $scope.closeModal = function () {
            $modalInstance.close();
        };

        function showloader() {
            $scope.loader.loading = true;
        }

        function hideloader() {
            $scope.loader.loading = false;
        }

        init();
    }]
);

angular
    .module('posApp').controller('lastOrderModalController', ['$rootScope', '$scope', 'AppUtil', '$http', '$modalInstance', 'posAPI', '$modal', '$location', 'generateOrderId',
    function ($rootScope, $scope, AppUtil, $http, $modalInstance, posAPI, $modal, $location, generateOrderId) {
        $scope.lastOrderId = generateOrderId;
        $scope.OrderObj = {};
        $scope.init = function () {
            loadOrderObj();
        };

        function loadOrderObj() {
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/', AppUtil.restUrls.order.generatedOrder).post($scope.lastOrderId)
                .then(function (response) {
                    AppUtil.OrderObj = {};
                    AppUtil.OrderObj = response.plain();
                    $scope.OrderObj = response.plain();
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    AppUtil.myAlert(err.data.errorMessage);
                });
        }

        $scope.openOrderSearch = function () {
            $location.url('/orderSearch');
            $scope.closeModal();
        };

        $scope.printOrder = function (type) {
            if (!AppUtil.isEmptyObject($scope.OrderObj)) {
                ////console.log("Print Count :"+$scope.OrderObj.printCount);
                if ($scope.OrderObj.printCount < 2 || type == 'KOT') {
                    $rootScope.showFullScreenLoader = true;
                    if (type == 'KOT') {
                        AppUtil.reprintOrderKOT($scope.lastOrderId);
                    } else {
                        AppUtil.reprintOrder($scope.lastOrderId).then(function () {
                            $scope.GetSearchedOrder();
                        });
                    }
                } else {
                    AppUtil.myAlert('Reprints for orders are limited to only once');
                }

            } else {
                AppUtil.myAlert('Please fill a valid order id');
            }
        };

        $scope.GetSearchedOrder = function () {
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/', AppUtil.restUrls.order.generatedOrder).post($scope.lastOrderId)
                .then(function (response) {
                    if (!angular.isUndefined(response.errorType)) {
                        AppUtil.myAlert(response.errorMessage);
                    } else {
                        AppUtil.OrderObj = {};
                        AppUtil.OrderObj = response.plain();
                        $scope.OrderObj = response.plain();
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;

                });
        };

        $scope.closeModal = function () {
            $modalInstance.close();
        };

    }]
);

