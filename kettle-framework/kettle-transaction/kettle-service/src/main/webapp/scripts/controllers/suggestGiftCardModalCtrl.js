/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp').controller('SuggestGiftCardCtrl',
    ['$scope', '$http', 'APIJson', '$modalInstance', 'AppUtil', '$rootScope', 'posAPI', 'productService', 'giftObject', 'giftCard','subscriptionDetails','totalAmount','offerApplied','amountPayable','orderDetail','socketUtils','$timeout',
        function ($scope, $http, APIJson, $modalInstance,  AppUtil, $rootScope, posAPI, productService, giftObject, giftCard,subscriptionDetails,totalAmount,offerApplied,amountPayable,orderDetail,socketUtils,$timeout) {
            $scope.cardType = giftObject.type;
            $scope.customerPendingCardInfo = giftCard;
            $scope.cardAmount = null;
            $scope.prepaidAmount = amountPayable;
            $scope.amountPayable = amountPayable;
            $scope.totalAmount = totalAmount;
            $scope.subscriptionDetails = subscriptionDetails;
            $scope.offerApplied = offerApplied;
            $scope.totalWalletAmountShow = parseInt(0);
            $scope.totalWalletAmountShowOffer = parseInt(0);
            $scope.customerWalletAmountLeft = 0;
            $scope.orderWithoutWallet = orderDetail;
            $scope.isSubscriptionProduct = false;
            var giftCardId = [1000248,1000007, 1026, 1027, 1048, 1000159,2];
            $scope.disableGiftCardId = [1026,1027,1048,1000159,2];
            var giftCardNumberList = [];
            $scope.giftCardsList = [];
            $scope.notEditable = {};
            $scope.cardNumber = {};
            var card = {};
            $scope.cardList = [{"name": "E-Card", "code": "ECARD"}];
            $scope.voucherList = [0];
            $scope.vouherNumbers = {};
            $scope.vouherNumbers[1] = "";
            $scope.totalValue = 0;
            $scope.totalValueFlag = false;
            $scope.giftCardsArray = [5000, 2000, 1000, 500, 100, 50,1];
            $scope.giftCardOfferArray = [0, 0, 0, 0, 0,0, 0];
            $scope.quantityAmountArray = [0, 0, 0, 0, 0, 0, 0];
            $scope.giftCards = {};
            $scope.amountPresentFlag = [false, false, false, false, false,false,false];
            $scope.customerFinalPayableAmount=0;
            $scope.customerFinalExtraAmount=0;
            $scope.deletableCardEntry=[];
            $rootScope.isWalletSavingsScreenOpen = false;
            var walletFlow = "WALLET_SUGGESTION_VERSION_1";
            $rootScope.walletSuggestionAttribute = "WALLET_SUGGESTION_VERSION_1";
            $scope.activeDenominationMap ={};
            $scope.init = function () {
                $scope.backupCurrentState();
                getGiftCards();
                getCardOffer();
                console.log($scope.giftCardsArray);
                console.log($scope.amountPresentFlag);
                // var flowNo = AppUtil.generateRandomNumber(1,100);
                // if(flowNo%2 === 0){
                //     walletFlow = "WALLET_SUGGESTION_VERSION_2";
                //     $rootScope.walletSuggestionAttribute = "WALLET_SUGGESTION_VERSION_2";
                // }
                $scope.suggestGiftCardAmount($scope.isSubscriptionActive());

                $scope.showOfferPrompt();

            };

            function getCardOffer() {
                $scope.giftCardOfferInfo = AppUtil.giftCardOffers; // to get offer which are on cover page
                $scope.giftCardOfferInfo.forEach(function (data) {
                    $scope.giftCards[data.denomination] = (data.denomination * data.suggestWalletOffer) / 100;
                    if (data.denomination == 5000) {
                        $scope.amountPresentFlag[0] = true;
                        $scope.giftCardOfferArray[0] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[0];
                    } else if (data.denomination == 2000) {
                        $scope.amountPresentFlag[1] = true;
                        $scope.giftCardOfferArray[1] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[1];
                    } else if (data.denomination == 1000) {
                        $scope.amountPresentFlag[2] = true;
                        $scope.giftCardOfferArray[2] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[2];
                    } else if (data.denomination == 500) {
                        $scope.amountPresentFlag[3] = true;
                        $scope.giftCardOfferArray[3] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[3];
                    } else if (data.denomination == 100) {
                        $scope.amountPresentFlag[4] = true;
                        $scope.giftCardOfferArray[4] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[4];
                    }else if (data.denomination == 50) {
                        $scope.amountPresentFlag[5] = true;
                        $scope.giftCardOfferArray[5] = ((data.denomination * data.suggestWalletOffer) / 100);
                        $scope.activeDenominationMap[data.denomination] = $scope.giftCardOfferArray[5];
                    }
                });

            };


            $scope.getVoucherList = function () {
                return $scope.voucherList;
            };

            $scope.updateVoucher = function (action) {
                if (action == 'add') {
                    $scope.voucherList.push($scope.voucherList.length);
                } else if (action == 'remove') {
                    if ($scope.voucherList.length == 1) {
                        AppUtil.myAlert("One vouhcer required.");
                        return false;
                    }
                    $scope.voucherList.splice($scope.voucherList.length - 1, 1);
                }
            };

            function getGiftCards() {
                //if ($scope.cardType == 'ECARD') {
                AppUtil.setCardType('ECARD');
                //} else {
                //  AppUtil.setCardType('SELF');
                //}
                var list = productService.getProductArrayForSubTypeCode(904);
                for (var i = 0; i < list.length; i++) {
                    if (giftCardId.indexOf(list[i].id) > -1) {
                        $scope.giftCardsList.push(list[i]);
                    }
                }
            };

            $scope.changeCardType = function (cardType) {
                if (cardType == 'GYFTR') {
                    getGyftrPartnerDetail($scope.cardType);
                }
                $scope.cardType = cardType;
                AppUtil.setCardType(cardType)
            };

            $scope.close = function (confirmRequired) {
                AppUtil.closeByButton=true;
                AppUtil.walletPayment=false;
                $scope.clearOnGiftModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };

            function closeModal() {
                $modalInstance.close('cancel');
                $scope.$parent.disableOrderCheckBtnGift = false;
            }

            $rootScope.closeGiftModal = function () {
                $scope.clearOnGiftModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };


            $scope.updateGiftCardCode = function (orderItem, cardNumber) {
                cardNumber = cardNumber.replace(/[^a-zA-Z0-9]/g, "");
                card.cardNumber = cardNumber;
                if (card.cardNumber.length == 6) {
                    if (!$scope.isDuplicateCard(card)) {
                        var gCards = [];
                        var configData = AppUtil.getAutoConfigData();

                        card.itemId = orderItem.itemId;
                        card.buyerId = AppUtil.customerSocket.id;
                        card.empId = $rootScope.globals.currentUser.userId;
                        card.unitId = configData.unitId;
                        card.terminalId = configData.selectedTerminalId;
                        card.type = AppUtil.cardType;
                        card.isValid = false;
                        card.error = "";
                        card.productName = orderItem.productName;
                        card.cardValue = orderItem.price;

                        gCards.push(card);

                        posAPI.allUrl('/', AppUtil.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function (response) {
                            if (response.errorType != undefined && response.errorType != '') {
                                var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
                                AppUtil.myAlert(msg);
                                return false;
                            }
                            var dataObj = response.plain();
                            if (dataObj != null) {
                                var valid = true;
                                dataObj.map(function (cardObj) {
                                    if (valid && !cardObj.isValid) {
                                        valid = false;
                                    }
                                });
                                if (!valid) {
                                    AppUtil.myAlert("Please fill the correct gift card codes.");
                                    $scope.notEditable[orderItem.itemId] = false;
                                    $scope.$parent.resetGiftCardCode(card);
                                } else {
                                    $scope.$parent.setGiftCardCode(card);
                                    $scope.notEditable[orderItem.itemId] = true;
                                    orderItem.isCardValid = true;
                                    giftCardNumberList.push(card);
                                    card = {};
                                }
                            } else {
                                var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
                                AppUtil.myAlert(msg);
                                $rootScope.showFullScreenLoader = false;
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                    } else {
                        AppUtil.myAlert("Card already added!");
                        card = {};
                    }
                }
            };


            $scope.deleteCardEntry = function (cardNumber, itemId) {
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber == cardNumber) {
                        giftCardNumberList.splice(i, 1);
                    }
                }
                $scope.deletableCardEntry.splice(itemId-1,1);
                $scope.notEditable[itemId] = false;

                $scope.calculateWallet(false);
            };

            $scope.isDuplicateCard = function (card) {
                var found = false;
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber.length > 0) {
                        if (giftCardNumberList[i].cardNumber == card.cardNumber) {
                            found = true;
                            break;
                        }
                    }
                }
                return found;
            };


            $scope.calculateWallet = function (flag) {
                var total = 0;
                for (var i = 0; i < $scope.orderItemArray.length; i++) {
                    total += $scope.orderItemArray[i].orderDetails.amount;
                    if (flag && ($scope.deletableCardEntry[i] == undefined || $scope.deletableCardEntry[i])) {
                        $scope.deletableCardEntry[i] = true;
                    }
                    else{
                        $scope.deletableCardEntry[i] = !!($scope.deletableCardEntry[i] != undefined && $scope.deletableCardEntry[i]);
                    }
                }
                $scope.totalValue = total;
                $scope.totalValueFlag = $scope.totalWalletAmountShow <= $scope.totalValue;
                //console.log(total);
                if ($scope.totalWalletAmountShow == 0 && $scope.orderItemArray.length == 0) {
                    $scope.totalValueFlag = false;
                    var offerMessage = "<div style='width: 100%;background: #F0F0F0;border-radius: 30px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%'><span style='font-size:large;'><b>Order Amount</b></span><span style='margin-left:25%;font-size:large;color:#E52F28'><b>₹ "+$scope.prepaidAmount+"</b></span></div>" +
                                       "<br><span style='font-size:large;color:#E52F28;margin-left: 20%;'><b> No Wallet Suggestion </b></span><br><br>"
                    bootbox.dialog({
                        className:"alertBox",
                        closeButton: false,
                        onEscape: false,
                        message: offerMessage,
                        size: "small",

                    });
                    $rootScope.walletSuggestionAttribute = "NO_WALLET_SUGGESTION";
                }
                if ($scope.totalValueFlag) {
                    calculateCustomerAmountLeft($scope.isSubscriptionActive());
                }else{
                    $scope.customerFinalPayableAmount=$scope.prepaidAmount;
                }
                //console.log($scope.customerWalletAmountLeft);
                //console.log($scope.totalValueFlag);
            };

            $scope.giftCardFilter= function(item){
                return !$scope.disableGiftCardId.includes(item.id);
            }


            $scope.makePayment = function () {
                $rootScope.isGiftCardPurchased = false;
                AppUtil.setPaymentSuggestWallet(true);
                $scope.$parent.disableOrderCheckBtnGift = true;
                console.log("without wallet payment");
                AppUtil.setCardType('GIFT');
                AppUtil.walletPayment = false;
                closeModal();

            };


            $scope.purchaseGiftCardWallet = function () {
                $rootScope.isGiftCardPurchased = false;
                $scope.$parent.disableOrderCheckBtnGift = true;
                $rootScope.isWithWalletPayment = true;
                AppUtil.setIsSuggestWalletPayment(true);
                console.log("with wallet payment");
                $scope.checkOrder(); //wall
                AppUtil.walletPayment = true;
                AppUtil.setTotalWalletAmount($scope.customerWalletAmountLeft);
            };

            $scope.suggestGiftCardAmount = function () {
                // extra Amount check here
                    var customerCardAmount;
                    if (!AppUtil.isEmptyObject($scope.customerPendingCardInfo) && $scope.customerPendingCardInfo.hasCard === false) {
                        customerCardAmount = parseInt(0);
                    } else {
                        if(!AppUtil.isEmptyObject($scope.customerPendingCardInfo) && !AppUtil.isEmptyObject($scope.customerPendingCardInfo.cardAmount)){
                            customerCardAmount = parseInt($scope.customerPendingCardInfo.cardAmount);
                        }

                    }
                    var customerPrepaidAmount = parseInt($scope.prepaidAmount);
                    if (customerCardAmount <= customerPrepaidAmount) {
                        var totalLeftAmount = Math.abs(customerPrepaidAmount - customerCardAmount);
                        var totalPayableAmount = totalLeftAmount;
                      var  flag = AppUtil.walletRecommendationDrools;
                        $rootScope.isSuggestedWallet = true;

                        if(!flag) {
                            if (totalPayableAmount < 320 && customerCardAmount <= 0) {
                                for (var i = 0; i < 6; i++) {
                                    if (totalLeftAmount >= $scope.giftCardsArray[i]) {
                                        var quantity = (totalLeftAmount / ($scope.giftCardsArray[i]));
                                        console.log(quantity);
                                        quantity = parseInt(quantity, 10);
                                        if (quantity > 0) {
                                            totalLeftAmount = totalLeftAmount - (quantity * $scope.giftCardsArray[i]);
                                        }
                                        //console.log($scope.giftCardsArray[i]);
                                        //console.log(quantity);
                                        $scope.totalWalletAmountShow += $scope.giftCardsArray[i] * quantity;
                                        $scope.totalWalletAmountShowOffer += $scope.giftCardOfferArray[i] * quantity;
                                        $scope.quantityAmountArray[i] = $scope.quantityAmountArray[i] + quantity;
                                        //console.log(totalLeftAmount);
                                        //console.log($scope.totalWalletAmountShow);
                                        //console.log($scope.totalWalletAmountShowOffer);
                                    }
                                }
                                //case if amount left less then 200*
                                if (totalLeftAmount < 200 && totalLeftAmount >= 0) {
                                    if (totalLeftAmount < 16) {
                                        $scope.quantityAmountArray[5] = $scope.quantityAmountArray[5] + 1;
                                        $scope.totalWalletAmountShow += 1 * $scope.giftCardsArray[5];
                                        $scope.totalWalletAmountShowOffer += 1 * $scope.giftCardOfferArray[5];
                                    } else {
                                        $scope.quantityAmountArray[5] = $scope.quantityAmountArray[5] + 2;
                                        $scope.totalWalletAmountShow += 2 * $scope.giftCardsArray[5];
                                        $scope.totalWalletAmountShowOffer += 2 * $scope.giftCardOfferArray[5];
                                    }

                                    totalLeftAmount = 0;
                                    // console.log("if amount is less then 100 add 100");
                                    // console.log($scope.totalWalletAmountShow);
                                    // console.log($scope.totalWalletAmountShowOffer);
                                }
                            } else {
                                for (var i = 0; i < 5; i++) {
                                    if (totalLeftAmount >= $scope.giftCardsArray[i]) {
                                        var quantity = (totalLeftAmount / ($scope.giftCardsArray[i]));
                                        console.log(quantity);
                                        quantity = parseInt(quantity, 10);
                                        if (quantity > 0) {
                                            totalLeftAmount = totalLeftAmount - (quantity * $scope.giftCardsArray[i]);
                                        }
                                        //console.log($scope.giftCardsArray[i]);
                                        //console.log(quantity);
                                        $scope.totalWalletAmountShow += $scope.giftCardsArray[i] * quantity;
                                        $scope.totalWalletAmountShowOffer += $scope.giftCardOfferArray[i] * quantity;
                                        $scope.quantityAmountArray[i] = $scope.quantityAmountArray[i] + quantity;
                                        //console.log(totalLeftAmount);
                                        //console.log($scope.totalWalletAmountShow);
                                        //console.log($scope.totalWalletAmountShowOffer);
                                    }
                                }
                                //case if amount left less then 200*
                                if (totalLeftAmount < 200 && totalLeftAmount >= 0) {
                                    if (totalPayableAmount < 1000) {
                                        if (totalPayableAmount >= 320 && customerCardAmount > 0) {
                                            if (totalLeftAmount < 66) {
                                                $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 2;
                                                $scope.totalWalletAmountShow += 2 * $scope.giftCardsArray[4];
                                                $scope.totalWalletAmountShowOffer += 2 * $scope.giftCardOfferArray[4];
                                            } else {
                                                $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 3;
                                                $scope.totalWalletAmountShow += 3 * $scope.giftCardsArray[4];
                                                $scope.totalWalletAmountShowOffer += 3 * $scope.giftCardOfferArray[4];
                                            }
                                        } else {
                                            if (totalLeftAmount < 66) {
                                                $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 1;
                                                $scope.totalWalletAmountShow += 1 * $scope.giftCardsArray[4];
                                                $scope.totalWalletAmountShowOffer += 1 * $scope.giftCardOfferArray[4];
                                            } else {
                                                $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 2;
                                                $scope.totalWalletAmountShow += 2 * $scope.giftCardsArray[4];
                                                $scope.totalWalletAmountShowOffer += 2 * $scope.giftCardOfferArray[4];
                                            }
                                        }

                                    } else {
                                        if (totalLeftAmount < 66) {
                                            $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 2;
                                            $scope.totalWalletAmountShow += 2 * $scope.giftCardsArray[4];
                                            $scope.totalWalletAmountShowOffer += 2 * $scope.giftCardOfferArray[4];
                                        } else {
                                            $scope.quantityAmountArray[4] = $scope.quantityAmountArray[4] + 3;
                                            $scope.totalWalletAmountShow += 3 * $scope.giftCardsArray[4];
                                            $scope.totalWalletAmountShowOffer += 3 * $scope.giftCardOfferArray[4];
                                        }
                                    }

                                    totalLeftAmount = 0;
                                    // console.log("if amount is less then 100 add 100");
                                    // console.log($scope.totalWalletAmountShow);
                                    // console.log($scope.totalWalletAmountShowOffer);
                                }
                            }
                            $scope.customerWalletAmountLeft = $scope.totalWalletAmountShow + $scope.totalWalletAmountShowOffer +
                                customerCardAmount - customerPrepaidAmount;
                            renderAmount();
                            //console.log($scope.customerWalletAmountLeft);
                        }
                        else{
                            console.log(AppUtil.customerSocket);
                            //AppUtil.customerSocket.id
                            //AppUtil.customerSocket.newCustomer
                            var requestObj = {
                                "customerId": AppUtil.customerSocket.id,
                                "brandId": "1",
                                "amountPayable": totalPayableAmount,
                                "newCustomer":AppUtil.customerSocket.newCustomer?"Y":"N",
                                "lastVisitTime" :getLastDayDifference(AppUtil.customerSocket.lastVisitTime),
                                "activeDenominationMap": $scope.activeDenominationMap,
                            };
                            posAPI.allUrl('/', AppUtil.restUrls.posMetaData.walletRecommendation)
                                .post(requestObj).then(function (response) {
                                    if(response !=undefined && response !=null){
                                        response = response.plain();
                                        var extraAmount = (response.extraAmount !=undefined && response.extraAmount!=null)?response.extraAmount:0;
                                        var recommendedAmount = (response.walletSuggestion !=undefined && response.walletSuggestion!=null)?response.walletSuggestion:0;
                                        $scope.quantityAmountArray[6] = $scope.quantityAmountArray[6] + recommendedAmount;
                                        $scope.totalWalletAmountShow += recommendedAmount * $scope.giftCardsArray[6];
                                        $scope.totalWalletAmountShowOffer += extraAmount;
                                        $scope.microWalletOfferAmount = extraAmount;

                                        $scope.customerWalletAmountLeft = $scope.totalWalletAmountShow + $scope.totalWalletAmountShowOffer +
                                            customerCardAmount - customerPrepaidAmount;
                                        AppUtil.setCardType('MICRO');
                                        renderMicroWallet();
                                        $scope.totalValueFlag = true;
                                        AppUtil.setCardType('ECARD');
                                        $rootScope.extraAmountGained = $scope.microWalletOfferAmount;
                                        $rootScope.totalAmountInWallet = $scope.customerWalletAmountLeft;
                                        $rootScope.paidExtra = $scope.customerWalletAmountLeft - $scope.microWalletOfferAmount;


                                    }

                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                                $rootScope.showFullScreenLoader = false;
                            })
                        }




                        // console.log($scope.quantityAmountArray);
                        // console.log($scope.totalWalletAmountShow);
                        // console.log($scope.totalWalletAmountShowOffer);

                }

            };

            function getLastDayDifference(lastDayDiff){
                var days =0;
                if(lastDayDiff !=undefined && lastDayDiff!=null && lastDayDiff>0){

                    var miliSec = Math.abs(Date.now()-lastDayDiff);
                    days = Math.floor(miliSec / (24*60*60*1000));
                }
                return days;
            }

            function  renderMicroWallet(){

                // var data = {
                //     "id": 2,
                //     "name": "Micro Card",
                //     "description": "Micro Card",
                //     "hasSizeProfile": false,
                //     "hasAddons": true,
                //     "type": 9,
                //     "subType": 904,
                //     "webType": 3632,
                //     "attribute": null,
                //     "classification": "MENU",
                //     "shortCode": "GV1",
                //     "inventoryTracked": false,
                //     "employeeMealComponent": false,
                //     "prices": [
                //         {
                //             "dimension": "None",
                //             "price": 1,
                //             "recipe": null,
                //             "recipeId": 10007,
                //             "customize": false,
                //             "dimensionDescriptor": null,
                //             "aliasProductName": null,
                //             "profile": "P0",
                //             "originalPrice": null,
                //             "isDeliveryOnlyProduct": false
                //         }
                //     ],
                //     "taxCode": "GIFT_CARD",
                //     "customize": false,
                //     "billType": "ZERO_TAX",
                //     "prepTime": 0,
                //     "brandId": 1,
                //     "skuCode": "GV1"
                // };
                // var numberOfGC;
                // if(data.id == 2 && $scope.quantityAmountArray[6] !=undefined && $scope.quantityAmountArray[6]!=null &&
                //     $scope.quantityAmountArray[6] >0 && $scope.microWalletOfferAmount!=undefined && $scope.microWalletOfferAmount!=null){
                //     numberOfGC = $scope.quantityAmountArray[6];
                //
                //     $scope.addNewProductToOrderItemArray(data,numberOfGC,undefined,undefined,undefined,$scope.microWalletOfferAmount);
                // }
                $scope.giftCardsList.forEach(function (data) {
                    //console.log(data);
                    var numberOfGC;
                    if(data.id == 2 && $scope.quantityAmountArray[6] !=undefined && $scope.quantityAmountArray[6]!=null &&
                        $scope.quantityAmountArray[6] >0 && $scope.microWalletOfferAmount!=undefined && $scope.microWalletOfferAmount!=null){
                        numberOfGC = $scope.quantityAmountArray[6];
                        $scope.addNewProductToOrderItemArray(data,numberOfGC,undefined,undefined,undefined,undefined,$scope.microWalletOfferAmount);
                    }
                });
                $scope.calculateWallet(false);
            }

            function renderAmount() {
                $scope.giftCardsList.forEach(function (data) {
                    //console.log(data);
                    var numberOfGC;
                    console.log(data.prices[0].price);
                    if (data.prices[0].price == 5000) {
                        numberOfGC = $scope.quantityAmountArray[0];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else if (data.prices[0].price == 2000) {
                        numberOfGC = $scope.quantityAmountArray[1];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else if (data.prices[0].price == 1000) {
                        numberOfGC = $scope.quantityAmountArray[2];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else if (data.prices[0].price == 500) {
                        numberOfGC = $scope.quantityAmountArray[3];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else  if (data.prices[0].price == 100){
                        numberOfGC = $scope.quantityAmountArray[4];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    }
                    else if(data.prices[0].price == 50){
                        numberOfGC = $scope.quantityAmountArray[5];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    }
                });
                $scope.calculateWallet(false);
            }

            function calculateCustomerAmountLeft(isSubscriptionActive) {
                $scope.customerWalletAmountLeft = 0;
                var customerCardAmount;
                if ($scope.customerPendingCardInfo.hasCard == false) {
                    customerCardAmount = parseInt(0);
                } else {
                    customerCardAmount = parseInt($scope.customerPendingCardInfo.cardAmount);
                }
                var customerPrepaidAmount = parseInt($scope.prepaidAmount);
                var total = 0, offer = 0;
                for (var i = 0; i < $scope.orderItemArray.length; i++) {
                   if($scope.orderItemArray[i].orderDetails.productId !=undefined && $scope.orderItemArray[i].orderDetails.productId !=null && $scope.orderItemArray[i].orderDetails.productId ==2 ) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += ($scope.orderItemArray[i].orderDetails.offerAmount !=undefined && $scope.orderItemArray[i].orderDetails.offerAmount !=null)? $scope.orderItemArray[i].orderDetails.offerAmount !=undefined && $scope.orderItemArray[i].orderDetails.offerAmount :0 ;
                   }
                   else if ($scope.orderItemArray[i].orderDetails.amount == 5000) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[0];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 2000) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[1];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 1000) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[2];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 500) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[3];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 100) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[4];
                    }else if ($scope.orderItemArray[i].orderDetails.amount == 50) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[5];
                    }

                }
                $scope.customerWalletAmountLeft = total + offer + customerCardAmount - customerPrepaidAmount;
                $scope.customerFinalPayableAmount=total;
                $scope.customerFinalExtraAmount=offer;
                var walletTotal = total + offer + customerCardAmount;
                var payExtraAmount = $scope.totalWalletAmountShow!=0 ? ($scope.customerFinalPayableAmount- $scope.prepaidAmount+customerCardAmount) : $scope.customerFinalPayableAmount;
                var percentageAmount =Math.round(($scope.customerFinalExtraAmount / payExtraAmount) * 100)
                if($scope.customerFinalExtraAmount !== 0 && percentageAmount !== 0 && !isSubscriptionActive){
                    var offerMessage = null;
                    if(customerCardAmount>0){
                        offerMessage = "<span style='font-size:large;color:#4A8132'><b>Bill Amount</b></span><span style='margin-left:39%;font-size:large;color:#4A8132'><b>₹ "+ $scope.prepaidAmount +"<br><span style='font-size:small;color:red'><b>*After using previous wallet balance of "+"₹ "+customerCardAmount+"</b></span></span>"+
                        "<hr style='border: 1px dashed black'/>"+
                        "<div style='text-align:center;width: 100%;background: #F0F0F0;border-radius: 10px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%;padding-bottom: 6%;'><span style='font-size:large;color:#000000;'><b>Add </b></span><span style='font-size:large;color:red;'><b>₹ "+payExtraAmount+"</span><span style='font-size:large;color:#000000;'> to your wallet<br/>And<br/>Get</span> <span style='font-size:large;color:red;'>₹ "+(payExtraAmount+$scope.customerFinalExtraAmount)+"</span><span style='font-size:large;color:#000000;'> back in your wallet</span></b></span></div>"+
                        "<hr style='border: 1px dashed black;margin-bottom: 10px;'/>"+
                        "<div style='text-align:center;width: 100%;'><span align=center style='text-align:center;font-size:large;color:#4A8132'><b>Customer gets </b></span><span style='font-size:large;color:#4A8132;'><b>₹ "+ $scope.customerFinalExtraAmount +" extra</b></span></div>"+
                        "<hr style='border: 1px dashed black;margin-top: 10px;'/>"+
                        "<br/><span style='margin-left: 10%;font-size:small'><b>Redeem Anytime at any Chaayos Cafe</b></span><br><br>"
                    }else{
                        offerMessage = "<span style='font-size:large;color:#4A8132'><b>Bill Amount</b></span><span style='margin-left:39%;font-size:large;color:#4A8132'><b>₹ "+ $scope.prepaidAmount +"<br></b></span>"+
                        "<hr style='border: 1px dashed black'/>"+
                        "<div style='text-align:center;width: 100%;background: #F0F0F0;border-radius: 10px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%;padding-bottom: 6%;'><span style='font-size:large;color:#000000;'><b>Add </b></span><span style='font-size:large;color:red;'><b>₹ "+payExtraAmount+"</span><span style='font-size:large;color:#000000;'> to your wallet<br/>And<br/>Get</span> <span style='font-size:large;color:red;'>₹ "+(payExtraAmount+$scope.customerFinalExtraAmount)+"</span><span style='font-size:large;color:#000000;'> back in your wallet</span></b></span></div>"+
                        "<hr style='border: 1px dashed black;margin-bottom: 10px;'/>"+
                        "<div style='text-align:center;width: 100%;'><span align=center style='text-align:center;font-size:large;color:#4A8132'><b>Customer gets </b></span><span style='font-size:large;color:#4A8132;'><b>₹ "+ $scope.customerFinalExtraAmount +" extra</b></span></div>"+
                        "<hr style='border: 1px dashed black;margin-top: 10px;'/>"+
                        "<br/><span style='margin-left: 10%;font-size:small'><b>Redeem Anytime at any Chaayos Cafe</b></span><br><br>"
                    }
                    bootbox.dialog({
                        className:"alertBox",
                        closeButton: false,
                        onEscape: false,
                        message: offerMessage,
                        size: "small",

                    });
                    var walletSavingsObj = {
                        finalAmount:total,
                        walletTotal:walletTotal,
                        previousBalance:customerCardAmount,
                        walletBalance:$scope.customerWalletAmountLeft,
                        extraAmount:$scope.customerFinalExtraAmount,
                        orderValue:$scope.prepaidAmount,
                        payExtra:payExtraAmount,
                        flow:walletFlow
                    }
                    socketUtils.emitMessage({WALLET_SAVINGS: walletSavingsObj});
                    $rootScope.isWalletSavingsScreenOpen = true;
                }else{
                    var offerMessage = "<div style='width: 100%;background: #F0F0F0;border-radius: 30px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%'><span style='font-size:large;'><b>Order Amount</b></span><span style='margin-left:25%;font-size:large;color:#E52F28'><b>₹ "+$scope.prepaidAmount+"</b></span></div>" +
                                       "<br><span style='font-size:large;color:#E52F28;margin-left: 20%;'><b> No Wallet Suggestion </b></span><br><br>"
                    bootbox.dialog({
                        className:"alertBox",
                        closeButton: false,
                        onEscape: false,
                        message: offerMessage,
                        size: "small",
                    });
                    $rootScope.walletSuggestionAttribute = "NO_WALLET_SUGGESTION";
                }
                //$scope.showOfferPrompt();
                //console.log($scope.customerWalletAmountLeft);
            }


            $scope.showOfferPrompt = function (){

                if(!AppUtil.isCOD() && !AppUtil.isPaidEmployeeMeal() && $rootScope.orderType == "order" && $scope.orderWithoutWallet !=undefined && $scope.orderWithoutWallet.length >=2 && !$scope.offerApplied){
                    var offerMessage = null;
                    offerMessage = "<div style='width: 100%;background: #F0F0F0;border-radius: 30px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%'><span style='font-size:large; margin-left: 15%'><b>AVAILABLE OFFERS</b></span></div>";

                    var bakeryProducts = false;
                    var merchandiseProducts = false;
                    for(var i = 0; i<$scope.orderWithoutWallet.length;i++){
                        if($scope.orderWithoutWallet[i].orderDetails!=undefined && $scope.orderWithoutWallet[i].orderDetails!=null && $scope.orderWithoutWallet[i].orderDetails.productType!=undefined && $scope.orderWithoutWallet[i].orderDetails.productType!=null && ($scope.orderWithoutWallet[i].orderDetails.productType==9 || $scope.orderWithoutWallet[i].orderDetails.productType==10) ){
                            $scope.orderWithoutWallet[i].orderDetails.productType==9 ? (merchandiseProducts=true):(bakeryProducts=true);
                        }

                    }

                    if(!merchandiseProducts && $scope.totalAmount !=undefined &&  $scope.totalAmount !=undefined >198){
                        offerMessage = offerMessage +  "<br><br><span style='font-size:large;color:#4A8132'><b>SNACK30 Coupon Applicable</b></span><span style='margin-left:18%;font-size:large;color:#4A8132'><b></b></span>";
                    }
                    if(!bakeryProducts && $scope.totalAmount !=undefined &&  $scope.totalAmount !=undefined >350){
                        offerMessage = offerMessage +  "<br><br><span style='font-size:large;color:#4A8132'><b>BAKERY30 Coupon Applicable</b></span><span style='margin-left:18%;font-size:large;color:#4A8132'><b></b></span>";
                    }
                    if($scope.totalAmount !=undefined && $scope.totalAmount >198){
                        var dialog = bootbox.dialog({
                            className:"alertBox2",
                            closeButton: true,
                            onEscape: true,
                            message: offerMessage,
                            size: "small",
                            backdrop:true
                        });
                    }

                }
            }

            $scope.isSubscriptionActive = function(){
                return ($scope.subscriptionDetails != null && $scope.subscriptionDetails.hasSubscription)
            }


        }]);
