/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular
    .module('posApp')
    .controller(
        'complimentaryOTPModalCtrl',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout',
            function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout) {

                $scope.init = function() {
                    $scope.errorMessage = null;
                    $scope.otpRequested = false;
                    $scope.unitAM = null;
                    $scope.unitDAM = null;
                    $scope.employeeName = ["","","Abhin<PERSON>"];
                    $scope.employeeNumber = [null,null,9717779785];
                    setAmDamDetails();
                };

                function setAmDamDetails(){
                    $scope.unitDetails = AppUtil.getUnitDetails();
                    if($scope.unitDetails.unitManager !=undefined && $scope.unitDetails.unitManager !=null){
                        $scope.unitAM = $scope.unitDetails.unitManager;
                        $scope.employeeName[0] =   $scope.unitAM.name!=undefined?$scope.unitAM.name:"";
                        $scope.employeeNumber[0] =   $scope.unitAM.primaryContact!=undefined?$scope.unitAM.primaryContact:null;

                        if($scope.employeeNumber[0].length <9){
                            $scope.employeeName[0] = "";
                            $scope.employeeNumber[0] = null;
                        }
                    }
                    if($scope.unitDetails.cafeManager !=undefined && $scope.unitDetails.cafeManager !=null){
                        $scope.unitDAM = $scope.unitDetails.cafeManager;
                        $scope.getEmployeeData($scope.unitDAM.id);
                    }
                }

                $scope.getEmployeeData = function(id) {
                    var url = AppUtil.restUrls.userManagement.user +"/"+id;
                    posAPI.allUrl('/',AppUtil.restUrls.userManagement.user)
                        .customGET("", {userId: id})
                        .then(
                            function(response) {
                                if(response !=undefined){
                                    $scope.unitDAM = response.plain();
                                    $scope.employeeName[1] =   $scope.unitDAM.name!=undefined?$scope.unitDAM.name:"";
                                    $scope.employeeNumber[1] =   $scope.unitDAM.primaryContact!=undefined?$scope.unitDAM.primaryContact:null;

                                    if($scope.employeeNumber[1].length <9){
                                        $scope.employeeName[1] = "";
                                        $scope.employeeNumber[1] = null;
                                    }
                                }
                            }, function(err) {
                                console.log("error feting employee details of Dam");
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                };

                // $scope.isValidContact = function() {
                //     return $scope.selectedEmployee != null && $scope.selectedEmployee.contactNumber != undefined &&
                //         $scope.selectedEmployee.contactNumber != null && $scope.selectedEmployee.contactNumber.length == 10;
                // };

                $scope.requestOTP = function() {
                    $scope.errorMessage = null;
                    var amContact = $scope.employeeNumber[0]!=null?$scope.employeeNumber[0]:$scope.employeeNumber[1];
                    var damContact = $scope.employeeNumber[1]!=null?$scope.employeeNumber[1]:null;
                    $scope.otpError = null;
                    posAPI.allUrl('/',AppUtil.restUrls.customer.generateComplimentaryOrderOTP).post({
                        amContactNumber : amContact,
                        damContactNumber : damContact
                    }).then(function(response) {
                        if (response) {
                            //$scope.mealOTP = null;
                            $scope.otpRequested = true;
                        } else {
                            $scope.showErrorMessage("Could not send OTP. Try again.");
                        }
                    }, function(err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                };

                $scope.showErrorMessage = function(text) {
                    $scope.errorMessage = text;
                    $timeout(function() {
                        $scope.errorMessage = null;
                    }, 4000)
                }

                $scope.verifyOTP = function(otp) {
                    posAPI.allUrl('/',AppUtil.restUrls.customer.verifyOTP).post({
                        contactNumber : $scope.employeeNumber[0]!=null?$scope.employeeNumber[0]:$scope.employeeNumber[1],
                        otpPin : otp,
                        unit : AppUtil.getUnitDetails().id
                    }).then(function(response) {
                        $scope.mealOTP = null;
                        if (response == true) {
                            $modalInstance.close();
                        } else {
                            $scope.showErrorMessage("Incorrect OTP. Please Try again.");
                        }
                    }, function(err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                };

                $scope.goBack = function() {
                    if (!AppUtil.isCOD()) {
                        $location.url('/cover');
                    } else {
                        $location.url('/CODCover');
                    }
                    $modalInstance.close();
                };
            }]);
