/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
	.module('posApp')
	.controller(
		'complimentaryOrderModalCtrl',
		['$scope','$rootScope', '$modalInstance', 'AppUtil', 'posAPI', '$location', function($scope,$rootScope, $modalInstance, AppUtil, posAPI, $location) {
		    
		    $scope.init = function() {
			
			$scope.complimentaryReasonCodes =  [];
			for(var i in $scope.transactionMetadata.complimentaryCodes.content){
			    if($scope.transactionMetadata.complimentaryCodes.content[i].category == 'INTERNAL'
			        && $scope.transactionMetadata.complimentaryCodes.content[i].id != 2102
			        && $scope.transactionMetadata.complimentaryCodes.content[i].id != 4034){
				    $scope.complimentaryReasonCodes.push($scope.transactionMetadata.complimentaryCodes.content[i]);
			    }
			}
			$rootScope.defaultComplimentaryCode = $scope.complimentaryReasonCodes[0];
		    };

		    $scope.setComplimentaryType = function(code){
			$modalInstance.close(code);
		    }
		    
		    $scope.goBack = function(){
		          if(!AppUtil.isCOD()){
		              $location.url('/cover');
		          } else {
	        		  $location.url('/CODCover');
		          }
		          $modalInstance.close();
		      };
		}]);
