/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('cafePartnerStatusCtrl', ['$rootScope', '$scope', '$filter', 'AppUtil', '$location', '$http', '$compile', 'posAPI', '$interval','$timeout',
        function ($rootScope, $scope, $filter, AppUtil, $location, $http, $compile, posAPI, $interval,$timeout) {

            $scope.init = function () {

                $scope.regionalCafeList = [];
                $scope.cafeStatusList = [];
                $scope.optedRegion = null;
                $scope.regionSelected = false;
                $scope.selectedBrandId = null;
                $scope.unitRegion = ["NCR", "NCR_EDU", "MUMBAI", "CHANDIGARH", "BANGALORE", "PUNE"];
                $scope.categories = ["Active on Both", "Inactive", "Off On Swiggy", "Off On Zomato", "Off On Both"];
                $scope.brand = ["CHAAYOS", "GnT"];
                $scope.showData = false;
                $scope.selectedBrandIdCode = null;
                $scope.resultFetch=[];
                if ($rootScope.unitRefresh != null) {
                    $timeout.cancel($rootScope.unitRefresh);
                }
                $scope.getCafes();

            };
            $scope.getCafes=function(){

                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.cafeLookUp.getCafeOfflineStatusForUnit
                    // url:'http://dev.kettle.chaayos.com:9595/channel-partner/rest/v1/lookUp/cafe-offline-status'
                }).then(function (response) {
                    console.log(response);
                    $scope.resultFetch=response.data;
                    $scope.refreshOrders();
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }
            posApp.filter('startFrom', function () {
                return function (input, start) {
                    if (input) {
                        start = +start; // parse to int
                        return input.slice(start);
                    }
                    return [];
                };
            });

            $scope.getAllCafe = function () {

                $scope.regionalCafeList = [];
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    params: {
                        brandId: $scope.selectedBrandIdCode
                    },
                    url: AppUtil.restUrls.cafeLookUp.getCafeStatus
                }).then(function (response) {
                    console.log(response)
                    $scope.cafeStatusList = response.data;
                    $scope.filterData();
                    $rootScope.showFullScreenLoader = false;
                    $scope.showData = true;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    $scope.showData = true;
                    console.log("ERROR********");
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }
            $scope.refreshOrders = function () {
                $rootScope.unitRefresh = $timeout(function () {
                    if ($location.url() == "/cafePartnerStatus") {
                   $scope.getCafes();
                    }
                }, 10000);
                console.log("I was refreshed");
            };

            $scope.selectBrand = function (value) {
                $scope.selectedBrandId = value;
                if ($scope.selectedBrandId === "CHAAYOS") {
                    $scope.selectedBrandIdCode = 1;
                } else if ($scope.selectedBrandId === "GnT") {
                    $scope.selectedBrandIdCode = 3;
                }
            };

            $scope.downloadCSV = function (args) {
                console.log(args);
                $scope.sortedOrder = [];
                $scope.sortedOrder = $scope.cafeStatusList;
                $scope.sortedOrder.sort(function (a, b) {
                    return a.category.localeCompare(b.category)
                })
                console.log($scope.sortedOrder);
                var data, filename, link;
                var csv = convertArrayOfObjectsToCSV({
                    data: $scope.sortedOrder
                });
                if (csv == null)
                    return;

                filename = args.filename || 'export.csv';

                if (!csv.match(/^data:text\/csv/i)) {
                    csv = 'data:text/csv;charset=utf-8,' + csv;
                }
                data = encodeURI(csv);

                link = document.createElement('a');
                link.setAttribute('href', data);
                link.setAttribute('download', filename);
                link.click();
            }

            function convertArrayOfObjectsToCSV(args) {
                console.log(args)
                var result, ctr, keys, columnDelimiter, lineDelimiter, data;

                data = args.data || null;
                if (data == null || !data.length) {
                    return null;
                }

                columnDelimiter = args.columnDelimiter || ',';
                lineDelimiter = args.lineDelimiter || '\n';

                keys = Object.keys(data[0]);

                result = '';
                result += keys.join(columnDelimiter);
                result += lineDelimiter;

                data.forEach(function (item) {
                    ctr = 0;
                    keys.forEach(function (key) {
                        if (ctr > 0)
                            result += columnDelimiter;

                        result += item[key];
                        ctr++;
                    });
                    result += lineDelimiter;
                });

                return result;
            }


            var interval = $interval(function () {
                $scope.getAllCafe();
            }, 360000); //after every 6 minutes


            $scope.backToCover = function () {
                $interval.cancel(interval);
                interval = undefined;
                $location.url('/CODCover');

            };


            $scope.filterData = function () {
                console.log(typeof $scope.cafeStatusList)
                $scope.cafeStatusList.forEach(function (cafeStatus) {
                    if (cafeStatus.cafeLive) {
                        if (cafeStatus.swiggyStatus === true && cafeStatus.zomatoStatusFromGetApi === true) {
                            cafeStatus.category = "Active on Both"
                        }
                        if (cafeStatus.zomatoStatusFromGetApi === false) {
                            cafeStatus.category = "Off On Zomato"
                        }
                        if (cafeStatus.swiggyStatus === false) {
                            cafeStatus.category = "Off On Swiggy"
                        }
                        if (cafeStatus.swiggyStatus === false && cafeStatus.zomatoStatusFromGetApi === false) {
                            cafeStatus.category = "Off On Both"
                        }
                    }
                    else {
                        cafeStatus.category = "Inactive"
                    }
                })
                return $scope.cafeStatusList;
            }

            $scope.getUnitCategoryWise = function (category) {
                $scope.modifiedList = [];
                $scope.modifiedList = $scope.filterData();
                $scope.selectedCategory = category;
            }


            $scope.categoryFilter = function (data) {
                return data.category == $scope.selectedCategory;

            }

        }]);