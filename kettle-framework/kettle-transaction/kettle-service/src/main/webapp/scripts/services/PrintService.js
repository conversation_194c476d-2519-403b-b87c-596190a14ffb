/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function() {
	'use strict';

	angular.module('posApp').factory('PrintService', PrintService);

	PrintService.$inject = ['$rootScope'];
	function PrintService($rootScope) {
		var service = {};

		service.printHTMLOnBilling = printHTMLOnBilling;
		service.printHTMLOnKot = printHTMLOnKot;
		service.isAndroid = isAndroid();
		service.printOnBilling = printOnBilling;
		service.printOnKot = printOnKot;
		$rootScope.printerStatus = false;
		service.loadQZPrinter = loadQZPrinter;

		return service;

		function isAndroid() {
			var isAndroid = false;
			if (/Android/i.test(navigator.userAgent)) {
				isAndroid = true;
			}
			return isAndroid;
		}
		
		function printOnBilling(printText, type) {
		    if (type == 'RAW') {
		        printRawOnBilling(printText);
		    } else {
		        printHTMLOnBilling(printText);
		    }
		}
		
		function printOnKot(printText, type) {
		    if (type == 'RAW') {
		    	printRawOnKot(printText);
		    } else {
		    	printHTMLOnKot(printText);
		    }
		}

		function printHTMLOnBilling(printText){
			printHTML(printText,"Billing");
		}
		
		function printRawOnBilling(printText){
			printRaw(printText,"Billing");
		}
		
		function printHTMLOnKot(printText){
			printHTML(printText,"Kot");
		}
		
		function printRawOnKot(printText){
			printRaw(printText,"Kot");
		}
		
		function printRaw(printText, printerName) {
		    var config = qz.configs.create(printerName);
		    var data = [{
		        type: 'raw',
		        data: printText
		    }];
		    qz.print(config, data).catch(function(e) {
		    	var message = null;
		    	if(e.message != null && e.message != ""){
		    		message = "<br/>" + e.message;
		    	}
		    	bootbox.alert("<h4>Qz is not running!<br/>Please run Qz Tray " + message + "</h4>");
		        console.log(e);
		        loadQZPrinter();
		    });
		}

		function printHTML(printText, printerName) {
		    var config = qz.configs.create(printerName);
		    var data = [{
		        type: 'html',
		        format: 'plain',
		        data: printText
		    }];
		    qz.print(config, data).catch(function(e) {
		    	var message = null;
		    	if(e.message != null && e.message != ""){
		    		message = "<br/>" + e.message;
		    	}
		    	bootbox.alert("<h4>Qz is not running!<br/>Please run Qz Tray " + message + "</h4>");
		        console.log(e);
		        loadQZPrinter();
		    });
		}
		
		function loadQZPrinter() {
			
			qz.security.setCertificatePromise(function(resolve, reject) {

              var cetificate = "-----BEGIN CERTIFICATE-----\n"+
                                       "MIIFXzCCA0egAwIBAgIQNzkyMDI0MDgyNzA3MzkxODANBgkqhkiG9w0BAQsFADCB\n"+
                                       "mDELMAkGA1UEBhMCVVMxCzAJBgNVBAgMAk5ZMRswGQYDVQQKDBJRWiBJbmR1c3Ry\n"+
                                       "aWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcGA1UEAwwQ\n"+
                                       "cXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBxemluZHVz\n"+
                                       "dHJpZXMuY29tMB4XDTI0MDgyNzA3MzkxOFoXDTI1MDgyNzA4MzY0MFowggEZMQsw\n"+
                                       "CQYDVQQGDAJJTjESMBAGA1UECAwJTmV3IERlbGhpMRIwEAYDVQQHDAlOZXcgRGVs\n"+
                                       "aGkxKjAoBgNVBAoMIVN1bnNoaW5lIFRlYWhvdXNlIFByaXZhdGUgTGltaXRlZDEq\n"+
                                       "MCgGA1UECwwhU3Vuc2hpbmUgVGVhaG91c2UgUHJpdmF0ZSBMaW1pdGVkMSowKAYD\n"+
                                       "VQQDDCFTdW5zaGluZSBUZWFob3VzZSBQcml2YXRlIExpbWl0ZWQxIDAeBgkqhkiG\n"+
                                       "9w0BCQEMEW1vaGl0QGNoYWF5b3MuY29tMTwwOgYDVQQNDDNyZW5ld2FsLW9mLWI0\n"+
                                       "Y2Y3NGUxNmNhOGYzNmZhNGZlODc5OGQxMDA3NTIzNWVhMGNiNGUwggEgMAsGCSqG\n"+
                                       "SIb3DQEBAQOCAQ8AMIIBCgKCAQEAzsYTcav+qBl6CaeENLQIWecqOFXBE4qiEnjp\n"+
                                       "psnanLeYYkZgGOmCVsyjY/UItfOVqMvN/IK5+o6vgO4s5Aerf0gstgy7sVO6/MX1\n"+
                                       "EYu9mX1xwpWjHuI6zLoXpgR4Lh7EE15Q6AFgy6wpnUl4MM2SUjqAUW39M4gDbQra\n"+
                                       "ht1vmungQVNgKWjw9N6IQEZFXEpvUOo8rOwSg7Gs/jYCT5bckqjuNmf1yrDeI1CH\n"+
                                       "a5Ttq64Z03pckaKYimuWJiB5Kxe5VLoQojiIJDiJmTBhGHVkVG1gmQjvbfxN9wE5\n"+
                                       "uEe7phmnfC81lsHgrQDoAgUb2qe/WOrjhcKz//+HKsBp+CLbiQIDAQABoyMwITAf\n"+
                                       "BgNVHSMEGDAWgBSQplC3hNS56l/yBYQTeEXoqXVUXDANBgkqhkiG9w0BAQsFAAOC\n"+
                                       "AgEAoMXk1YAQG6msnI6FvaU/qJEtx66KWoA1YWsUrB86obORQfyII9XwTtluJuOc\n"+
                                       "SQ/mWpdafzbdhAScu+OHGbib3UsVrimaNIiyY/VU5at5Qc5NE+U4gRuPdV4T6vzm\n"+
                                       "oz78OgeVIJ018NlYgxdU3rK3P31849mgrrGLPiVFmpyJEvP2Wzgg/E12l/xBCObQ\n"+
                                       "JMNgJqvt3vaO2LcPlf1y3WCttAA+HnuDmUvXGXC9pfRxR0U1QefRokWyKvHID/2p\n"+
                                       "u9c2LsTkdEonNFJZO6JDqHQ2OZr9TtVbIr/OXn6l1Gn2AAN8wSMjg+O7H1snTxIw\n"+
                                       "EK6bvLdPG/dQwWfQFJy52GSMzMJCuxpVyLSQxYUP6XvNot2wlXe1Yq/++IqUo1jk\n"+
                                       "Yxwo/vr/gDrUmNurzxNkaWBRPpoBh1O2EHj/GNbYfvlD6UkK4v697l2VbRXwmKmw\n"+
                                       "pCJE+tGRzpotSKbGnG4Kv4leeB1M7/Tco3lvRL82Q2feidgurRmhl+i+aP2Rykd6\n"+
                                       "JsBlQ2QHlqvR91fzkmlLomMJzxHJlxvMmEy1mQARwrV4GbYvbliH3N82m31H2Xu5\n"+
                                       "UgD9Adsc8P7pN8woV871aeCPgX2ar4E9jQU8gtz6OW1LKBVEoSTJSz2kAIYl8UcJ\n"+
                                       "fl3yA1ZPHOiY9dwD8FISGPSjKWhjyVDLY9bP6LhFv+1+nAM=\n"+
                                       "-----END CERTIFICATE-----\n"+
                                       "--START INTERMEDIATE CERT--\n"+
                                       "-----BEGIN CERTIFICATE-----\n"+
                                       "MIIFEjCCA/qgAwIBAgICEAAwDQYJKoZIhvcNAQELBQAwgawxCzAJBgNVBAYTAlVT\n"+
                                       "MQswCQYDVQQIDAJOWTESMBAGA1UEBwwJQ2FuYXN0b3RhMRswGQYDVQQKDBJRWiBJ\n"+
                                       "bmR1c3RyaWVzLCBMTEMxGzAZBgNVBAsMElFaIEluZHVzdHJpZXMsIExMQzEZMBcG\n"+
                                       "A1UEAwwQcXppbmR1c3RyaWVzLmNvbTEnMCUGCSqGSIb3DQEJARYYc3VwcG9ydEBx\n"+
                                       "emluZHVzdHJpZXMuY29tMB4XDTE1MDMwMjAwNTAxOFoXDTM1MDMwMjAwNTAxOFow\n"+
                                       "gZgxCzAJBgNVBAYTAlVTMQswCQYDVQQIDAJOWTEbMBkGA1UECgwSUVogSW5kdXN0\n"+
                                       "cmllcywgTExDMRswGQYDVQQLDBJRWiBJbmR1c3RyaWVzLCBMTEMxGTAXBgNVBAMM\n"+
                                       "EHF6aW5kdXN0cmllcy5jb20xJzAlBgkqhkiG9w0BCQEWGHN1cHBvcnRAcXppbmR1\n"+
                                       "c3RyaWVzLmNvbTCCAiIwDQYJKoZIhvcNAQEBBQADggIPADCCAgoCggIBANTDgNLU\n"+
                                       "iohl/rQoZ2bTMHVEk1mA020LYhgfWjO0+GsLlbg5SvWVFWkv4ZgffuVRXLHrwz1H\n"+
                                       "YpMyo+Zh8ksJF9ssJWCwQGO5ciM6dmoryyB0VZHGY1blewdMuxieXP7Kr6XD3GRM\n"+
                                       "GAhEwTxjUzI3ksuRunX4IcnRXKYkg5pjs4nLEhXtIZWDLiXPUsyUAEq1U1qdL1AH\n"+
                                       "EtdK/L3zLATnhPB6ZiM+HzNG4aAPynSA38fpeeZ4R0tINMpFThwNgGUsxYKsP9kh\n"+
                                       "0gxGl8YHL6ZzC7BC8FXIB/0Wteng0+XLAVto56Pyxt7BdxtNVuVNNXgkCi9tMqVX\n"+
                                       "xOk3oIvODDt0UoQUZ/umUuoMuOLekYUpZVk4utCqXXlB4mVfS5/zWB6nVxFX8Io1\n"+
                                       "9FOiDLTwZVtBmzmeikzb6o1QLp9F2TAvlf8+DIGDOo0DpPQUtOUyLPCh5hBaDGFE\n"+
                                       "ZhE56qPCBiQIc4T2klWX/80C5NZnd/tJNxjyUyk7bjdDzhzT10CGRAsqxAnsjvMD\n"+
                                       "2KcMf3oXN4PNgyfpbfq2ipxJ1u777Gpbzyf0xoKwH9FYigmqfRH2N2pEdiYawKrX\n"+
                                       "6pyXzGM4cvQ5X1Yxf2x/+xdTLdVaLnZgwrdqwFYmDejGAldXlYDl3jbBHVM1v+uY\n"+
                                       "5ItGTjk+3vLrxmvGy5XFVG+8fF/xaVfo5TW5AgMBAAGjUDBOMB0GA1UdDgQWBBSQ\n"+
                                       "plC3hNS56l/yBYQTeEXoqXVUXDAfBgNVHSMEGDAWgBQDRcZNwPqOqQvagw9BpW0S\n"+
                                       "BkOpXjAMBgNVHRMEBTADAQH/MA0GCSqGSIb3DQEBCwUAA4IBAQAJIO8SiNr9jpLQ\n"+
                                       "eUsFUmbueoxyI5L+P5eV92ceVOJ2tAlBA13vzF1NWlpSlrMmQcVUE/K4D01qtr0k\n"+
                                       "gDs6LUHvj2XXLpyEogitbBgipkQpwCTJVfC9bWYBwEotC7Y8mVjjEV7uXAT71GKT\n"+
                                       "x8XlB9maf+BTZGgyoulA5pTYJ++7s/xX9gzSWCa+eXGcjguBtYYXaAjjAqFGRAvu\n"+
                                       "pz1yrDWcA6H94HeErJKUXBakS0Jm/V33JDuVXY+aZ8EQi2kV82aZbNdXll/R6iGw\n"+
                                       "2ur4rDErnHsiphBgZB71C5FD4cdfSONTsYxmPmyUb5T+KLUouxZ9B0Wh28ucc1Lp\n"+
                                       "rbO7BnjW\n"+
                                       "-----END CERTIFICATE-----";
			resolve(cetificate);
		});

            var privateKey = "-----BEGIN PRIVATE KEY-----\n" +
                "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDOxhNxq/6oGXoJ\n" +
                "p4Q0tAhZ5yo4VcETiqISeOmmydqct5hiRmAY6YJWzKNj9Qi185Woy838grn6jq+A\n" +
                "7izkB6t/SCy2DLuxU7r8xfURi72ZfXHClaMe4jrMuhemBHguHsQTXlDoAWDLrCmd\n" +
                "SXgwzZJSOoBRbf0ziANtCtqG3W+a6eBBU2ApaPD03ohARkVcSm9Q6jys7BKDsaz+\n" +
                "NgJPltySqO42Z/XKsN4jUIdrlO2rrhnTelyRopiKa5YmIHkrF7lUuhCiOIgkOImZ\n" +
                "MGEYdWRUbWCZCO9t/E33ATm4R7umGad8LzWWweCtAOgCBRvap79Y6uOFwrP//4cq\n" +
                "wGn4ItuJAgMBAAECggEAFR8Hy63WV4ixrfSH9UO6EifiKMq+rA62TzV9ZXl7PJSm\n" +
                "6sv2QzkwBIqG+p4EH6Tj8c/uqLdYRLsoO37oxDqx1pCya36puft771fPuJT5/yig\n" +
                "kxAqr0wL1k+AfKd6eLiRDgrtcAN7Bhb+EbhYYADRrFdYNekqquW+9teb2piaLrw7\n" +
                "mHG5/T93Weksx5KNYVRqfBPHQYwqTdxCe/HZCKlwryhja65AE4b8WZRCTy5HTZwC\n" +
                "M4DlnTPIQw5rL5kprTOQdL4g99fNqJR2dovW/rcbiYGPgrGjMdh3Q3RjMmNd2TMz\n" +
                "YT5Tp/z5pQMI5Cws8/SfNoI0MyxqaBycIuQkEhAUsQKBgQD/FV/UfzK0Q70TM8XT\n" +
                "CpucQk+mJEUHmhtBUCvllNYgDp7MXL9gYM43LwhL7LwtP8+0X2AWopKyWcrH+b/j\n" +
                "WCwDASPr05QBPOcy8C+OAkoEKmOyFA4A8HuXo4FEfA2KHbtrdomPrj1V3IVcq4Ph\n" +
                "BXHtmBhFUd3ISrgfBfr70gN9WQKBgQDPhEQt7DVEiABd3Ep9f+GRX5324k/KsL1j\n" +
                "sajimRKz0l5sDer/oIn9fIpa/7X+9F4qu+HTKyqrVp5W0BLtH7bEJzuyE1ZPQfuO\n" +
                "sUo6CMHap9GInOJ/9IHyM0uK8+us8FbBWgXY6OjTCHrSSA+8MBKqIqCej6Yzy9cs\n" +
                "lUjxV2eZsQKBgQCDnG2jYDWzGLg3biEFsJV1XCloWZJcR8EEQ+9CNzb7t09rtfbw\n" +
                "LUJ46oVnVzAIUMJEGGmlhCAYcYfVAiFllZqvuijkhnf444mOmKqQpNG3sH9b5EkN\n" +
                "Zwb9yFjsEu2Rc6G94p07SvVOlcchd0VGFt8fbbgoHANIUnUJXLaQs10lkQKBgBEf\n" +
                "uQCRHVQqZN1Z8Euq4dI9MavNwQfYzcgMQQNx3jk4gtIn09yEQt7ICCK6Nypyv9KG\n" +
                "7nuedEbvPuGrCeTHWS2WjzaCofyoVTnRJ27iihyg/IlpaMdmRdLWqMUum6QJsR8D\n" +
                "brgXgB7p9Dil+aZt0Rx4/wgWkoBzsa3cI8jGjaYBAoGBANL7qUad7pwidhwLKfza\n" +
                "zawjpnm1JpzLHp1j0VTRcuVp4sFQ4X7PtbBpLBvrFIgFHouXasPwAXQBXAcAjoXM\n" +
                "wryyAAGw+ch9ZioSFxxs9P22EI2rDRlVoqDl4dOUn1el5e+NnrT7/RAULnxuE2o8\n" +
                "fF4Jf70zqswoB6o7EXCfn5Mz\n" +
                "-----END PRIVATE KEY-----";

	        qz.security.setSignaturePromise(function(toSign) {
	            return function(resolve, reject) {
	                try {
	                    var pk = KEYUTIL.getKey(privateKey);
	                    var sig = new KJUR.crypto.Signature({"alg": "SHA1withRSA"});
	                    sig.init(pk);
	                    sig.updateString(toSign);
	                    var hex = sig.sign();
	                    resolve(stob64(hextorstr(hex)));
	                } catch (err) {
	                    try{
	                        $.ajax({
	                            method : 'POST',
	                            url : '/kettle-service/print/v1/printer/secure/url/for',
	                            data : toSign,
	                            async : false,
	                            success : resolve
	                        });
	                    }catch (e){
	                        reject(e);
	                    }
	                }
	            };
	        });

			qz.websocket.setClosedCallbacks(function(evt) {
		        console.log(evt);
		        $rootScope.$apply(function(){
		        	$rootScope.printerStatus = false;
		        });
		        qz.websocket.disconnect();
		        if (evt.reason) {
		            console.log("Connection closed: " + evt.reason);
		        }
		        printOnBilling("","RAW");
		    });
			
	        launchQZ(false);
	}

	// / Connection ///
	function launchQZ(forced) {		
		if (!qz.websocket.isActive() || forced) {
			// Retry 5 times, pausing 1 second between each attempt
			startConnection({
				retries : 100,
				delay : 1
			});
		}
	}

	function startConnection(config) {
		if (!qz.websocket.isActive()) {
			qz.websocket.connect(config).then(function() {
				console.log("QZ Connected!");
				$rootScope.$apply(function(){
					$rootScope.printerStatus = true;
				});
			});
		} else {
			console.log("An active connection with QZ already exists.");
		}
	}
	}

})();
