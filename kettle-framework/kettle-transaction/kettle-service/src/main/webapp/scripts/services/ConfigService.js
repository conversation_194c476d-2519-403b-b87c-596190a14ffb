(function () {
    'use strict';
    angular.module('posApp').factory('ConfigService', ConfigService);
    function ConfigService() {

        var service = {};

        service.getUrls = getUrls;

        function getUrls(envType) {
            if (envType === "DEV") {
               return{
               masterBaseUrl :"http://dev.kettle.chaayos.com:8080",
               knockService: "http://dev.kettle.chaayos.com:8080",
               inventoryService: "http://dev.kettle.chaayos.com:8787"

               }
            }
            else if (envType === "STAGE") {
                return {
                masterBaseUrl : "http://stage.kettle.chaayos.com:8080",
                knockService: "http://stage.kettle.chaayos.com:8080",
                inventoryService: "http://stage.kettle.chaayos.com:8787"

                }
            }
            else {
                return {
                masterBaseUrl : window.location.protocol + '//' + window.location.host,
                knockService: window.location.protocol + '//' + window.location.host,
                inventoryService:  window.location.protocol + '//' + window.location.host

                }
           }
        }
       return service;
    }
})();