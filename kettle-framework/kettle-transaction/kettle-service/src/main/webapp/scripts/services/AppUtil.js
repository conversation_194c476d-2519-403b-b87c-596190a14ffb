/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular.module('posApp').factory('AppUtil', AppUtil);

    AppUtil.$inject = ['$cookieStore', '$rootScope', '$location', '$timeout', 'Flash', 'posAPI', 'usSpinnerService',
        'APIJson', 'PrintService', 'socketUtils', '$modal', 'trackingService', '$interval',];

    function AppUtil($cookieStore, $rootScope, $location, $timeout, Flash, posAPI, usSpinnerService, APIJson,
                     PrintService, socketUtils, $modal, trackingService, $interval) {

        var service = {};
        var swiggyCustomerNo = "9599598307";
        var amazonCustomerNo = "9599598306";
        var zomatoCustomerNo = "89475893476";

        service.analyticsRefreshInterval = 30; //minutes
        service.refreshAnalyticsData = refreshAnalyticsData;
        service.offers = [];
        service.customerFavChaiMappings =[];
        service.favChaiString = "MERI WALI CHAI";
        service.cafePartnerId= 1;
        service.massOfferData;
        service.redemptionDisplayChaiProducts = [{id: 10, name: "Desi Chai"}, {id: 170, name: "Green Tea"}, {
            id: 1205,
            name: "Black Tea"
        }];
        service.GetRequest = GetRequest;
        service.GetRequestWithoutStringify = GetRequestWithoutStringify;
        service.verifyLogin = verifyLogin;
        service.GetLoggedInUser = GetLoggedInUser;
        service.getAllComplimentaryCodes = getAllComplimentaryCodes;
        service.getFavChaiRequestObject= getFavChaiRequestObject;
        service.getFavChaiCustomizationShortCodes= getFavChaiCustomizationShortCodes;
        service.favChaiRequestObject={};
        service.getCustomerFavChaiMappings=getCustomerFavChaiMappings;
        service.checkIsFavChaiRedeemed=checkIsFavChaiRedeemed;
        service.saveClickedCustomerFavChaiMapping= saveClickedCustomerFavChaiMapping;
        service.currentCustomerFavChaiMapping ={};
        service.isFavChaiRedeemed = false;
        service.customerSessionObj = {};
        service.disableForTakeaway = false;
        service.unitFamily = getUnitFamily();
        service.checkIsMonk = checkIsMonk;
        service.isCafe = checkIsCafe;
        service.isDelivery = checkIsDelivery;
        service.isCOD = checkIsCOD;
        service.isTakeaway = checkTakeaway;
        service.hasLiveInventory = hasLiveInventory;
        service.hasTableService = hasTableService;
        service.tableServiceType = tableServiceType;
        service.hasExtendedTableService = hasExtendedTableService;
        service.hasSeparateKotPrinting = hasSeparateKotPrinting;
        service.checkUnitProductsData = checkUnitProductsData;
        service.getUnitProductsData = getUnitProductsData;
        service.getEmployeeMealProductsData = getEmployeeMealProductsData;
        service.isWorkstationEnabled = checkWorkstationEnabled;
        service.isTokenEnabled = checkTokenEnabled;
        service.getCreditAccounts = getCreditAccounts;
        service.creditAccounts = null;
        service.JSONToCSVConvertor = JSONToCSVConvertor;
        service.JSONToCSVConvertorLabelFromatter = JSONToCSVConvertorLabelFromatter;
        service.showCancel = true;
        service.showEditSettlement = false;
        service.isCustomerScreen = false;
        service.previousLocation = ['/login'];
        //unitDetails
        service.unitDetails = {};
        service.getUnitDetails = getUnitDetails;
        service.unitEdcMappingDetails=[]
        service.getUnitEdcMappingDetails = getUnitEdcMappingDetails;
        service.setUnitEdcMappingDetails = setUnitEdcMappingDetails;
        service.allCafeAppProperties = null;
        service.getAllCafeAppProperties = getAllCafeAppProperties;
        service.setAllCaeAppProperties = setAllCafeAppProperties;
        service.edcOrderRetryCount = 0;
        service.getEdcOrderRetryCount = getEdcOrderRetryCount;
        service.setEdcOrderRetryCount = setEdcOrderRetryCount;
        service.edcOrderMerchantId = null;
        service.getEdcOrderMerchantId = getEdcOrderMerchantId;
        service.setEdcOrderMerchantId = setEdcOrderMerchantId;
        service.paymentThroughEdcMode = false;
        service.getPaymentThroughEdcMode = getPaymentThroughEdcMode;
        service.setPaymentThroughEdcMode = setPaymentThroughEdcMode;
        service.transactionMetadata = {};
        service.giftCardOffers = {};
        service.directGiftCardPurchase=false;
        service.walletRecommendationDrools=false;
        service.getTransactionMetadata = getTransactionMetadata;
        service.PaymentModeMap = {};
        service.getPaymentModeIdsByModeType = getPaymentModeIdsByModeType;
        service.isEmptyObject = isEmptyObject;
        service.orderSummaryObj = {};
        service.customerSearchObject = {}; // created for customer order
        // summary
        service.refreshRedeemLock = refreshRedeemLock;
        service.lockRedeem = lockRedeem;
        service.OrderObj = $rootScope.OrderObj;
        service.discountObj = {
            value: 0,
            percentage: 0,
            discountReason: null,
            discountCode: null
        };
        service.localityMapping = [];
        service.cities = [];
        service.companyMapping = [];
        service.freeKettle = false;
        service.freeKettleCode = 'LOYALTEA';
        service.CSObj = {
            firstname: null,
            middlename: null,
            lastname: null,
            loyaltyPoints: null,
            emailId: null,
            contactNumber: null,
            acquisitionSource: 'Chaayos-COD',
            countryCode: '+91',
            registrationUnitId: null,
            acquisitionToken: null,
            addressess: null
        };
        service.outlet = {
            pri_unitId: null,
            pri_name: 'Primary Outlet',
            sec_unitId: null,
            sec_name: 'Secondary Outlet',
            ter_unitId: null,
            ter_name: 'Tertiary Outlet',
            selectedId: 1
        };
        service.partnerId = null;
        service.getCSAddress = getCSAddress();
        service.openOrderSearch = openOrderSearch;

        // SuggestGiftCardModal
        service.walletPayment=false;
        service.cardPaymentModule=false;
        service.closeByButton=false;

        // Alerts

        service.myAlert = myAlert;
        service.mySuccessAlert = mySuccessAlert;
        service.myInfoAlert = myInfoAlert;
        service.myWarningAlert = myWarningAlert;
        service.stdDialogueAlert = stdDialogueAlert;

        service.customerDeliveryAddress = 0;
        service.getTaxProfile = getTaxProfile;
        service.getPartnerId = getPartnerId;
        service.setPartnerId = setPartnerId;
        service.getPackagingProfile = getPackagingProfile;
        service.getDeliveryProfile = getDeliveryProfile;
        service.getProductProfile = getProductProfile;
        service.getBrandProductProfile = getBrandProductProfile;
        service.getDeliveryProfileForUnit = getDeliveryProfileForUnit;
        service.checkInArrayOfPartners = checkInArrayOfPartners;
        service.startSpin = startSpin;
        service.stopSpin = stopSpin;
        service.getSettlements = getSettlements;
        service.getDeliveryDetail = getDeliveryDetail;
        service.discountValue = 0;
        service.isAndroid = isAndroid();
        service.sendOrder = sendOrder;
        service.restUrls = APIJson.urls;
        service.addOrderItems = addOrderItems;
        service.resetOrderItemCustomization = resetOrderItemCustomization;
        service.resetOtpStatus = resetOtpStatus;
        service.calculateCustomization = calculateCustomization;
        service.hasRecipeContents = hasRecipeContents;
        service.addNewItemForCombo = addNewItemForCombo;
        service.getCustomizationAbb = getCustomizationAbb;
        service.addNewItem = addNewItem;
        service.addNewItemForIndex = addNewItemForIndex;
        service.checkNumber = checkNumber;
        service.isDev = isDev;
        service.downloadConsumption = isDev();
        service.hasProductForId = hasProductForId;
        service.setRedemptionProductList = null;
        service.setUnitFamily = setUnitFamily;
        service.autoConfigData = {};
        service.getAutoConfigData = getAutoConfigData;
        service.setAutoConfigData = setAutoConfigData;
        service.removeAutoConfigData = removeAutoConfigData;
        service.getCoveredCustomerContact = getCoveredCustomerContact;
        service.swiggyCustomer = undefined;
        service.amazonCustomer = undefined;
        service.zomatoCustomer = undefined;
        service.getSwiggyCustomer = getSwiggyCustomer;
        service.getZomatoCustomer = getZomatoCustomer;
        service.loadSwiggyCustomer = loadSwiggyCustomer;
        service.loadZomatoCustomer = loadZomatoCustomer;
        service.loadAmazonCustomer = loadAmazonCustomer;
        service.getProductProfileByCity = getProductProfileByCity;
        service.getBrandUnitDeliveryProductProfile = getBrandUnitDeliveryProductProfile;
        service.activeChannelPartnerId = -1;
        service.enablefreeChaiDelivery=true;
        service.defaultDeliveryDetail = undefined;
        service.nonDeliveryOnlyProductIndex = -1;
        service.checkIsDeliveryOnlyProduct= checkIsDeliveryOnlyProduct;
        service.isWebDineIn = function (order) {
            // channel partner 14 is Webapp
            return order != null && (order.channelPartner == 14 && order.source == "CAFE");
        };

        service.resetCustomerSocket = function () {
            return {
                id: null,
                name: null,
                contact: null,
                email: null,
                loyalityPoints: 0,
                contactVerified: false,
                emailVerified: false,
                unitId: service.unitDetails.id,
                newCustomer: false,
                otp: null,
                chaiRedeemed: 0,
                productId: 10,
                otpVerified: false
            };
        };

        service.customerSocket = null;

        service.getMassOffers = getMassOffers;

        service.yesterday = yesterday;
        service.validate = validate;
        service.backToCover = backToCover;
        service.getCurrentDate = getCurrentDate;
        service.formatDate = formatDate;
        service.GetCustomerInfo = GetCustomerInfo;
        service.getCustomerBasicInfo= getCustomerBasicInfo;
        service.getEmptyComposition = getEmptyComposition;
        service.checkInventory = true;
        service.getSubscriber = function () {
            var currentUser = $rootScope.globals.currentUser;
            return {
                unit: currentUser.unitId,
                terminal: currentUser.terminalId,
                type: currentUser.screenType
            };
        };

        service.getParseList = function () {
            var parseList = APIJson.PROD_PARSE_LIST;
            if ($location.host().indexOf("prod") != -1) {
                parseList = APIJson.PROD_PARSE_LIST;
            } else {
                parseList = APIJson.DEV_PARSE_LIST;
            }
            // //console.log("returning parse list :::::::::::: ", parseList);
            return parseList;
        };


        //special url to add delivery details in order summary

        service.getDeliveryDetails = function () {
            var url = "";
            console.log(getEnvType())
            if (getEnvType() == 'PROD') {
                url = "https://stpltd.typeform.com/to/wYaqx4I6#"; //prod
            } else {
                url = "https://stpltd.typeform.com/to/z15mVMEl#";//dev
            }
            return url;
        }

        // cache maintained for COD
        service.regionCache = [];
        service.deliveryProfileCache = [];
        service.taxProfileCache = [];
        service.packagingProfileCache = [];
        service.deliverProfileUnitCache = [];
        service.getProductForId = getProductForId;
        service.isInventoryTrackedProductForId = isInventoryTrackedProductForId;
        service.getOrderPointsRedeemed = getOrderPointsRedeemed;
        service.getProductsString = getProductsString;
        service.getSelectedAddress = getSelectedAddress;
        service.getSelectedUnitIdName = getSelectedUnitIdName;
        service.getOrderModeFromSource = getOrderModeFromSource;
        service.isPaidEmployeeMeal = isPaidEmployeeMeal;
        service.arrangeAddress = arrangeAddress;
        service.cardType = 'GIFT';
        service.setCardType = setCardType;
        service.reprintOrder = reprintOrder;
        service.reprintSettlementSlip = reprintSettlementSlip;
        service.reprintOrderKOT = reprintOrderKOT;
        service.sortBy = sortBy;
        service.currentTransactionGiftCards = [];
        service.setCurrentTransactionGiftCards = setCurrentTransactionGiftCards;
        service.getCurrentTransactionGiftCards = getCurrentTransactionGiftCards;
        service.getProductsOfCategory = getProductsOfCategory;
        service.productMap = {};
        service.unitProductMap = {};
        service.getUnitsProductMap = getUnitsProductMap;
        service.categoryList = [];
        service.expiryProducts = [];
        service.arrangeProductCategory = arrangeProductCategory;
        service.clearSpecialChars = clearSpecialChars;
        service.clearProductCache = clearProductCache;
        service.getOrderSourceName = getOrderSourceName;
        service.getSelectedUnitId = getSelectedUnitId;
        service.outletList = [];
        service.getUnitList = getUnitList;
        service.unitBasicDetailList = [];
        service.sendOrderStart = sendOrderStart;
        service.sendTableOrderStart = sendTableOrderStart;
        service.otpStatus = {text: "", status: -1};
        service.isCSOtpVerified = isCSOtpVerified;
        service.printAndroidReceipt = printAndroidReceipt;
        service.selectedBrand = null;
        service.setSelectedBrand = setSelectedBrand;
        service.getSelectedBrand = getSelectedBrand;
        service.CHAAYOS_BRAND_ID = 1;
        service.getBrandByBrandId = getBrandByBrandId;
        service.pendingStockCalendarEvent = false;
        service.setPendingStockCalendarEvent = setPendingStockCalendarEvent;
        service.getPendingStockCalendarEvent = getPendingStockCalendarEvent;
        //Assembly rider temp. info
        service.swiggyFlag = false;
        service.zomatoFlag = true;
        service.ZOMATO_PARTNER_ID = 1;
        service.SWIGGY_PARTNER_ID = 2;
        service.MAX_BODY_TEMP_SWIGGY = 98.6;
        service.MAX_BODY_TEMP_ZOMATO = 99.0;
        service.PARTNER_TEMP_FLAG = true;
        service.PARTNER_MASK_FLAG = true;
        service.unitPartnerBrandMappings = [];
        service.getSwiggyMappedBrandPricingUnits = getSwiggyMappedBrandPricingUnits;
        service.getZomatoMappedBrandPricingUnits = getZomatoMappedBrandPricingUnits;
        service.getAmazonMappedBrandPricingUnits = getAmazonMappedBrandPricingUnits;
        service.unitPartnerBrandMappingAvailable = unitPartnerBrandMappingAvailable;
        service.getPartnerNameById = getPartnerNameById;
        service.unitPartnerBrandMetadata = {};
        service.setUnitPartnerBrandMetadata = setUnitPartnerBrandMetadata;
        service.getUnitPartnerBrandMetadata = getUnitPartnerBrandMetadata;
        service.purchasedGiftCard = [];
        service.getPurchasedGiftCard = getPurchasedGiftCard;
        service.setPurchasedGiftCard = setPurchasedGiftCard;
        service.purchasedChaayosSelect = [];
        service.getPurchasedChaayosSelect = getPurchasedChaayosSelect;
        service.setPurchasedChaayosSelect = setPurchasedChaayosSelect;
        service.isGiftCardModalOpen = false;
        service.getIsGiftCardModalOpen = getIsGiftCardModalOpen;
        service.setIsGiftCardModalOpen = setIsGiftCardModalOpen;
        service.isChaayosSelectModalOpen = false;
        service.getIsChaayosSelectModalOpen = getIsChaayosSelectModalOpen;
        service.setIsChaayosSelectModalOpen = setIsChaayosSelectModalOpen;
        service.isSuggestWalletPayment = false;
        service.getIsSuggestWalletPayment = getIsSuggestWalletPayment;
        service.setIsSuggestWalletPayment = setIsSuggestWalletPayment;
        service.setPriceMap=setPriceMap;
        service.calculateGiftCardAmmount = calculateGiftCardAmount;
        service.calculateSelectCardAmount=calculateSelectCardAmount;
        service.totalPaybleAmount = 0;
        service.getTotalPaybleAmount = getTotalPaybleAmount;
        service.setTotalPaybleAmount = setTotalPaybleAmount;
        service.totalWalletAmount = 0;
        service.setTotalWalletAmount = setTotalWalletAmount;
        service.totalSettlementAmount = 0;
        service.getTotalSettlementAmount = getTotalSettlementAmmount;
        service.setTotalSettlementAmount = setTotalSettlementAmmount;
        service.paymentSuggestWallet =false;
        service.getPaymentSuggestWallet = getPaymentSuggestWallet;
        service.setPaymentSuggestWallet = setPaymentSuggestWallet;
        service.selectGiftCardAmount={};
        service.getSelectGiftCardAmount = getSelectGiftCardAmount;
        service.setSelectGiftCardAmount = setSelectGiftCardAmount;
        service.setRemainingSettlementAmount = setRemainingSettlementAmount;
        service.TotalAmountToBePaid = {};
        service.getTotalAmountToBePaid = getTotalAmountToBePaid;
        //service.differentPaymentModes={};
        //service.getDifferentPaymentModes=getDifferentPaymentModes;
        service.UnitIDZoneMap = {};
        service.getUnitIDZoneMap = getUnitIDZoneMap;
        service.setUnitIDZoneMap = setUnitIDZoneMap;
        service.generateRandomNumber = generateRandomNumber;

        return service;

        /**
         * function definitions
         */

        function isCSOtpVerified() {
            return service.customerSocket != null ? service.customerSocket.otpVerified : false;
        }

        function isPaidEmployeeMeal() {
            return $rootScope.orderType == "paid-employee-meal";
        }


        function reprintSettlementSlip(tableRequestId) {
            var promise = posAPI.allUrl('/', service.restUrls.order.reprintSettlementReceipt).post(tableRequestId).then(function (response) {
                var receipt = response.plain();
                if (service.isAndroid) {
                    Android.printText([receipt.settlementReceipt]);
                } else {
                    PrintService.printOnBilling(receipt.settlementReceipt, receipt.printType);
                }
                $rootScope.showFullScreenLoader = false;
                return receipt;
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
            return promise;
        }

        function reprintOrder(generatedOrderId) {
            var reqObj = {
                generatedOrderId: generatedOrderId,
                approvedBy: $rootScope.globals.currentUser.userId,
                reason: "Required"
            };
            //console.log(reqObj);
            var promise = posAPI.allUrl('/', service.restUrls.order.reprintOrder).post(service.GetRequest(reqObj)).then(function (response) {
                var receipt = response.plain();
                if (service.isAndroid) {
                    Android.printText(receipt.printString);
                } else {
                    PrintService.printOnBilling(receipt.printString, receipt.printType);
                }
                $rootScope.showFullScreenLoader = false;
                return receipt;
            }, function (err) {
                //console.log(err);
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
            return promise;
        }


        function reprintOrderKOT(generatedOrderId) {
            var reqObj = {
                generatedOrderId: generatedOrderId,
                approvedBy: $rootScope.globals.currentUser.userId,
                reason: "Required"
            };
            posAPI.allUrl('/', service.restUrls.order.reprintOrderKOT).post(service.GetRequest(reqObj)).then(function (response) {
                var receiptList = response.plain();
                if (receiptList.length > 0) {
                    if (service.isAndroid) {
                        var list = [];
                        for (var i = 0; i < receiptList.length; i++) {
                            list.push(receiptList[i].printString);
                        }
                        Android.printKots(list);
                    } else {
                        for (var i = 0; i < receiptList.length; i++) {
                            if (hasSeparateKotPrinting()) {
                                PrintService.printOnKot(receiptList[i].printString, receiptList[i].printType);
                            } else {
                                PrintService.printOnBilling(receiptList[i].printString,
                                    receiptList[i].printType);
                            }
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                } else {
                    service.myAlert("This Order does not contain items to be printed on KOT.");
                    $rootScope.showFullScreenLoader = false;
                }
            }, function (err) {
                //console.log(err);
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
        }

        function getFavChaiCustomizationShortCodes (customizationList,customerFavChaiMapping,s){
            var customizationsShortCodes =[];
            if(customizationList!=undefined && customizationList!=null && customizationList.length>0){
                for(var i in customizationList){
                    customizationsShortCodes.push(customizationList[i].shortCode);
                }
            }
            var addOnShortCodes ="";
            if(customizationsShortCodes!=null && customizationsShortCodes !=undefined && customizationsShortCodes.length>0 ){
                addOnShortCodes=customizationsShortCodes.join(" ,");
            }
            if(customerFavChaiMapping.productName.length == s.length ){
                if(addOnShortCodes.length>0){
                    return s+" - "+addOnShortCodes;
                }else {
                    return s +" - "+ "No selected customizations/Addons";
                }
            }
            return s+addOnShortCodes;
        }

        function getFavChaiRequestObject (orderItem,unitId,customerBasicInfo){
            orderItem.orderDetails.brandId=service.CHAAYOS_BRAND_ID;
            orderItem.orderDetails.unitId= unitId;
            orderItem.orderDetails.tagType ="Meri Wali Chai";//FOR NOW SET THE DEAFULT VALUE
            orderItem.orderDetails.consumeType="SELF";
            orderItem.orderDetails.sourceId=1;
            orderItem.orderDetails.sourceName="POS";
            service.favChaiRequestObject.customerBasicInfo = customerBasicInfo
            service.favChaiRequestObject.orderDetails=orderItem.orderDetails;
            service.favChaiRequestObject.productDetails=orderItem.productDetails;
            service.favChaiRequestObject.recipeDetails=orderItem.recipeDetails;
            service.favChaiRequestObject.isFavChaiMarked= orderItem.isFavChaiMarked;
            return service.favChaiRequestObject;
        }

        function getCustomerBasicInfo(callback){
            var customerBasicInfo={};
            if (service.customerSocket != undefined && service.customerSocket != null
                && service.customerSocket.id != undefined && service.customerSocket.id != null
                && service.customerSocket.id > 5) {
                customerBasicInfo.id = service.customerSocket.id;
                customerBasicInfo.name=service.customerSocket.name;
                customerBasicInfo.contact= service.customerSocket.contact;
                console.log("Customer basic info ", customerBasicInfo);
            }
            if(callback!=undefined){
                callback(customerBasicInfo);
            }
        }

        function getCustomerFavChaiMappings (customerId,callback){
                var requestUrl = service.restUrls.favChai.customerActiveFavChaiMappings;
                posAPI.allUrl('/',requestUrl ).customGET("", {
                    customerId: customerId
                }).then(function (response) {
                    console.log("::::::::::::::::::::::::Customer Fav chai mappings for customerId ::::::::{}",customerId, response.plain());
                    service.customerFavChaiMappings= response.plain();
                    if(callback!=undefined){
                        callback(service.customerFavChaiMappings);
                    }
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                });
        }

        function checkIsFavChaiRedeemed (isFavChaiRedeemed){
            service.isFavChaiRedeemed= isFavChaiRedeemed!==undefined && isFavChaiRedeemed!=null && isFavChaiRedeemed?isFavChaiRedeemed:false;
            return service.isFavChaiRedeemed;
        }

        function  saveClickedCustomerFavChaiMapping (customerFavChaiMapping){
            service.currentCustomerFavChaiMapping = customerFavChaiMapping;
        }

        function arrangeAddress(addressObj, isDetail) {
            if (addressObj == undefined) {
                return "-";
            }
            var address = "<strong>"+ addressObj.name +"</strong> (" + addressObj.contact + ")<br>";
            if(addressObj.email != null) {
                address = address + addressObj.email + "<br>"
            }
            address = address + "(" + addressObj.addressType + ") ";
            if (addressObj.addressType == "OFFICE" && addressObj.company != null) {
                address = address + " " + addressObj.company + "<br>";
            }
            address = address + addressObj.line1;
            if (addressObj.line2 != null) {
                address = address + "," + addressObj.line2;
            }
            if (addressObj.locality != null) {
                address = address + "<br>" + addressObj.locality;
            }
            if (isDetail && addressObj.locality != null) {
                address = address + "," + addressObj.subLocality;
            }
            if (addressObj.landmark != null) {
                address = address + "<br>" + addressObj.landmark;
            }

            if (addressObj.city != null) {
                address = address + "<br>" + addressObj.city;
            }

            if (isDetail && addressObj.state != null) {
                address = address + "," + addressObj.state;
            }
            if (addressObj.country != null) {
                address = address + "," + addressObj.country;
            }

            return address;
        };

        function setUnitFamily(unitFamily) {
            if (unitFamily != undefined) {
                service.unitFamily = unitFamily;
            } else {
                service.unitFamily = getUnitFamily();
            }
        }

        function checkNumber(number) {
            var regex = /^([6-9])(\d{9})$/;
            var regexReturn = regex.test(number);
            // //console.log(regexReturn);
            return regexReturn;
        }

        function checkIsCOD() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'COD';
            }
            return false;

        }

        function getEnvType() {
            return ($location.host().indexOf("prod") != -1 ||
                $location.host().indexOf("internal") != -1) ? "PROD" : "DEV";
        }

        function isDev() {
            return getEnvType() == "DEV";
        }

        function checkTakeaway() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return (unitFamily == 'TAKE_AWAY' || unitFamily == "CHAI_MONK");
            }
            return false;
        }

        function getEmptyComposition() {
            var data = {
                variants: [],
                products: [],
                menuProducts: [],
                addons: [],
                options: [],
                others:[],
            };
            return data;
        }

        function hasRecipeContents(recipe) {
            // //console.log('hasRecipeContents : ', recipe);
            if (recipe == null) {
                return false;
            }
            if (recipe.ingredient != null) {
                if (recipe.ingredient.variants != null && recipe.ingredient.variants.length > 0) {
                    return true;
                }
                if (recipe.ingredient.products != null && recipe.ingredient.products.length > 0) {
                    return true;
                }
                if (recipe.ingredient.compositeProduct != null && recipe.ingredient.compositeProduct.details != null
                    && recipe.ingredient.compositeProduct.details.length > 0) {
                    return true;
                }
                if (recipe.addons != null && recipe.addons.length > 0) {
                    return true;
                }
                if (recipe.options != null && recipe.options.length > 0) {
                    return true;
                }
            }
            return false;
        }

        function verifyLogin(userId, showComment, mandatoryComment, callback) {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: window.version + 'views/loginVerificationModal.html',
                controller: 'LoginVerificationModalCtrl',
                backdrop: 'static',
                size: 'sm',
                resolve: {
                    requestData: function () {
                        var data = {
                            id: userId,
                            showComment: showComment,
                            mandatoryComment: mandatoryComment
                        }
                        return data;
                    }
                }
            });
            modalInstance.result.then(function (result) {
                callback(result);
            }, function (result) {
                callback(result);
            });
        }

        function resetOtpStatus() {
            service.otpStatus = {
                text: "",
                status: -1
            };
        }

        function resetOrderItemCustomization(recipe) {
            if (recipe == null) {
                return null;
            }
            if (recipe.ingredient != null) {
                if (recipe.ingredient.variants != null && recipe.ingredient.variants.length > 0) {
                    for (var k in recipe.ingredient.variants) {
                        for (var i in recipe.ingredient.variants[k].details) {
                            recipe.ingredient.variants[k].details[i].selected = recipe.ingredient.variants[k].details[i].defaultSetting;
                        }
                    }
                }
                if (recipe.ingredient.products != null && recipe.ingredient.products.length > 0) {
                    for (var k in recipe.ingredient.products) {
                        for (var i in recipe.ingredient.products[k].details) {
                            recipe.ingredient.products[k].details[i].selected = recipe.ingredient.products[k].details[i].defaultSetting;
                        }
                    }
                }
                if (recipe.ingredient.compositeProduct != null && recipe.ingredient.compositeProduct.details != null
                    && recipe.ingredient.compositeProduct.details.length > 0) {
                    for (var k in recipe.ingredient.compositeProduct.details) {
                        for (var i in recipe.ingredient.compositeProduct.details[k].menuProducts) {
                            recipe.ingredient.compositeProduct.details[k].menuProducts[i].selected = false;
                        }
                    }
                }
                if (recipe.addons != null && recipe.addons.length > 0) {
                    for (var i in recipe.addons) {
                        recipe.addons[i].selected = false;
                    }
                }
                if (recipe.options != null && recipe.options.length > 0) {
                    for (var i in recipe.options) {
                        recipe.options[i].selected = false;
                    }
                }
            }

            return recipe;
        }

        function getCustomizationAbb(orderItem) {
            if (orderItem == undefined || orderItem.orderDetails.composition == undefined
                || orderItem.orderDetails.composition == null) {
                if (orderItem != undefined && orderItem.orderDetails != undefined && orderItem.orderDetails.takeAway) {
                    return 'Take Away';
                }
                return;
            }
            var allAddons = [];
            if (orderItem.orderDetails.takeAway) {
                allAddons.push('Take Away')
            }
            if (orderItem.orderDetails.composition.variants != null
                && orderItem.orderDetails.composition.variants.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.variants.length; i++) {
                    if (orderItem.orderDetails.composition.variants[i].selected)
                        allAddons.push(orderItem.orderDetails.composition.variants[i].alias);
                }
            }
            if (orderItem.orderDetails.composition.products != null
                && orderItem.orderDetails.composition.products.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.products.length; i++) {
                    if (orderItem.orderDetails.composition.products[i].selected)
                        allAddons.push(orderItem.orderDetails.composition.products[i].product.name);
                }
            }
            var returnAddons = '';
            if (allAddons.length > 0) {
                returnAddons = allAddons.join(" - ");
                allAddons = [];
            }
            if (orderItem.orderDetails.composition.addons.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.addons.length; i++) {
                    allAddons.push(orderItem.orderDetails.composition.addons[i].product.shortCode);
                }
            }
            if (orderItem.orderDetails.composition.options.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.options.length; i++) {
                    allAddons.push(orderItem.orderDetails.composition.options[i]);
                }
            }
            if (allAddons.length > 0) {
                returnAddons = returnAddons + ' :: ' + allAddons.join(",");
            }

            return returnAddons;
        }

        function calculateCustomization(orderData, itemId) {
            if (orderData.recipeDetails == null) {
                return orderData;
            }
            orderData.orderDetails.composition = getEmptyComposition();
            if (orderData.recipeDetails.ingredient != null) {
                if (orderData.recipeDetails.ingredient.variants != null
                    && orderData.recipeDetails.ingredient.variants.length > 0) {
                    for (var k in orderData.recipeDetails.ingredient.variants) {
                        for (var i in orderData.recipeDetails.ingredient.variants[k].details) {
                            if (orderData.recipeDetails.ingredient.variants[k].details[i].selected) {
                                orderData.orderDetails.composition.variants
                                    .push(orderData.recipeDetails.ingredient.variants[k].details[i]);
                            }
                        }
                    }
                }
                if (orderData.recipeDetails.ingredient.products != null
                    && orderData.recipeDetails.ingredient.products.length > 0) {
                    for (var k in orderData.recipeDetails.ingredient.products) {
                        for (var i in orderData.recipeDetails.ingredient.products[k].details) {
                            if (orderData.recipeDetails.ingredient.products[k].details[i].selected) {
                                orderData.orderDetails.composition.products
                                    .push(orderData.recipeDetails.ingredient.products[k].details[i]);
                            }
                        }
                    }
                }
                if (orderData.recipeDetails.ingredient.components != null
                    && orderData.recipeDetails.ingredient.components.length > 0) {
                    for (var k in orderData.recipeDetails.ingredient.components) {
                            if (orderData.recipeDetails.ingredient.components[k].display===true) {
                                orderData.orderDetails.composition.others
                                    .push(orderData.recipeDetails.ingredient.components[k]);
                        }
                    }
                }
                if (orderData.recipeDetails.ingredient.compositeProduct != null
                    && orderData.recipeDetails.ingredient.compositeProduct.details != null
                    && orderData.recipeDetails.ingredient.compositeProduct.details.length > 0) {
                    var finalArray = [];
                    for (var k in orderData.recipeDetails.ingredient.compositeProduct.details) {
                        for (var i in orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts) {
                            if (orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts[i].selected) {
                                var menuProduct = angular
                                    .copy(orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts[i]);
                                menuProduct.name = orderData.recipeDetails.ingredient.compositeProduct.details[k].name;
                                menuProduct.item = addNewItemForCombo(menuProduct.product.productId,
                                    menuProduct.dimension.code, menuProduct.quantity, itemId + finalArray.length);
                                delete menuProduct['$$hashKey'];
                                // //console.log('Menu Clone Product ',
                                // menuProduct);
                                finalArray.push(menuProduct);
                            }
                        }
                    }
                    /*
                     * //console.log('Before Final Array ',finalArray);
                     * finalArray = JSON.parse(angular.toJson(finalArray));
                     * //console.log('After Final Array ',finalArray);
                     */
                    orderData.orderDetails.composition.menuProducts = finalArray;
                }
                if (orderData.recipeDetails.addons != null && orderData.recipeDetails.addons.length > 0) {
                    for (var i in orderData.recipeDetails.addons) {
                        if (orderData.recipeDetails.addons[i].selected) {
                            orderData.orderDetails.composition.addons.push(orderData.recipeDetails.addons[i]);
                        }
                    }
                }
                if (orderData.recipeDetails.options != null && orderData.recipeDetails.options.length > 0) {
                    for (var i in orderData.recipeDetails.options) {
                        if (orderData.recipeDetails.options[i].selected) {
                            orderData.orderDetails.composition.options.push(orderData.recipeDetails.options[i].name);
                        }
                    }
                }

            }
            console.log('After Calculation Composition ',
            orderData.orderDetails.composition);

            return orderData;
        }

        function calculateFavCustomization(orderData, itemId, customizationList) {
            if (orderData.recipeDetails == null) {
                return orderData;
            }
            orderData.orderDetails.composition = getEmptyComposition();
            //Variants --->
                if (orderData.recipeDetails.ingredient != null) {
                    if (orderData.recipeDetails.ingredient.variants != null
                        && orderData.recipeDetails.ingredient.variants.length > 0) {
                        for (var k in orderData.recipeDetails.ingredient.variants) {
                            for (var i in orderData.recipeDetails.ingredient.variants[k].details) {
                                if (customizationList.indexOf(orderData.recipeDetails.ingredient.variants[k].details[i].alias)>=0) {
                                    orderData.recipeDetails.ingredient.variants[k].details[i].selected=true;
                                    orderData.orderDetails.composition.variants.push(orderData.recipeDetails.ingredient.variants[k].details[i]);
                                }else{
                                    orderData.recipeDetails.ingredient.variants[k].details[i].selected=false;
                                }
                            }
                        }
                    }
                    if (orderData.recipeDetails.ingredient.products != null
                        && orderData.recipeDetails.ingredient.products.length > 0) {
                        for (var k in orderData.recipeDetails.ingredient.products) {
                            for (var i in orderData.recipeDetails.ingredient.products[k].details) {
                                if (customizationList.indexOf(orderData.recipeDetails.ingredient.products[k].details[i].alias)>=0) {
                                    orderData.recipeDetails.ingredient.products[k].details[i].selected = true;
                                    orderData.orderDetails.composition.products.push(orderData.recipeDetails.ingredient.products[k].details[i]);
                                }
                            }
                        }
                    }

                    if(orderData.recipeDetails.ingredient.components !=null && orderData.recipeDetails.ingredient.components.length>0){
                        for (var k in orderData.recipeDetails.ingredient.components) {
                            if (orderData.recipeDetails.ingredient.components[k].display === true) {
                                orderData.orderDetails.composition.others
                                    .push(orderData.recipeDetails.ingredient.components[k]);
                            }
                        }
                    }

                    if (orderData.recipeDetails.addons != null && orderData.recipeDetails.addons.length > 0) {
                        for (var i in orderData.recipeDetails.addons) {
                            if (customizationList.indexOf(orderData.recipeDetails.addons[i].product.name)>=0) {
                                orderData.recipeDetails.addons[i].selected = true;
                                orderData.orderDetails.composition.addons.push(orderData.recipeDetails.addons[i]);
                            }
                        }
                    }
                    if (orderData.recipeDetails.options != null && orderData.recipeDetails.options.length > 0) {
                        for (var i in orderData.recipeDetails.options) {
                            if (customizationList.indexOf(orderData.recipeDetails.options[i].name)>=0) {
                                orderData.recipeDetails.options[i].selected = true;
                                orderData.orderDetails.composition.options.push(orderData.recipeDetails.options[i].name);
                            }
                        }
                    }
                }

            console.log("OrderItem After Setting Fav Chai Customizations ::::::::::", orderData);
            return orderData;
        }

        function getSwiggyCustomer() {
            return service.swiggyCustomer;
        }

        function getZomatoCustomer() {
            return service.zomatoCustomer;
        }

        function loadSwiggyCustomer() {
            var reqObj = {};
            reqObj.contactNumber = swiggyCustomerNo;
            reqObj.customer = {};
            reqObj.customer.contactNumber = swiggyCustomerNo;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            $rootScope.showFullScreenLoader = true;
            var response = posAPI.allUrl('/', service.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    service.swiggyCustomer = response.customer;
                    $rootScope.showFullScreenLoader = false;
                    return response.customer;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    myAlert.myAlert(err.data.errorMessage);
                });
            return response;
        }

        function loadZomatoCustomer() {
            var reqObj = {};
            reqObj.contactNumber = zomatoCustomerNo;
            reqObj.customer = {};
            reqObj.customer.contactNumber = reqObj.contactNumber;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            $rootScope.showFullScreenLoader = true;
            var response = posAPI.allUrl('/', service.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    service.zomatoCustomer = response.customer;
                    $rootScope.showFullScreenLoader = false;
                    return response.customer;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    myAlert.myAlert(err.data.errorMessage);
                });
            return response;
        }

        function loadAmazonCustomer() {
            var reqObj = {};
            reqObj.contactNumber = amazonCustomerNo;
            reqObj.customer = {};
            reqObj.customer.contactNumber = amazonCustomerNo;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            $rootScope.showFullScreenLoader = true;
            console.log(reqObj);
            var response = posAPI.allUrl('/', service.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    service.amazonCustomer = response.customer;
                    $rootScope.showFullScreenLoader = false;
                    return response.customer;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    myAlert.myAlert(err.data.errorMessage);
                });
            return response;
        }

        function getEmployeeMealProductsData(callback) {
            if (service.unitDetails.employeeMealProducts == undefined || service.unitDetails.employeeMealProducts == null || service.unitDetails.employeeMealProducts.length == 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.employeeMealProducts)
                    .post(service.unitDetails.id)
                    .then(function (response) {
                            var data = response.plain();
                            service.unitDetails.employeeMealProducts = data;
                            addEmployeeMealProductPricesToUnit(data);
                            //localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                            $rootScope.showFullScreenLoader = false;
                            if (typeof callback == "function") {
                                callback();
                            }
                        },
                        function (err) {
                            $rootScope.showFullScreenLoader = false;
                            service.myAlert(err.data.errorMessage);
                            if (typeof callback == "function") {
                                callback();
                            }
                        });
            } else if (typeof callback == "function") {
                callback();
            }
        };

        function addEmployeeMealProductPricesToUnit(products) {
            service.unitDetails.empMealPrices = {};
            for (var i in products) {
                var product = products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    service.unitDetails.empMealPrices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                    localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                }
            }
        };

        function checkUnitProductsData() {
            service.unitDetails = localStorage.getItem("unitDetails") != null ? JSON.parse(localStorage.getItem("unitDetails")) : null;
            if (service.unitDetails.products != null || service.unitDetails.products.length > 0) {
                $rootScope.showFullScreenLoader = true;
                var req = {unitId: service.unitDetails.id, productDimensionRecipeIdMap: {}};
                service.unitDetails.products.map(function (product) {
                    req.productDimensionRecipeIdMap[product.id] = {};
                    product.prices.map(function (price) {
                        req.productDimensionRecipeIdMap[product.id][price.dimension] = price.recipeId;
                    })
                });
                posAPI.allUrl('/', service.restUrls.unitMetaData.checkUnitProductRecipes).post(req)
                    .then(function (response) {
                        if (response != true) {
                            service.clearProductCache();
                        }
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        service.myAlert(err.data.errorMessage);
                    });
            }
        };

        function getCurrentMenuType(){
            var getHour = new Date();
            var hour=localStorage.getItem("serverTime");
            hour=Math.abs(hour-getHour.getHours());
            if ((hour >= 5 && hour <= 11)) {
                return "DAY_SLOT_BREAKFAST";
            } else if (hour >= 12 && hour <= 14) {
                return "DAY_SLOT_LUNCH";
            } else if (hour >= 15 && hour <= 19) {
                return "DAY_SLOT_EVENING";
            } else if (hour >= 20 && hour <= 21) {
                return "DAY_SLOT_DINNER";
            } else if ((hour >= 22 && hour <= 23)) {
                return "DAY_SLOT_POST_DINNER";
            } else if (hour >= 0 && hour <= 4) {
                return "DAY_SLOT_OVERNIGHT";
            }
            return "DEFAULT";
        };

        function addSubProducts(products){
            var reqProducts = [];
                for(var i in products){
                    if(products[i].subType === 3810){
                        reqProducts.push(products[i]);
                    }
                }
            var productIds = [];
            var productName = [];

            for(var i in reqProducts){
                productIds.push(reqProducts[i].id)
                var pro = {
                    productName:reqProducts[i].name,
                    productSKU:reqProducts[i].skuCode
                }
                productName.push(pro);
            }
            console.log(productIds);
            localStorage.setItem("Subscription_Products",JSON.stringify(productIds));
            localStorage.setItem("Subscription_Products_Name",JSON.stringify(productName));
            console.log(localStorage.getItem("Subscription_Products"));
        }

        function getUnitProductsData(callback) {
            service.unitDetails = localStorage.getItem("unitDetails") != null ? JSON.parse(localStorage.getItem("unitDetails")) : null;
            if (service.unitDetails.products == undefined || service.unitDetails.products == null || service.unitDetails.products.length == 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.unitProducts)
                //.all("unit")
                    .post(service.unitDetails.id)
                    .then(function (response) {
                            var data = response.plain();
                            service.unitDetails.products = data.products;
                            service.unitProductMap = {};
                            arrangeProducts(service.unitDetails.products, service.categoryList, service.productMap, service.unitProductMap);
                            var taxData = {};
                            for (var i in data.taxes) {
                                var tax = data.taxes[i];
                                taxData[tax.taxCode] = tax;
                            }
                            Object.keys(data.categoryMap).map(function (brandId) {
                                var foodIndex = data.categoryMap[brandId].indexOf(7);
                                if(foodIndex >= 0) {
                                    data.categoryMap[brandId].splice(foodIndex, 1, 701,702);
                                }
                            });
                            service.unitDetails.categoryMap = data.categoryMap;
                            // service.unitDetails.priceMap = preparePriceMap(data.priceMap);
                            service.unitDetails.taxes = taxData;
                            // addProductPricesToUnit(data.products);
                            addProductPricesToUnit(data.products,data.priceMap);
                            localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                            //console.log('Selected Unit', service.unitDetails);
                            addSubProducts(data.products)
                            $rootScope.showFullScreenLoader = false;
                            if (typeof callback == "function") {
                                callback();
                            }
                        },
                        function (err) {
                            $rootScope.showFullScreenLoader = false;
                            service.myAlert(err.data.errorMessage);
                        });
            } else {
                if (isEmptyObject(service.productMap)) {
                    service.unitProductMap = {};
                    arrangeProducts(service.unitDetails.products, service.categoryList, service.productMap, service.unitProductMap);
                }
                if(service.unitDetails.priceMap!==undefined && service.unitDetails.priceMap!==null && Object.keys(service.unitDetails.priceMap).length>0){
                    if (service.unitDetails.currentMenuType !== getCurrentMenuType()) {
                        var productDesiChai;
                        var productBlackTea;
                        if (service.unitDetails.prices != undefined &&
                            service.unitDetails.prices != null && Object.keys(service.unitDetails.prices).length>0
                            && service.unitDetails.prices["10_Regular"] != undefined
                            && service.unitDetails.priceMap[getCurrentMenuType()]!= undefined
                            && service.unitDetails.priceMap[getCurrentMenuType()]!= null
                            && service.unitDetails.prices["10_Regular"].price != null) {
                            productDesiChai = service.unitDetails.priceMap[getCurrentMenuType()]["10_Regular"].price;
                        } else {
                            productDesiChai = service.unitDetails.originalPrices["10_Regular"].price;
                        }
                        if (service.unitDetails.prices != undefined &&
                            service.unitDetails.prices != null && Object.keys(service.unitDetails.prices).length>0 && service.unitDetails.prices["1205_Regular"] != undefined
                            && service.unitDetails.prices["1205_Regular"].price != null
                            && service.unitDetails.priceMap[getCurrentMenuType()]!= undefined
                            && service.unitDetails.priceMap[getCurrentMenuType()]!= null) {
                            productBlackTea = service.unitDetails.priceMap[getCurrentMenuType()]["1205_Regular"].price;
                        } else {
                            productBlackTea = service.unitDetails.originalPrices["1205_Regular"].price;
                        }
                        var data = [
                            {
                                productName: "Desi Chai",
                                currentPrice: service.unitDetails.prices["10_Regular"].price,
                                expectedPrice: productDesiChai
                            },
                            {
                                productName: "Black Tea",
                                currentPrice: service.unitDetails.prices["1205_Regular"].price,
                                expectedPrice: productBlackTea
                            }
                        ];
                        if(service.unitDetails.priceMap[getCurrentMenuType()]!= undefined
                        && service.unitDetails.priceMap[getCurrentMenuType()]!= null){
                            var modalInstance = $modal.open({
                                animation: true,
                                templateUrl: window.version + 'views/expectedPriceProfileModal.html',
                                controller: 'expectedPriceProfileModalCtrl',
                                backdrop: 'static',
                                size: "lg",
                                resolve: {
                                    requestData: function () {
                                        return data;
                                    }
                                }
                            });
                        }
                    }
                }
                if (typeof callback == "function") {
                    callback();
                }
            }
        };

        function setPriceMap(){
            var priceMap={};
            service.unitDetails.currentMenuType = getCurrentMenuType();
            if (service.unitDetails.priceMap!=undefined && service.unitDetails.priceMap!=null && service.unitDetails.priceMap[getCurrentMenuType()] !== undefined
                && service.unitDetails.priceMap[getCurrentMenuType()] !== null && Object.keys(service.unitDetails.priceMap[getCurrentMenuType()]).length > 0) {
                priceMap= service.unitDetails.priceMap[getCurrentMenuType()];
            }
            if( priceMap!==undefined && priceMap!==null && Object.keys(priceMap).length > 0){
                service.unitDetails.prices=priceMap;
            }
            else{
                service.unitDetails.prices=service.unitDetails.originalPrices;
            }
            localStorage.removeItem("unitDetails");
            localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
        }

        function preparePriceMap(products,priceMap){
            // if(priceMap!=undefined && priceMap!=null && priceMap[getCurrentMenuType()] !== undefined && priceMap[getCurrentMenuType()] !== null && Object.keys(priceMap[getCurrentMenuType()]).length > 0){
            service.unitDetails.currentMenuType = getCurrentMenuType();
            // }
            // else{
            //     service.unitDetails.currentMenuType =undefined;
            // }
            service.unitDetails.priceMap = {};
            for (var i in priceMap){ //Map<MenuType, Map<Integer, List<ProductPriceVO>>>
                var priceValue=priceMap[i];
                var priceList={};
                for (var j in priceValue) {//Map<Integer, List<ProductPriceVO>>
                    var productPriceVO=priceValue[j];
                    for (var key in productPriceVO) {//List<ProductPriceVO>
                        var priceCode = j + '_' +productPriceVO[key].dimension;
                        var val={
                            price:service.unitDetails.prices[priceCode].price,
                            taxCode:service.unitDetails.prices[priceCode].taxCode
                        };
                        val.price=productPriceVO[key].price;
                        priceList[priceCode]=val;
                    }
                }
                service.unitDetails.priceMap[i]=priceList;//MenuType,PriceList
            }
            console.log(service.unitDetails.priceMap);
        };

        function clearProductCache() {
            service.unitDetails.products = [];
            localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
        }

        function addProductPricesToUnit(products,priceMap) {
            service.unitDetails.prices = {};
            service.unitDetails.originalPrices = {};
            for (var i in products) {
                var product = products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    service.unitDetails.prices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                    service.unitDetails.originalPrices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                }
            }
            preparePriceMap(products,priceMap);
            var priceMapDetail = service.unitDetails.priceMap[getCurrentMenuType()];
            if( priceMapDetail !== undefined && priceMapDetail!==null && Object.keys(priceMapDetail).length>0){
                service.unitDetails.prices=priceMapDetail;
            }
        }

        function addNewItem(productItem, quantity, hasRedeemed, itemId,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping,offerAmount) {
            //verify for delievry only product . If delivery only product is true for all dimensions , dont show it on ui .
            if(!service.isCOD()){
                if( service.checkIsDeliveryOnlyProduct(productItem)){
                    bootbox.alert("This product is a delivery only product !");
                }else{
                    if(service.nonDeliveryOnlyProductIndex>=0){
                        return addNewItemForIndex(productItem, quantity, hasRedeemed, productItem.prices[service.nonDeliveryOnlyProductIndex], itemId,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping,offerAmount);
                    }
                }
            }else{
                return addNewItemForIndex(productItem, quantity, hasRedeemed, productItem.prices[0], itemId,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping,offerAmount);
            }
        }

        function checkIsDeliveryOnlyProduct(productItem){
            var isDeliveryOnlyProduct = true;
            if(productItem!=null ){
                if(!service.isEmptyObject(productItem)){
                    for (var i in productItem.prices){
                        if(productItem.prices[i].isDeliveryOnlyProduct===false){
                            isDeliveryOnlyProduct=false;
                            service.nonDeliveryOnlyProductIndex= i;
                            break;
                        }
                    }
                }
            }
            return isDeliveryOnlyProduct;
        }

        function addNewItemForCombo(productId, dimensionCode, quantity, itemId) {
            var productItem = getProductForId(productId);
            var orderItem = null;
            for (var index in productItem.prices) {
                if (productItem.prices[index].dimension == dimensionCode) {
                    orderItem = addNewItemForIndex(productItem, quantity, false, productItem.prices[index], itemId);
                }
            }
            if (orderItem == null) {
                orderItem = addNewItemForIndex(productItem, quantity, false, productItem.prices[0], itemId);
            }
            // //console.log('Menu Order Item', orderItem);
            return orderItem;

        }

        function addNewItemForIndex(productItem, quantity, hasRedeemed, priceData, itemId,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping,offerAmount) {
            var amount = (priceData.price) * quantity;
            var unitDetails=JSON.parse(localStorage.getItem("unitDetails"));
            var price ;
            if(unitDetails.prices!=null && unitDetails.prices!=undefined && unitDetails.prices[productItem.id+"_"+priceData.dimension]!=undefined && unitDetails.prices[productItem.id+"_"+priceData.dimension]!=null){
                price=unitDetails.prices[productItem.id+"_"+priceData.dimension].price;
            }
            else {
                price=priceData.price
            }
            var originalPrices ;
            if(unitDetails.originalPrices!=null && unitDetails.originalPrices!=undefined && unitDetails.originalPrices[productItem.id+"_"+priceData.dimension]!=undefined && unitDetails.originalPrices[productItem.id+"_"+priceData.dimension]!=null){
                originalPrices=unitDetails.originalPrices[productItem.id+"_"+priceData.dimension].price;
            }
            else {
                originalPrices=priceData.price
            }
            var isCombo = productItem.type == 8;
            // //console.log('Is Combo ', isCombo);
            var orderDetails = {
                itemId: itemId,
                productId: productItem.id,
                productName: productItem.name,
                productType: productItem.type,
                billType: productItem.billType,
                addons: [],
                complimentaryDetail: {
                    isComplimentary: false,
                    reasonCode: null,
                    reason: null
                },
                quantity: quantity,
                price: price,
                offerAmount:offerAmount,
                originalPrice: originalPrices,
                amount: amount,
                discountDetail: {
                    discountCode: null,
                    discountReason: null,
                    promotionalOffer: 0,
                    discount: {
                        percentage: 0,
                        value: 0
                    },
                    totalDiscount: 0
                },
                dimension: priceData.dimension,
                hasBeenRedeemed: hasRedeemed,
                isCombo: isCombo,
                recomCategory:productItem.recomCategory
        };

            var singleOrderItem = {
                orderDetails: orderDetails,
                productDetails: productItem
            };
            singleOrderItem.recipeDetails = resetOrderItemCustomization(angular.copy(priceData.recipe));
            if(customizationList!=null && customizationList.length>0 && customizationList!=undefined){
                singleOrderItem = calculateFavCustomization(singleOrderItem, itemId, customizationList);
            }else{
                singleOrderItem = calculateCustomization(singleOrderItem, itemId);
            }
            // //console.log('singleOrderItem ', singleOrderItem);
            singleOrderItem.orderDetails.recipeId = priceData.recipeId;
            singleOrderItem.isDefaultFavChaiSelected=isDefaultFavChaiSelected;
            singleOrderItem.isFavChaiMarked=isDefaultFavChaiSelected;
            return singleOrderItem;
        }

        function getProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                var productObj = service.unitDetails.products[i];
                if (productObj.id == productId) {
                    return productObj;
                }
            }
        }

        function isInventoryTrackedProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                var productObj = service.unitDetails.products[i];
                if (productObj.id == productId) {
                    return productObj.inventoryTracked;
                }
            }
            return false;
        }

        function hasProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                if (service.unitDetails.products[i].id == productId) {
                    return true;
                    ;
                }
            }
            return false;
        }

        function checkWorkstationEnabled() {
            if (service.unitDetails != undefined) {
                return service.unitDetails.workstationEnabled;
            } else {
                return false;
            }
        }

        function checkTokenEnabled() {
            if (service.unitDetails != undefined) {
                return service.unitDetails.tokenEnabled;
            } else {
                return false;
            }
        }

        function checkIsCafe() {
            // console.log('Inside checkIsCafe')
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'CAFE' || unitFamily == "CHAI_MONK";
            }
            return true;
        }

        function checkIsMonk() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'CHAI_MONK';
            }
            return true;
        }

        function checkIsDelivery() {
            // console.log('Inside checkIsDelivery')
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'DELIVERY';
            }
            return true;
        }

        // TODO This needs to be moved to the backend. As of now there is a
        // check on unit id for Galleria
        function hasSeparateKotPrinting() {
            return false;//service.unitDetails != null && service.unitDetails.id == 10005;
        }

        function getUnitFamily() {
            if (isEmptyObject(service.unitFamily) && $rootScope.globals != undefined) {
                var currentUser = $rootScope.globals.currentUser;
                if (currentUser != undefined) {
                    return currentUser.unitFamily;
                }
            }
            return service.unitFamily;
        }

        function getSettlements(orderObj) {
            var settlementString = "";
            var settlementsArray = [];
            // //console.log("inside getSettlements");
            if (orderObj != undefined && orderObj.settlements != undefined) {
                for (var i = 0; i < orderObj.settlements.length; i++) {
                    settlementsArray.push(orderObj.settlements[i].modeDetail.name + ":"
                        + orderObj.settlements[i].amount);
                }
            }
            settlementString = settlementsArray.join(",");
            // //console.log(settlementString);
            return settlementString;
        }

        function getCSAddress() {
            return service.CSObj.addressess;
        }

        function stdDialogueAlert(text, yesCallback, noCallback) {
            bootbox.confirm(text, function (result) {
                if (result == true) {
                    yesCallback();
                } else {
                    noCallback();
                }
            });
        }

        function GetRequest(obj) {
            var requestObj = {};
            if (typeof obj != 'string') {
                requestObj = {
                    session: $rootScope.globals.currentUser,
                    data: JSON.stringify(obj)
                };
            } else {
                requestObj = {
                    session: $rootScope.globals.currentUser,
                    data: obj
                };
            }

            return requestObj;
        }


                 function GetRequestWithoutStringify(obj) {
                             var requestObj = {};

                                 requestObj = {
                                     session: $rootScope.globals.currentUser,
                                     data: obj
                                 };


                             return requestObj;
                         }

        function clearSpecialChars(str) {
            return str.replace(/(a1)|(a0)|(i)|(!)|(M0)|(E)|(E)/g, '');
        }

        function isEmptyObject(obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else if (typeof obj == 'boolean')
                    return false;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        }

        function GetLoggedInUser() {
            return $rootScope.globals.currentUser.userId;
        }

        function myAlert(message) {
            Flash.create('danger', message, null);
        }

        function mySuccessAlert(message) {
            Flash.create('success', message, null);
        }

        function myInfoAlert(message) {
            Flash.create('info', message, null);
        }

        function myWarningAlert(message) {
            Flash.create('warning', message, null);
        }

        function refreshRedeemLock() {
            setPointsRedeemed(0, false);
        }

        function lockRedeem(loyaltyPointsRedeemed) {
            setPointsRedeemed(loyaltyPointsRedeemed, true);
        }

        function setPointsRedeemed(loyaltyPointsRedeemed, status) {
            var pointsRedeemed = {
                pointsRedeemed: loyaltyPointsRedeemed,
                pointsRedeemedSuccessfully: status
            };
            $cookieStore.put('pointsRedeemed', pointsRedeemed);
            // //console.log($cookieStore.get('pointsRedeemed'));
        }

        function getTaxProfile() {
            startSpin('spinner-1');
            var unitId = getSelectedUnitId();
            if (service.taxProfileCache[unitId] == undefined || service.taxProfileCache[unitId] == null) {
                posAPI.allUrl('/', service.restUrls.posMetaData.taxProfile).customGET("", {
                    unitId: unitId
                }).then(function (response) {
                    // //console.log(response.plain());
                    service.taxProfileCache[unitId] = response.plain();
                    service.unitDetails.taxProfiles = service.taxProfileCache[unitId];
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                service.unitDetails.taxProfiles = service.taxProfileCache[unitId];
            }

        }

        function getPartnerId() {
            var partnerId = service.partnerId;
            return partnerId != null ? partnerId : 2; //2 is partnerId for chaayos delivery
        }

        function setPartnerId(partnerId) {
            service.partnerId = partnerId;
        }

        function getPackagingProfile() {
            startSpin('spinner-1');
            var unitId = getSelectedUnitId();
            var brandId = getSelectedBrand().brandId;
            var partnerId = getPartnerId();
            var packagingProfileKey = getPackagingProfileKey(unitId, brandId, partnerId);
            if (service.packagingProfileCache[packagingProfileKey] == undefined || service.packagingProfileCache[packagingProfileKey] == null) {
                posAPI.allUrl('/', service.restUrls.posMetaData.brandPartnerPackagingProfile).customGET("", {
                    unitId: unitId,
                    brandId: brandId,
                    partnerId: partnerId
                }).then(function (response) {
                    // //console.log(response.plain());
                    service.packagingProfileCache[packagingProfileKey] = response.plain();
                    service.unitDetails.packagingType = service.packagingProfileCache[packagingProfileKey].packagingType;
                    service.unitDetails.packagingValue = service.packagingProfileCache[packagingProfileKey].packagingValue;
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                service.unitDetails.packagingType = service.packagingProfileCache[packagingProfileKey].packagingType;
                service.unitDetails.packagingValue = service.packagingProfileCache[packagingProfileKey].packagingValue;
            }
        }

        function getPackagingProfileKey(unitId, brandId, partnerId) {
            return unitId + ":" + brandId + ":" + partnerId;
        }

        function validCache(region) {
            var isPartnerOrder = $rootScope.isPartnerOrder != null ? $rootScope.isPartnerOrder : false;
            var regionObj = service.regionCache[getRegionKey(region, isPartnerOrder)];
            return regionObj != null && regionObj.products != null && regionObj.taxData != null && regionObj.prices != null;
        }


        function getProductProfile(customer, callback) {
            var city = getSelectedCity(customer);
            getProductProfileByCity(city, callback);
        }

        function getBrandProductProfile(brandId, unitId, partnerId, callback) {
            getBrandUnitDeliveryProductProfile(brandId, unitId, partnerId, callback);
        }

        function getProductProfileByCity(city, callback) {
            startSpin('spinner-1');
            $rootScope.showFullScreenLoader = true;
            var isPartnerOrder = $rootScope.isPartnerOrder != null && $rootScope.isPartnerOrder != undefined ? $rootScope.isPartnerOrder
                : false;
            var region = null;
            getUnitBasicDetails(function () {
                service.unitBasicDetailList.map(function (ubd) {
                    if (getSelectedUnitId() === ubd.id) {
                        region = ubd.region;
                    }
                });
            });
            if (region == null) {
                region = getCODRegion(city);
            }

            if (!validCache(region)) {
                posAPI.allUrl('/', service.restUrls.unitMetaData.productProfile).customGET("", {
                    region: region,
                    unitId: getSelectedUnitId(),
                    partnerOrder: isPartnerOrder
                }).then(function (response) {
                    var data = response.plain();
                    var taxData = {};
                    for (var i in data.taxes) {
                        var tax = data.taxes[i];
                        taxData[tax.taxCode] = tax;
                    }
                    var cacheData = {};
                    cacheData.products = data.products;
                    cacheData.taxData = taxData;
                    setUnitDetailData(cacheData);
                    service.unitDetails.prices = cacheData.prices;
                    console.log('Selected Unit', service.unitDetails);
                    service.unitDetails.products = data.products;
                    service.unitDetails.taxes = taxData;
                    service.regionCache[getRegionKey(region, isPartnerOrder)] = cacheData;
                    // This code needs to be changed and merged with
                    // coverController.
                    // Its a duplicate
                    stopSpin('spinner-1');
                    $rootScope.showFullScreenLoader = false;
                    if (typeof callback == "function") {
                        callback();
                    }
                    $location.url('/pos');
                }, function (err) {
                    stopSpin('spinner-1');
                    $rootScope.showFullScreenLoader = false;
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                var data = service.regionCache[getRegionKey(region, isPartnerOrder)];
                service.unitDetails.products = data.products;
                service.unitDetails.taxes = data.taxData;
                // This code needs to be changed and merged with
                // coverController.
                // Its a duplicate
                service.unitDetails.prices = data.prices;
                if (typeof callback == "function") {
                    callback();
                }
                stopSpin('spinner-1');
                $rootScope.showFullScreenLoader = false;
                $location.url('/pos');
            }
        }


        function getBrandUnitDeliveryProductProfile(brandId, unitId, partnerId, callback) {
            startSpin('spinner-1');
            $rootScope.showFullScreenLoader = true;
            var isPartnerOrder = $rootScope.isPartnerOrder != null && $rootScope.isPartnerOrder != undefined ? $rootScope.isPartnerOrder
                : false;
            if (!isPartnerOrder) {
                partnerId = 2; //For Chaayos delivery
            }
            var deliveryProfileUnitId = null;
            service.unitPartnerBrandMappings.map(function (mapping) {
                if (mapping.unitId == unitId && mapping.brandId == brandId && mapping.partnerId == partnerId) {
                    deliveryProfileUnitId = mapping.priceProfileUnitId;
                }
            });
            if (deliveryProfileUnitId != null) {
                if (service.deliverProfileUnitCache[deliveryProfileUnitId] != null) {
                    var data = service.deliverProfileUnitCache[deliveryProfileUnitId];
                    service.unitDetails.products = data.products;
                    service.unitDetails.taxes = data.taxData;
                    // This code needs to be changed and merged with
                    // coverController.
                    // Its a duplicate
                    service.unitDetails.prices = data.prices;
                    service.unitDetails.originalPrices = data.originalPrices;
                    if (typeof callback == "function") {
                        callback();
                    }
                    stopSpin('spinner-1');
                    $rootScope.showFullScreenLoader = false;
                    $location.url('/pos');
                } else {
                    posAPI.allUrl('/', service.restUrls.unitMetaData.brandPartnerProductProfile).customGET("", {
                        brandId: brandId,
                        unitId: unitId,
                        partnerId: partnerId
                    }).then(function (response) {
                        var data = response.plain();
                        var taxData = {};
                        for (var i in data.taxes) {
                            var tax = data.taxes[i];
                            taxData[tax.taxCode] = tax;
                        }
                        var cacheData = {};
                        cacheData.products = data.products;
                        cacheData.taxData = taxData;
                        setUnitDetailData(cacheData);
                        service.unitDetails.prices = cacheData.prices;
                        service.unitDetails.originalPrices = cacheData.originalPrices;
                        console.log('Selected Unit', service.unitDetails);
                        service.unitDetails.products = data.products;
                        service.unitDetails.taxes = taxData;
                        service.deliverProfileUnitCache[deliveryProfileUnitId] = cacheData;
                        // This code needs to be changed and merged with
                        // coverController.
                        // Its a duplicate
                        stopSpin('spinner-1');
                        $rootScope.showFullScreenLoader = false;
                        if (typeof callback == "function") {
                            callback();
                        }
                        $location.url('/pos');
                    }, function (err) {
                        stopSpin('spinner-1');
                        $rootScope.showFullScreenLoader = false;
                        service.myAlert(err.data.errorMessage);
                    });
                }
            } else {
                $rootScope.showFullScreenLoader = false;
                service.myAlert("Pricing unit not mapped.");
            }
        }


        function getUnitBasicDetails() {
            if (service.unitBasicDetailList == null || service.unitBasicDetailList.length === 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.allUnitsList).customGET("", {}).then(function (response) {
                    if (response != null && response.length > 0) {
                        service.unitBasicDetailList = response;
                    }
                    $rootScope.showFullScreenLoader = false;
                    if (typeof callback == "function") {
                        callback();
                    }
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                if (typeof callback == "function") {
                    callback();
                }
            }
        }

        function setUnitDetailData(data) {
            data.prices = {};
            data.originalPrices = {};
            for (var i in data.products) {
                var product = data.products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    data.prices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                    data.originalPrices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                }
            }
        }

        function getSelectedCity(customer) {
            var city = "DELHI";
            if (customer.newAddress != undefined) {
                city = customer.newAddress.city;
            } else {
                customer.addresses.forEach(function (address) {
                    if (address.isSelectedAddress) {
                        city = address.city;
                    }
                });
            }
            return city;
        }

        function getCODRegion(city) {
            var region = "NCR";
            if (city.toUpperCase() == "MUMBAI") {
                region = "MUMBAI";
            }
            if (city.toUpperCase() == "CHANDIGARH") {
                region = "CHANDIGARH";
            }
            if (city.toUpperCase() == "CHANDIGARH") {
                region = "CHANDIGARH";
            }
            //Chutzpah for IIT Delhi
            if (getSelectedUnitId() == 26039) {
                region = "NCR_EDU";
            }
            return region;
        }

        function getRegionKey(region, isPartnerOrder) {
            return isPartnerOrder ? region + "-PARTNER" : region;
        }

        function getSelectedUnitId() {
            // //console.log("inside getSelectedUnitId function");
            var selectedUnitId = null;
            // //console.log(service.outlet);
            if (service.outlet.selectedId == 1) {
                selectedUnitId = service.outlet.pri_unitId;
            } else if (service.outlet.selectedId == 2) {
                selectedUnitId = service.outlet.sec_unitId;
            } else {
                selectedUnitId = service.outlet.ter_unitId;
            }
            // //console.log("selectedUnit is ::: " + selectedUnitId);
            return selectedUnitId;
        }

        function getDeliveryProfile(callback) {
            var unitId = getSelectedUnitId();
            if (service.deliveryProfileCache[unitId] == undefined || service.deliveryProfileCache[unitId] == null) {
                getDeliveryProfileForUnit(unitId, callback);
            } else {
                service.unitDetails.deliveryPartners = service.deliveryProfileCache[unitId];
            }
        }

        function getDeliveryProfileForUnit(selectedUnitId, callback) {
            startSpin('spinner-1');
            posAPI.allUrl('/', service.restUrls.posMetaData.deliveryProfile).customGET("", {
                unitId: selectedUnitId
            }).then(function (response) {
                service.deliveryProfileCache[selectedUnitId] = response.plain(); // setting
                // cache

                if(selectedUnitId!=null && selectedUnitId!=undefined){
                        if(service.deliveryProfileCache[selectedUnitId] !=null && service.deliveryProfileCache[selectedUnitId] !=undefined){
                            for(var i=0;i<service.deliveryProfileCache[selectedUnitId].length;i++){
                                if(service.deliveryProfileCache[selectedUnitId][i] !=null && service.deliveryProfileCache[selectedUnitId][i]["zone"] !=null) {
                                    service.setUnitIDZoneMap(selectedUnitId, service.deliveryProfileCache[selectedUnitId][i]["zone"]);
                                }
                            }
                        }
                }
                service.unitDetails.deliveryPartners = service.deliveryProfileCache[selectedUnitId];
                stopSpin('spinner-1');
                if (callback != undefined && typeof callback == "function") {
                    callback(service.unitDetails.deliveryPartners);
                }
            }, function (err) {
                service.myAlert(err.data.errorMessage);
            });

        }

        function checkInArrayOfPartners(partners, partnerid) {
            for (var i = 0; i < partners.length; i++) {
                var partner = partners[i];

                if (partner.id == partnerid) {
                    // //console.log("returning true");
                    return true;
                }
            }
            // //console.log("returning false");
            return false;
        }

        function startSpin(spinnerKey) {
            // //console.log("inside start spin function");
            usSpinnerService.spin(spinnerKey);
        }

        function stopSpin(spinnerKey) {
            // //console.log("inside stop spin function");
            usSpinnerService.stop(spinnerKey);
        }

        function openOrderSearch(generateOrderId, orderType) {
            $rootScope.orderType = orderType;
            // var reqObj = service.GetRequest(generateOrderId);
            posAPI.allUrl('/', service.restUrls.order.generatedOrder).post(generateOrderId)
                .then(
                    function (response) {
                        service.OrderObj = $rootScope.OrderObj = {};
                        if (response != undefined) {
                            service.OrderObj = $rootScope.OrderObj = response.plain();
                            service.showCancel = service.OrderObj.source == service.unitFamily;
                            service.showEditSettlement = service.OrderObj.source == 'CAFE' || service.OrderObj.source == 'TAKE_AWAY';
                            // //console.log(response.plain());
                            if (service.OrderObj.deliveryAddress != undefined
                                && service.OrderObj.deliveryAddress != null
                                && service.OrderObj.deliveryAddress > 0) {
                                GetCustomerInfo(service.OrderObj.deliveryAddress);
                                getDeliveryDetail(generateOrderId);
                            } else if (!service.isEmptyObject($rootScope.CustomerObj)) {
                                $rootScope.CustomerObj = {};
                            }

                            $location.url('/orderSearch');
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                    });

        }

        function GetCustomerInfo($deliveryAddressId) {
            // var reqObj = service.GetRequest($deliveryAddressId + "");
            posAPI.allUrl('/', service.restUrls.customer.lookupAddress).post($deliveryAddressId).then(function (response) {
                $rootScope.CustomerObj = {};
                $rootScope.CustomerObj = response.plain();
                angular.forEach($rootScope.CustomerObj.addresses, function (deliveryAddress) {
                    if (deliveryAddress.id === $deliveryAddressId) {
                        $rootScope.deliveryAddress = deliveryAddress;
                    }
                });
            }, function (err) {
                $rootScope.CustomerObj = {};
                service.myAlert(err.data.errorMessage);
            });
        }

        function getDeliveryDetail(generatedOrderId) {
            // var reqObj = service.GetRequest(generatedOrderId);
            // //console.log("inside getDelivery Detail for order Id :::: "
            // + generatedOrderId);
            posAPI.allUrl('/', service.restUrls.delivery.details)
                .post(generatedOrderId)
                .then(
                    function (response) {
                        $rootScope.deliveryObject = null;
                        if (response != undefined && response != null) {
                            $rootScope.deliveryObject = response.plain();
                            if ($rootScope.deliveryObject != null) {
                                console.log("inside if loop of empty check for deliveryObject");
                                $rootScope.deliveryObject.deliveryPartnerName = getDeliveryPartner($rootScope.deliveryObject.deliveryPartnerId);
                            }
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                        $rootScope.deliveryObject = null;
                    });
        }

        function isAndroid() {
            var isAndroid = false;
            // console.log('Called Is Android');
            // console.log(navigator.userAgent);
            if (/Android/i.test(navigator.userAgent)) {
                isAndroid = true;
            }
            return isAndroid;
        }

        function getDeliveryPartner(partnerId) {
            if (!isEmptyObject(service.transactionMetadata)) {
                var partners = service.transactionMetadata.deliveryPartner;
                // //console.log("inside getDeliveryPartner for partner id ::::
                // "
                // + partnerId);
                for (var i = 0; i < partners.length; i++) {
                    if (partnerId == partners[i].id) {
                        return partners[i].name;
                    }
                }
            }
            return "Delivery Partner not found";
        }

        function addOrderItems(items) {
            var orderItems = angular.copy(items);
            var orderItemResolvedArray = [];
            for (var i = 0; i < orderItems.length; i++) {
                if (orderItems[i].orderDetails.isCombo) {
                    for (var j in orderItems[i].orderDetails.composition.menuProducts) {
                        orderItems[i].orderDetails.composition.menuProducts[j] = orderItems[i].orderDetails.composition.menuProducts[j].item.orderDetails;
                    }
                }
                orderItems[i].orderDetails.brandId = orderItems[i].productDetails.brandId;
                //check if any fav chai orderItem exists in array ;If exists add preference to this orderItem
                checkForFavChaiOrderItem(orderItems[i]);
                orderItemResolvedArray.push(orderItems[i].orderDetails);
            }
            console.log(orderItemResolvedArray);
            return orderItemResolvedArray;
        }

        function checkForFavChaiOrderItem (orderItem){
            if(orderItem.isFavChaiMarked !=undefined && orderItem.isFavChaiMarked){
                if(service.customerFavChaiMappings!=null && service.customerFavChaiMappings.length>0){
                    for(var i in service.customerFavChaiMappings){
                        if(service.customerFavChaiMappings[i].productId == orderItem.productDetails.id){
                            getPreferenceObject(service.customerFavChaiMappings[i],orderItem);
                        }
                    }
                }
            }
        }
        function getPreferenceObject(customerFavChaiMapping, orderItem){
            orderItem.orderDetails.hasPreference=true;
            orderItem.orderDetails.preferenceDetail={};
            orderItem.orderDetails.preferenceDetail.preferenceName = customerFavChaiMapping.tagType;
            orderItem.orderDetails.preferenceDetail.preferenceId= customerFavChaiMapping.customizationId;
            orderItem.orderDetails.preferenceDetail.preferenceType="PREFERENCE";
        }

        function getOrderPointsRedeemed(orderItems) {
            var pointsRedeemed = 0;
            if (!service.customerSocket.eligibleForSignupOffer && !service.freeKettle) {
                orderItems.forEach(function (item) {
                    if (item.orderDetails.hasBeenRedeemed) {
                        pointsRedeemed = pointsRedeemed + 60;
                    }
                });
            }
            return pointsRedeemed != 0 ? -pointsRedeemed : 0;
        }

        function getProductsString(orderItemArray) {
            var orderItems = '';
            if (orderItemArray != undefined && orderItemArray != null) {
                orderItemArray.forEach(function (item) {
                    orderItems = orderItems + ',' + item.orderDetails.productId + '(' + item.orderDetails.quantity
                        + ')';
                });
                return orderItems.substring(1);
            } else {
                return '';
            }
        }

        function setCardType(value) {
            service.cardType = value;
        }

        function sendOrder(order, $modalInstance, callback, customerPresent, orderItemArray) {
            // //console.log(order);
            var orderCreationCallbackFunction = function () {
                $modalInstance.dismiss('submit');
                $rootScope.showFullScreenLoader = true;
                sendOrderforCreation(order, service.unitDetails, service.isAndroid, customerPresent, callback);
            };
            if (service.cardType != 'ECARD' && service.cardType != 'GYFTR' &&  service.cardType != 'AP01'  && service.cardType != 'MICRO'&& checkGiftCardInOrder(order)) {
                validateGiftCardsInOrder(order, orderCreationCallbackFunction, orderItemArray);
            } else {
                orderCreationCallbackFunction();
            }

        }

        function checkGiftCardInOrder(order) {
            var zeroTaxItemList = order.orders.filter(function (orderItem) {
                return orderItem.code == "GIFT_CARD";
            });
            return zeroTaxItemList.length > 0;
        }


        function sendOrderforCreation(order, unitDetails, isAndroid, customerPresent, callBack) {
            // var reqObj = service.GetRequest(order);
            if ($rootScope.orderType == "order" || $rootScope.orderType == "employee-meal" || $rootScope.orderType == "paid-employee-meal") {
                createOrder(order, unitDetails, isAndroid, customerPresent, callBack);
            } else if ($rootScope.orderType == "complimentary-order") {
                createOrder(order, unitDetails, isAndroid, customerPresent, callBack);
            } else if ($rootScope.orderType == "wastage-order") {
                createWastageOrder(order, callBack);
            } else if ($rootScope.orderType == "unsatisfied-customer-order" || $rootScope.orderType == "PPE") {
                order.linkedOrderId = $rootScope.orderIdforUnsatisfiedOrder;
                createOrder(order, unitDetails, isAndroid, customerPresent, callBack);
                $rootScope.orderIdforUnsatisfiedOrder = null;
            } else if ($rootScope.orderType == "subscription") {
                createSubscriptionEventOrder(order, unitDetails, callBack);
            }
        }

        function createSubscriptionEventOrder(reqObj, unitDetails, callBack) {
            if (service.isCOD()) {
                var requestUrl = service.restUrls.subscription.createSubscription;
                posAPI.allUrl('/', requestUrl).post(reqObj).then(function (response) {
                    if (response != undefined && response != null && callBack != undefined) {
                        if (callBack != undefined && typeof callBack == 'function') {
                            callBack();
                            $rootScope.showFullScreenLoader = false;
                        }
                    } else {
                        bootbox.alert("Order Creation Failed");
                    }
                    var dataObj = response;
                    service.mySuccessAlert("Subscription with subscription id: " + dataObj + " created successfully");
                    addUpdatelastThreeOrderArray(dataObj, unitDetails);
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
            } else {
                service.myAlert("Subscriptions can be created from call centers only!");
            }
        }

        function getAllComplimentaryCodes(list) {
            var array = [];
            for (var i in list) {
                if (list[i].id == 1 || list[i].id == 2100 || list[i].id == 2101) {
                    continue;
                }
                if ((service.isCOD() == true) && (list[i].id == 2105 || list[i].id == 2106)) {
                    continue;
                }
                array.push(list[i]);
            }
            return array;
        }

        function validateGiftCardsInOrder(reqObj, createOrderCallback, orderItemArray) {
            var gCards = [];
            var configData = service.getAutoConfigData();
            reqObj.orders.map(function (orderItem) {
                if (orderItem.code == "GIFT_CARD") {
                    gCards.push({
                        itemId: orderItem.itemId,
                        productName: orderItem.productName,
                        cardValue: orderItem.price,
                        cardNumber: orderItem.itemCode,
                        isValid: true,
                        error: "",
                        buyerId: service.customerSocket.id,
                        empId: $rootScope.globals.currentUser.userId,
                        unitId: configData.unitId,
                        terminalId: configData.selectedTerminalId,
                        type: service.cardType
                    });
                }
            });
            posAPI.allUrl('/', service.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function (response) {
                if (response.errorType != undefined && response.errorType != '') {
                    var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
                    service.myAlert(msg);
                    return false;
                }
                var dataObj = response.plain();
                if (dataObj != null) {
                    var valid = true;
                    dataObj.map(function (cardObj) {
                        if (valid && !cardObj.isValid) {
                            valid = false;
                        }
                    });
                    if (valid) {
                        if (createOrderCallback != undefined && typeof createOrderCallback == 'function') {
                            createOrderCallback();
                        }
                    } else {
                        service.mySuccessAlert("Please ask customer to fill the correct gift card codes.");
                        if (service.cardType == 'GIFT') {
                            sendGiftCardsRevalidation(dataObj, orderItemArray);
                        }
                    }
                } else {
                    var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
                    service.myAlert(msg);
                    $rootScope.showFullScreenLoader = false;
                }
            }, function (err) {
                service.myAlert(err.data.errorMessage);
            });
        }

        function sendGiftCardsRevalidation(dataObj, orderItemArray) {
            dataObj.map(function (cardObj) {
                if (!cardObj.isValid) {
                    cardObj.cardNumber = "";
                    orderItemArray.map(function (item) {
                        if (item.orderDetails.itemId == cardObj.itemId) {
                            item.orderDetails.itemCode = "";
                            item.orderDetails.isCardValid = false;
                        }
                    });
                }
            });
            socketUtils.emitMessage({
                GIFT_CARDS_REVALIDATE: dataObj
            });
        }

        function createWastageOrder(reqObj, callBack) {

            if (orderSanitized(reqObj)) {

                reqObj = JSON.parse(JSON.stringify(reqObj, function (key, value) {
                    if (key === "recipeDetails" || key === 'productDetails' || key === 'recipe') {
                        return undefined;
                    }

                    return value;
                }));
                var requestUrl = service.restUrls.order.createComplimentaryOrder;
                posAPI.allUrl('/', requestUrl).post(reqObj).then(
                    function (response) {
                        // var msg = response == undefined ? "Wastage booked successfully"
                        //     : response.plain().errorMessage;
                        //
                        // $timeout($rootScope.fetchCurrentAllSalesData, 10000); // get
                        // if (response == undefined) {
                        //     service.mySuccessAlert(msg);
                        //     service.refreshAnalyticsData(true);
                        //     if (callBack != undefined && typeof callBack == 'function') {
                        //         callBack();
                        //         $rootScope.showFullScreenLoader = false;
                        //     }
                        // } else {
                        //     bootbox.alert(msg);
                        // }
                        // service.setRedemptionProductList = null;
                        // $rootScope.showFullScreenLoader = false;
                        // if (!service.isCOD()) {
                        //     service.refreshRedeemLock();
                        // }
                        var receiptList = response.plain();
                        var msg = response != undefined ? "Wastage booked successfully"
                                : response.plain().errorMessage;
                        $timeout($rootScope.fetchCurrentAllSalesData, 10000);
                        if (receiptList.length > 0) {
                            service.mySuccessAlert(msg);
                            service.refreshAnalyticsData(true);
                            if (callBack != undefined && typeof callBack == 'function') {
                                callBack();
                                $rootScope.showFullScreenLoader = false;
                            }
                            // if (service.isAndroid) {
                            //     var list = [];
                            //     for (var i = 0; i < receiptList.length; i++) {
                            //         list.push(receiptList[i].printString);
                            //     }
                            //     Android.printKots(list);
                            // } else {
                            for (var i = 0; i < receiptList.length; i++) {
                                if (hasSeparateKotPrinting()) {
                                    PrintService.printOnKot(receiptList[i].printString, receiptList[i].printType);
                                } else {
                                    PrintService.printOnBilling(receiptList[i].printString,
                                        receiptList[i].printType);
                                }
                            }
                            // }
                            service.setRedemptionProductList = null;
                            $rootScope.showFullScreenLoader = false;
                            if (!service.isCOD()) {
                                service.refreshRedeemLock();
                            }
                        } else {
                            bootbox.alert(response.plain().errorMessage);
                            $rootScope.showFullScreenLoader = false;
                            service.myAlert("Error Printing Book Wastage KOT");
                        }
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        service.myAlert(err.data.errorMessage);
                    });
            } else {
                service.myAlert("Error in complimentary order please and re-punch the order!");
                console.log("Error in complimentary order please and re-punch the order!");
            }
        }

        function validateOrderObject(reqObj){
            var totalLoyaltyPointToBeRedeemed = 0;
            var orderItems = reqObj.orders
            for(var i =0 ;i<orderItems.length ; i++){
                var item = orderItems[i]
                if(item.hasBeenRedeemed === true ||item.hasBeenRedeemed === "true" )
                {
                    totalLoyaltyPointToBeRedeemed = totalLoyaltyPointToBeRedeemed + (item.quantity * 60);
                }
            }
            if(totalLoyaltyPointToBeRedeemed>0)
            {
                totalLoyaltyPointToBeRedeemed = -1 * totalLoyaltyPointToBeRedeemed;
            }
            reqObj.pointsRedeemed = totalLoyaltyPointToBeRedeemed;
            return reqObj;
        }

        function createOrder(reqObj, unitDetails, isAndroid, customerPresent, callBack) {
            reqObj = validateOrderObject(reqObj)
            if (orderSanitized(reqObj)) {

                //setting brandId on orderLevel::
                if(reqObj.orderType === "complimentary-order") {
                    reqObj.brandId = reqObj.orders[0].brandId;
                }

                reqObj = JSON.parse(JSON.stringify(reqObj, function (key, value) {
                    if (key === "recipeDetails" || key === 'productDetails' || key === 'recipe') {
                        return undefined;
                    }

                    return value;
                }));
                /*var requestUrl = !isAndroid ? service.restUrls.order.createOrder
                    : service.restUrls.order.androidCreateOrder;*/
                var requestUrl = service.restUrls.order.createOrder;
                console.log("Order before creating order on backend :", reqObj);
                posAPI.allUrl('/', requestUrl).post(reqObj).then(
                    function (response) {
                        $rootScope.showFullScreenLoader = false;
                        var dataObj = response.plain();
                        console.log(dataObj);
                        if (response != undefined && response != null && callBack != undefined) {

                            if($rootScope.orderType == "order" && service.unitEdcMappingDetails !=undefined && service.unitEdcMappingDetails !=null && service.unitEdcMappingDetails.length >0 && dataObj!=undefined && dataObj.errorType != undefined && service.edcOrderRetryCount <1 && service.paymentThroughEdcMode){
                                //bootbox.alert("Manually enter Last 4 digits of Transaction Id");
                                service.edcOrderRetryCount = service.edcOrderRetryCount + 1;

                               var modalInstance = $modal.open({
                                    animation: true,
                                    templateUrl: window.version + 'views/edcMerchantIdVerificationModal.html',
                                    controller: 'edcMerchantIdVerificationModalCtrl',
                                    backdrop: 'static',
                                    keyboard: false,
                                    size: 'lg',
                                });

                                modalInstance.result.then(function (result) {
                                    console.log("Printing value of result",result);

                                    if (result != undefined && result != null && result == true) {
                                        createOrder(reqObj, unitDetails, isAndroid, customerPresent, callBack);
                                    }
                                    else if (result == undefined || result == null || result == false) {
                                        service.refreshAnalyticsData(true);
                                        if (callBack != undefined && typeof callBack == 'function') {
                                            callBack();
                                            $rootScope.showFullScreenLoader = false;
                                        }
                                    }
                                    //callback(result);
                                }, function (result) {
                                    console.log("Printing value of result",result);
                                    //callback(result);
                                });
                            }
                            else{
                                service.refreshAnalyticsData(true);
                                if (callBack != undefined && typeof callBack == 'function') {
                                    callBack();
                                    $rootScope.showFullScreenLoader = false;
                                }
                            }
                        } else {
                            bootbox.alert("Order Creation Failed");
                        }
                        var dataObj = response.plain();
                        console.log(dataObj);
                        service.setCurrentTransactionGiftCards(dataObj.giftCards);
                        var msg = (dataObj.errorType == undefined) ? "Order placed successfully"
                            : "Order Creation Failed! " + dataObj.errorMessage;
                        var displayError = (dataObj.errorType == undefined) ? false : true;
                        if (displayError) {
                            $rootScope.$broadcast('updateIsGiftCardPurchasedFlag',{
                                isGiftCardOrder : false
                            })
                            service.myAlert(msg);
                            bootbox.alert(msg);

                        } else {
                            var offerMessage = "";
                            var walletBalance = {
                                walletBalance:service.totalWalletAmount
                            }
                            if (dataObj.nextOffer !== undefined && dataObj.nextOffer !== null
                                && dataObj.nextOffer.available === true && dataObj.nextOffer.text !== undefined && dataObj.nextOffer.text !== null) {
                                offerMessage += "<h2><b>"+ dataObj.nextOffer.text +"</b></h2>"
                                if(dataObj.nextOffer.channelPartner != null){
                                    offerMessage += "<h2>"+"    ( on "+dataObj.nextOffer.channelPartner+" )"+"</h2>";
                                }else{
                                    offerMessage += "<h2>"+"    ( at CHAAYOS )"+"</h2>";
                                }
                                offerMessage += "<br><br>";
                            }
                            if(dataObj.nextDeliveryOffer !== undefined && dataObj.nextDeliveryOffer !== null
                                && dataObj.nextDeliveryOffer.available === true && dataObj.nextDeliveryOffer.text !== undefined && dataObj.nextDeliveryOffer.text !== null){
                                offerMessage += "<h2><b>"+ dataObj.nextDeliveryOffer.text +"</b></h2>";
                                if(dataObj.nextDeliveryOffer.channelPartner != null){
                                    offerMessage += "<h2>"+"    ( on "+dataObj.nextDeliveryOffer.channelPartner+" )"+"</h2>";
                                }else{
                                    offerMessage += "<h2>"+"    ( at CHAAYOS )"+"</h2>";
                                }
                            }
                            if(offerMessage != ""){
                                offerMessage = "Inform customer about his/her offer" +"<br>" + offerMessage
                                var offer = null;
                                if($rootScope.isGiftCardPurchased){
                                    offer = {
                                        nextOffer:dataObj.nextOffer,
                                        nextDeliveryOffer: dataObj.nextDeliveryOffer,
                                        walletRecharge:walletBalance
                                    }
                                }else{
                                    offer = {
                                        nextOffer:dataObj.nextOffer,
                                        nextDeliveryOffer: dataObj.nextDeliveryOffer
                                    }
                                }

                                 socketUtils.emitMessage({ "CLM_OFFER" : offer});
                                 bootbox.alert({
                                     closeButton: false,
                                     message: offerMessage,
                                     callback: function (result) {
                                        socketUtils.emitMessage({"CLM_OFFER_CLOSE": null});
                                    }
                                 });
                            }else{
                                if($rootScope.isGiftCardPurchased){
                                    socketUtils.emitMessage({"WALLET_PURCHASED":walletBalance});
                                }
                            }
                            service.mySuccessAlert(msg);
                            calculateGiftCardAndSelectAmount(reqObj);
                            $rootScope.$broadcast('updateIsGiftCardPurchasedFlag',{
                                isGiftCardOrder : reqObj.isGiftOrder
                            })
                        }
                        try {
                            if (dataObj.errorType == undefined) {
                                reqObj.channelPartnerName = getOrderSourceName(reqObj.channelPartner);
                                reqObj.generatedOrderId = dataObj.generatedOrderId;
                                trackingService.trackSuccessOrder(reqObj);
                            }
                        } catch (e) {
                            console.log(e);
                        }
                        // get analytics data for this order
                        $timeout($rootScope.fetchCurrentAllSalesData, 10000);
                        if (dataObj.errorType != undefined && msg.startsWith("Employee Meal Issue")) {
                            alert(msg);
                        }

                        service.setRedemptionProductList = null;
                        addUpdatelastThreeOrderArray(dataObj, unitDetails);
                        if (reqObj.isGiftOrder || reqObj.isChaayosSelectOrder) {
                            $rootScope.$broadcast("updateGiftAmountFired", {myParam: {}});
                        }

                        try {
                            trackingService.logout();
                        } catch (e) {
                        }
                        // default object
                        if (!service.isCOD() && dataObj.receipts != null && dataObj.receipts.length > 0) {
                            // //console.log(dataObj);
                            service.refreshRedeemLock();

                            // This is for Bill printing Only
                            if (service.isTakeaway() && !customerPresent) {
                                if (isAndroid) {
                                    printAndroidReceipt(dataObj.receipts);
                                    //Android.printReceipt(JSON.stringify(dataObj.receipts));
                                } else {
                                    // printing just the bill in case of
                                    // take away
                                    PrintService.printOnBilling(dataObj.receipts[0], dataObj.printType);
                                }
                            }

                            // this is for bill print only
                            if (service.isCafe() && service.isWorkstationEnabled()) {
                                if (isAndroid) {
                                    printAndroidReceipt([dataObj.receipts[0]]);
                                    //Android.printReceipt(JSON.stringify([dataObj.receipts[0]]));
                                } else {
                                    if(reqObj.orderType === "complimentary-order") {
                                        PrintService.printOnBilling(dataObj.receipts[1], dataObj.printType);
                                    }
                                    else{
                                        PrintService.printOnBilling(dataObj.receipts[0], dataObj.printType);
                                    }
                                }
                            } else if (service.isCafe()) {
                                if (isAndroid) {
                                    printAndroidReceipt(dataObj.receipts);
                                    //Android.printReceipt(JSON.stringify(dataObj.receipts));
                                } else {
                                    printBills(dataObj);
                                }
                            }

                        }
                        if (!service.isCOD() && dataObj.additionalReceipts != null && dataObj.additionalReceipts.length > 0) {
                                if (!isAndroid) {
                                    printAdditionalReceipt(dataObj);
                                }
						}
                        if ((service.downloadConsumption && dataObj.generatedOrderId != null && dataObj.generatedOrderId != "")
                            || (unitDetails.isTestingUnit != null && unitDetails.isTestingUnit)) {
                            var walletSuggestion ={
                            totalAmountInWallet : $rootScope.totalAmountInWallet,
                            paidExtra : $rootScope.paidExtra,
                            extraAmountGained : $rootScope.extraAmountGained,
                            }
                            saveWalletSuggestions(reqObj,dataObj,walletSuggestion)
                            posAPI.allUrl('/', service.restUrls.order.calculateConsumption).post(dataObj.generatedOrderId)
                                .then(
                                    function (response) {
                                        // download file here
                                        // //console.log("Success in
                                        // consumption calulation");
                                        JSONToCSVConvertor(response, "ItemConsumption_"
                                            + dataObj.generatedOrderId, true);
                                    }, function (err) {
                                        // console.log("Error While
                                        // calulation consumption");
                                    });
                            posAPI.allUrl('/', service.restUrls.order.calculateAllConsumption).post(
                                dataObj.generatedOrderId).then(
                                function (response) {
                                    // download file here
                                    // //console.log("Success in all
                                    // consumption calulation");
                                    JSONToCSVConvertor(response, "AllItemConsumption_"
                                        + dataObj.generatedOrderId, true);
                                }, function (err) {
                                    // console.log("Error While
                                    // calulation all consumption");
                                });
                        }
                    }, function (err) {
                        $rootScope.$broadcast('updateIsGiftCardPurchasedFlag',{
                            isGiftCardOrder : false
                        })
                        service.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            } else {
                service.myAlert("Error in order please and re-punch the order!");
                console.log("Error in order please and re-punch the order!");
            }
        }

                   function saveWalletSuggestions(order,orderResponse,walletSuggestion){
                            if($rootScope.isSuggestedWallet && !$rootScope.isWithWalletPayment){
                            var reqObj ={
                              orderId : orderResponse.orderId,
                              customerId : order.customerId,
                              customerName : order.customerName,
                              billingServerTime : order.billingServerTime,
                              brandId : order.brandId,
                              partnerId : order.channelPartner,
                              suggestedWallet:{
                              totalAmountInWallet : walletSuggestion.totalAmountInWallet,
                              paidExtra : walletSuggestion.paidExtra,
                              extraAmountGained : walletSuggestion.extraAmountGained
                              }
                            }
                             var promise = posAPI.allUrl('/', service.restUrls.order.walletSuggestion)
                                                   .post(reqObj).then(function (response) {
                                                                  if(response !=undefined && response.status == 200){
                                                                                        console.log("Event saved Successfully for orderId : ",orderResponse.orderId);
                                                                                        }
                                                                                        else{
                                                                                        console.log("Unable to save event for orderId : ",orderResponse.orderId);
                                                                                        }
                                                              }, function (err) {
                                                                  // console.log("Error While
                                                                  // calulation all consumption");
                                });
        //                      var promise = posAPI.allUrl('/', service.restUrls.order.walletSuggestion)
        //                      .post(service.GetRequest(reqObj)).then(function (response) {
        //                      if(response !=undefined && response.status == 200){
        //                      console.log("Event saved Successfully for orderId : ",orderResponse.orderId);
        //                      }
        //                      else{
        //                      console.log("Unable to save event for orderId : ",orderResponse.orderId);
        //                      }
                              }
                            }


        function printAndroidReceipt(data) {

            if (parseInt(Android.getAndroidVersion()) >= 8) {
                Android.printReceipt(JSON.stringify(data));
            } else {
                Android.printReceipt(data);
            }
        }

        function setSelectedBrand(brand) {
            service.selectedBrand = brand;
        }

        function getSelectedBrand() {
            return service.selectedBrand;
        }

        function getBrandByBrandId(id) {
            var selectedBrand = null;
            service.transactionMetadata.brandList.map(function (brand) {
                if (brand.brandId == id) {
                    selectedBrand = brand;
                }
            });
            return selectedBrand;
        }

        function orderSanitized(reqObj) {
            var sum = 0;
            reqObj.settlements.map(function (settlement) {
                sum += settlement.amount;
            });
            console.log(sum, reqObj.transactionDetail.paidAmount);
            if (sum == reqObj.transactionDetail.paidAmount) {
                return true;
            } else {
                return false;
            }
        }

        function printBills(dataObj) {
            for (var printReceipt in dataObj.receipts) {

                if (dataObj.billIncludedInReceipts) {
                    if (printReceipt == 0) {
                        PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                    } else {
                        if (hasSeparateKotPrinting()) {
                            PrintService.printOnKot(dataObj.receipts[printReceipt], dataObj.printType);
                        } else {
                            PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                        }
                    }
                } else {
                    if (hasSeparateKotPrinting()) {
                        PrintService.printOnKot(dataObj.receipts[printReceipt], dataObj.printType);
                    } else {
                        PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                    }
                }
            }

        }

		function printAdditionalReceipt(dataObj) {
            for (var printReceipt in dataObj.additionalReceipts) {
                PrintService.printOnBilling(dataObj.additionalReceipts[printReceipt], dataObj.printType);
            }

        }


        function getOrderSourceName(channelPartner) {
            var name = "";
            for (var i = 0; i < service.getTransactionMetadata().channelPartner.length; i++) {
                if (service.getTransactionMetadata().channelPartner[i].id == channelPartner) {
                    name = service.getTransactionMetadata().channelPartner[i].name;
                    break;
                } else if (channelPartner == 1) {
                    name = "Chaayos (Dine In)";
                    break;
                }
            }
            return name;
        }

        function addUpdatelastThreeOrderArray(dataObj, unitDetails) {
            var lastThreeOrders = $cookieStore.get('lastThreeOrders');
            // //console.log(lastThreeOrders);
            if (typeof lastThreeOrders === 'undefined') {
                lastThreeOrders = getDefaultLastThreeOrders(dataObj, unitDetails);
            } else {
                var currentUnitId = lastThreeOrders.unitId;
                if (currentUnitId == unitDetails.id) {
                    if (lastThreeOrders.orders.length >= 3) {
                        lastThreeOrders.orders.shift();
                    }
                    lastThreeOrders.orders.push(dataObj.generatedOrderId);
                    // //console.log(lastThreeOrders);
                } else {
                    lastThreeOrders = getDefaultLastThreeOrders(dataObj, unitDetails);
                }
            }
            $cookieStore.put('lastThreeOrders', lastThreeOrders);
            $rootScope.lastThreeOrders = $cookieStore.get('lastThreeOrders').orders;
            // //console.log("after addUpdatelastThreeOrderArray:::",
            // $rootScope.lastThreeOrders);
        }

        function getDefaultLastThreeOrders(dataObj, unitDetails) {
            var lastThreeOrders = {
                unitId: unitDetails.id,
                orders: [dataObj.generatedOrderId]
            };
            // //console.log("inside default object method");
            return lastThreeOrders;
        }

        function getMassOffers() {
            posAPI.allUrl('/', service.restUrls.offers.allOffers).post(service.GetRequest("")).then(function (response) {
                if (response != undefined) {
                    service.offers = response.plain();
                    service.massOfferData = {
                        validate: false,
                        minimumAmount: 1000000,
                    }
                    if (service.offers != null && service.offers.length > 0) {
                        for (var i in service.offers) {
                            if (service.offers[i].offer.type == 'OFFER_WITH_FREE_ITEM_STRATEGY') {
                                service.massOfferData.validate = true;
                                if (service.offers[i].offer.minValue < service.massOfferData.minimumAmount) {
                                    service.massOfferData.minimumAmount = service.offers[i].offer.minValue;
                                    service.massOfferData.productId = service.offers[i].offer.offerWithFreeItem.productId;
                                    service.massOfferData.quantity = service.offers[i].offer.offerWithFreeItem.quantity;
                                }
                            }
                        }
                    }
                    // //console.log(service.offers);
                }
            }, function (err) {
                // console.log("could not fetch offers");
            });
        }

        function getCreditAccounts() {
            posAPI.allUrl('/', service.restUrls.posMetaData.creditAccounts).post(service.GetRequest("")).then(
                function (response) {
                    if (response != undefined) {
                        service.creditAccounts = response.plain();
                    }
                }, function (err) {
                    // console.log(err);
                });
        }

        function getUnitList(callback) {
            if (service.outletList == null || service.outletList.length == 0) {
                posAPI.allUrl('/', service.restUrls.unitMetaData.activeUnits).customGET("", {category: "CAFE"})
                    .then(function (response) {
                        if (service.outletList.length == 0) {
                            service.outletList = response.plain();
                        } else {
                            angular.forEach(response.plain(), function (v) {
                                service.outletList.push(v);
                            });
                        }
                        if (typeof callback == "function") {
                            callback(service.outletList);
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                    });
            } else {
                if (typeof callback == "function") {
                    callback(service.outletList);
                }
            }
        }

        function validate() {
            if (angular.isUndefined($rootScope.globals) || service.transactionMetadata == null) {
                $location.url("/login");
            }
        }

        function backToCover() {
            if (service.isCOD()) {
                $location.url('/CODCover');
            } else {
                $location.url('/cover');
            }
        };

        function getCurrentDate(format) {
            var date = new Date();
            if (format == 'yyyy-mm-dd') {
                return date.getFullYear() + "-" + ("0" + date.getMonth()).slice(-2) + "-"
                    + ("0" + date.getDate()).slice(-2);
            }
        }

        function formatDate(date, format) {
            var time = new Date(date);
            var yyyy = time.getFullYear();
            var M = time.getMonth() + 1;
            var d = time.getDate();
            var MM = M;
            var dd = d;
            var hh = time.getHours();
            var mm = time.getMinutes();
            var ss = time.getSeconds();
            if (M < 10) {
                MM = "0" + M;
            }
            if (d < 10) {
                dd = "0" + d;
            }
            if (format == "yyyy-MM-dd") {
                return yyyy + "-" + MM + "-" + dd;
            }
            if (format == "dd/MM/yyyy") {
                return dd + "/" + MM + "/" + yyyy;
            }
            if (format == "dd-MM-yyyy-hh-mm-ss") {
                return dd + "-" + MM + "-" + yyyy + "-" + hh + "-" + mm + "-" + ss;
            }
            if (format == "yyyy-MM-dd hh:mm:ss") {
                return yyyy + "-" + MM + "-" + dd + " " + hh + ":" + mm + ":" + ss;
            }
            if (format == "dd-MM-yyyy") {
                return dd + "-" + MM + "-" + yyyy;
            }
        }


        function sendTableOrderStart(customerObj) {
            $rootScope.orderStartCount = $rootScope.orderStartCount == undefined ? 0 : $rootScope.orderStartCount;
            if ($rootScope.csTimeout != null) {
                // required to close previous loop
                $interval.cancel($rootScope.csTimeout);
                $rootScope.csTimeout = null;
            }
            $rootScope.csTimeout = $interval(function () {
                if ($rootScope.csScreenTimer == undefined) {
                    $rootScope.csScreenTimer = true;
                }
                if ($rootScope.csScreenTimer || $rootScope.orderStartCount == 0) {
                    console.log("Sending table order start message");
                    try {
                        socketUtils.emitMessage({TABLE_ORDER: customerObj});
                    } catch(e) {
                        console.error("Could not emit order start ::", e);
                    }
                    $rootScope.csScreenTimer = true;
                    if ($rootScope.orderStartCount > 5) {
                        console.log("Cancelling order start interval due to five failures");
                        socketUtils.setPairingStatus(false);
                        $rootScope.csScreenTimer = false;
                        $interval.cancel($rootScope.csTimeout);
                        $rootScope.orderStartCount = 0;
                        $rootScope.$broadcast("customerScreenDown");
                    } else {
                        $rootScope.orderStartCount++;
                    }
                } else {
                    console.log("Cancelling order start interval");
                    $rootScope.csScreenTimer = false;
                    $interval.cancel($rootScope.csTimeout);
                    $rootScope.orderStartCount = 0;
                }
            }, 1000);
        }

        function sendOrderStart() {
            $rootScope.orderStartCount = $rootScope.orderStartCount == undefined ? 0 : $rootScope.orderStartCount;
            if ($rootScope.csTimeout != null) {
                // required to close previous loop
                $interval.cancel($rootScope.csTimeout);
                $rootScope.csTimeout = null;
            }
            var sessionId = new Date().getTime() + "_" + service.getSubscriber().unit + "_" + service.getSubscriber().terminal;
            $rootScope.csTimeout = $interval(function () {
                if ($rootScope.csScreenTimer == undefined) {
                    $rootScope.csScreenTimer = true;
                }
                if ($rootScope.csScreenTimer || $rootScope.orderStartCount == 0) {
                    console.log("Sending order start message", service.resetCustomerSocket());
                    var orderMessage = service.resetCustomerSocket();
                    orderMessage.sessionId = sessionId;
                    try {
                        socketUtils.emitMessage({ORDER_START: orderMessage});
                    } catch(e) {
                        console.error("Could not emit order start ::", e);
                    }
                    $rootScope.csScreenTimer = true;
                    if ($rootScope.orderStartCount > 5) {
                        //bootbox.alert("Not able to open customer screen for you. Please check if customer screen is logged in!");
                        console.log("Cancelling order start interval due to five failures");
                        socketUtils.setPairingStatus(false);
                        $rootScope.csScreenTimer = false;
                        $interval.cancel($rootScope.csTimeout);
                        $rootScope.orderStartCount = 0;
                        $rootScope.$broadcast("customerScreenDown");
                    } else {
                        $rootScope.orderStartCount++;
                    }
                } else {
                    console.log("Cancelling order start interval");
                    $rootScope.csScreenTimer = false;
                    $interval.cancel($rootScope.csTimeout);
                    $rootScope.orderStartCount = 0;
                }
            }, 1000);
        }

        function setPendingStockCalendarEvent(data) {
            service.pendingStockCalendarEvent = data;
        }

        function getPendingStockCalendarEvent() {
            return service.pendingStockCalendarEvent;
        }

        function getSwiggyMappedBrandPricingUnits() {
            var mappedUnitIds = [];
            var brandId = service.getSelectedBrand().brandId;
            service.unitPartnerBrandMappings.map(function (mapping) {
                if(mapping.status === "ACTIVE" && mapping.partnerId === 6 && mapping.brandId === brandId) {
                    mappedUnitIds.push(mapping.unitId);
                }
            });
            return mappedUnitIds;
        }

        function getZomatoMappedBrandPricingUnits() {
            var mappedUnitIds = [];
            var brandId = service.getSelectedBrand().brandId;
            service.unitPartnerBrandMappings.map(function (mapping) {
                if(mapping.status === "ACTIVE" && mapping.partnerId === 3 && mapping.brandId === brandId) {
                    mappedUnitIds.push(mapping.unitId);
                }
            });
            return mappedUnitIds;
        }

        function getAmazonMappedBrandPricingUnits() {
            var mappedUnitIds = [];
            var brandId = service.getSelectedBrand().brandId;
            service.unitPartnerBrandMappings.map(function (mapping) {
                if(mapping.status === "ACTIVE" && mapping.partnerId === 6 && mapping.brandId === brandId) {
                    mappedUnitIds.push(mapping.unitId);
                }
            });
            return mappedUnitIds;
        }

        function unitPartnerBrandMappingAvailable(partnerId) {
            var found = false;
            if(partnerId == null) {
                partnerId = service.getPartnerId();
            }
            service.unitPartnerBrandMappings.map(function (mapping) {
                if(!found && mapping.status == "ACTIVE" && mapping.unitId == service.getSelectedUnitId() &&
                    mapping.brandId == service.getSelectedBrand().brandId && mapping.partnerId == partnerId) {
                    found = true;
                }
            });
            return found;
        }

        function getPartnerNameById(partnerId) {
            var partnerName = "";
            service.getTransactionMetadata().channelPartner.map(function (partner) {
                if(partner.id === partnerId) {
                    partnerName = partner.name
                }
            });
            return partnerName;
        }

        function getPaymentModeIdsByModeType (modeType){
            if(!service.isEmptyObject(modeType) && service.isPaidEmployeeMeal()){
                if(modeType == "OTHER"){
                    service.PaymentModeMap[modeType]=[31];
                }
                else{
                    service.PaymentModeMap[modeType] = [];
                }
            }
            else if (!service.isEmptyObject(modeType)){
                if(service.unitEdcMappingDetails !=undefined  && service.unitEdcMappingDetails !=null &&  service.unitEdcMappingDetails.length > 0 && $rootScope.orderType !=undefined && $rootScope.orderType !=null && $rootScope.orderType == "order" ){
                    switch (modeType){
                        case "CASH":
                            service.PaymentModeMap[modeType]= [1];
                            break;
                        case "CARD":
                            service.PaymentModeMap[modeType]=[35,36];
                            break;
                        case "UPI":
                            service.PaymentModeMap[modeType]=[37];
                            break;
                        case "GIFT_CARD":
                            service.PaymentModeMap[modeType]=[10];
                            break;
                        case "OTHER":
                            service.PaymentModeMap[modeType]=[17,29,30,26,21,33,34];
                            break;
                        case "MANUAL":
                            service.PaymentModeMap[modeType]=[2,3,7,8,11,12,13,14,15,16,27,28,19];
                            break;
                    }
                }else{
                    switch (modeType){
                        case "CASH":
                            service.PaymentModeMap[modeType]= [1];
                            break;
                        case "CARD":
                            service.PaymentModeMap[modeType]=[2,3,7,8];
                            break;
                        case "UPI":
                            service.PaymentModeMap[modeType]=[11,12,13,14,15,16,27,28,19];
                            break;
                        case "GIFT_CARD":
                            service.PaymentModeMap[modeType]=[10];
                            break;
                        case "OTHER":
                            service.PaymentModeMap[modeType]=[17,29,30,26,21,33,34];
                            break;
                    }
                }
            }
            return service.PaymentModeMap;
        }

        function setUnitPartnerBrandMetadata(data) {
            service.unitPartnerBrandMetadata = data;
        }

        function getUnitPartnerBrandMetadata() {
            return service.unitPartnerBrandMetadata;
        }

        function getPurchasedGiftCard() {
            return service.purchasedGiftCard ;
        }

        function setPurchasedGiftCard(card) {
            service.purchasedGiftCard = card;

        }

        function getPurchasedChaayosSelect() {
            return service.purchasedChaayosSelect ;
        }

        function setPurchasedChaayosSelect(card) {
            service.purchasedChaayosSelect = card;
        }

        function getIsGiftCardModalOpen(){
            return service.isGiftCardModalOpen;
        }
        function setIsGiftCardModalOpen(gift) {
            service.isGiftCardModalOpen = gift;
        }
        function getIsChaayosSelectModalOpen(){
            return service.isChaayosSelectModalOpen;
        }
        function setIsChaayosSelectModalOpen(gift) {
            service.isChaayosSelectModalOpen = gift;
        }

        function getIsSuggestWalletPayment() {
            return service.isSuggestWalletPayment;
        }

        function setIsSuggestWalletPayment(flag) {
            service.isSuggestWalletPayment = flag;
        }

        function calculateGiftCardAndSelectAmount(reqObj){
            if(service.getIsGiftCardModalOpen()){
                if(reqObj.orders.length>0){
                    var giftCard = []
                    if(service.getPurchasedGiftCard().length>0){
                        giftCard = service.getPurchasedGiftCard();
                    }
                    for(i=0; i<reqObj.settlements.length;i++){
                        if(reqObj.settlements[i].mode!=10){
                            setSelectGiftCardAmount(false,reqObj.settlements[i].mode,reqObj.settlements[i].amount);
                        }
                    }
                    for(var i=0; i<reqObj.orders.length;i++){
                        if(reqObj.orders[i].code == "GIFT_CARD") {
                            giftCard.push(reqObj.orders[i].amount);

                        }
                    }
                    service.setPurchasedGiftCard(giftCard);
                }
                service.setIsGiftCardModalOpen(false);
            }
            if(service.getIsChaayosSelectModalOpen()){
                if(reqObj.settlements.length>0){
                    var select = []
                    if(service.getPurchasedChaayosSelect().length>0){
                        select = service.getPurchasedChaayosSelect();
                    }
                    for(i=0; i<reqObj.settlements.length;i++){
                        if(reqObj.settlements[i].mode!=10){
                            select.push(reqObj.settlements[i].amount);
                            setSelectGiftCardAmount(false,reqObj.settlements[i].mode,reqObj.settlements[i].amount);
                        }
                    }
                    service.setPurchasedChaayosSelect(select);
                }
                service.setIsChaayosSelectModalOpen(false);
            }

        }
        function calculateGiftCardAmount(){
            var arr = service.getPurchasedGiftCard()
            var res = 0;
            for(var i=0; i<arr.length;i++){
                res +=arr[i];
            }
            return res;
        }

        function calculateSelectCardAmount(){
            var arr = service.getPurchasedChaayosSelect()
            var res = 0;
            for(var i=0; i<arr.length;i++){
                res +=arr[i];
            }
            return res;
        }

        function getTotalPaybleAmount(){
            return service.totalPaybleAmount+service.calculateSelectCardAmount()+ service.calculateGiftCardAmmount();
        }
        function setTotalPaybleAmount(amount) {
            service.totalPaybleAmount = amount;
        }
        function setTotalWalletAmount(amount) {
            service.totalWalletAmount = amount;
        }

        function getTotalSettlementAmmount(flag){
            if(flag){
                return service.totalSettlementAmount+service.calculateSelectCardAmount()+ service.calculateGiftCardAmmount();
            }
            else{
                return service.totalSettlementAmount;
            }
        }
        function setTotalSettlementAmmount() {
            for(var key in service.totalSettlementAmount){
                service.totalSettlementAmount=0;
                if(key.amount!=undefined){
                    service.totalSettlementAmount+=key.amount;
                }
                if(key.remaining!=undefined){
                    service.totalSettlementAmount+=key.remaining;
                }
            }
        }

        function getPaymentSuggestWallet(){
            return service.paymentSuggestWallet;
        }
        function setPaymentSuggestWallet(flag){
            service.paymentSuggestWallet=flag;
        }
        function getSelectGiftCardAmount(){
            getTotalAmountToBePaid();
            return service.selectGiftCardAmount;
        }

        function setSelectGiftCardAmount(flag,key,amount){
            if(flag){
                service.selectGiftCardAmount={};
            }
            else{
                if(key in service.selectGiftCardAmount){
                    service.selectGiftCardAmount[key].amount += amount;
                }
                else{
                    var obj = {
                        "name":getDifferentPaymentModes(key),
                        "amount":amount,
                        "remaining":0
                    }
                    service.selectGiftCardAmount[key] = obj;
                }
            }
        }
        function getDifferentPaymentModes(key){
            var data = service.getTransactionMetadata();
            var res = "";
            for(var i=0; i<data.paymentModes.length;i++){
                if(data.paymentModes[i].id === key){
                    res = data.paymentModes[i].name;
                    break;
                }
            }
            return res;
        }

        function setRemainingSettlementAmount(flag,key,remaining){

            if(flag){
                for(key in service.selectGiftCardAmount){
                    service.selectGiftCardAmount[key].remaining=0;
                    if(service.selectGiftCardAmount[key].amount ==0){
                        delete service.selectGiftCardAmount[key];
                    }
                }
            }
            else {
                if(!(key in service.selectGiftCardAmount)){
                    setSelectGiftCardAmount(false,key,0)
                }
                for(var index in service.selectGiftCardAmount){
                    service.selectGiftCardAmount[index].remaining = 0;
                }
                service.selectGiftCardAmount[key].remaining = remaining;
            }

        }
        function getTotalAmountToBePaid(){
            service.TotalAmountToBePaid= {"totalAmount":0};
            for( var key in service.selectGiftCardAmount){
                service.TotalAmountToBePaid.totalAmount = service.TotalAmountToBePaid.totalAmount + service.selectGiftCardAmount[key].remaining + service.selectGiftCardAmount[key].amount;
            }
            return service.TotalAmountToBePaid;
        }

       function getUnitIDZoneMap(id){
            if(id in service.UnitIDZoneMap){
                return service.UnitIDZoneMap[id];
            }else{
                return null;
            }
       }
        function setUnitIDZoneMap(id,zone){
            if(!(id in service.UnitIDZoneMap) && zone!=null){
                service.UnitIDZoneMap[id] = zone;
            }
        }

        function refreshAnalyticsData(silent){
            if(!silent) {
                $rootScope.showFullScreenLoader = true;
            }
            if (!service.isCOD()) {
                $rootScope.fetchCurrentAllSalesData();
               // $rootScope.fetchAllCurrentDataAtOnce();
               // $rootScope.fetchAllTargetDataAtOnce();
            }
            refreshAnalyticsTimer();
        }

        function refreshAnalyticsTimer() {
            $rootScope.analyticsRefreshTime = new Date();
            $rootScope.showFullScreenLoader = false;
            console.log('RESET TIMER:::::::::::::', $rootScope.analyticsRefreshTime);
        }

    }


    function yesterday() {
        var currentDate = new Date();
        // //console.log(currentDate);
        currentDate = currentDate.setDate(currentDate.getDate() - 1);
        return currentDate;
    }


    function getTransactionMetadata() {
        /*
         * if(this.isEmptyObject(this.transactionMetadata)){ if (typeof(Storage)
         * !== "undefined"){ this.transactionMetadata =
         * JSON.parse(localStorage.getItem("transactionMetadata")); }else{
         * this.transactionMetadata = {}; } }
         */
        return this.transactionMetadata;
    }


    function getUnitDetails() {
        /*
         * if(this.isEmptyObject(this.unitDetails)){ if (typeof(Storage) !==
         * "undefined"){ this.unitDetails =
         * JSON.parse(localStorage.getItem("unitDetails")); }else{
         * this.unitDetails = {}; } }
         */
        return this.unitDetails;
    }

    function getUnitEdcMappingDetails(){
        return this.unitEdcMappingDetails;
    }

    function setUnitEdcMappingDetails(mappingList,terminalId){
        var list = [];
        console.log("Printiong Terminal ID::{}", terminalId);
        for(var i=0; i<mappingList.length;i++){
            if(mappingList[i].terminalId == terminalId){
                list.push(mappingList[i]);
            }
        }
        this.unitEdcMappingDetails = list;
    }

    function  getEdcOrderRetryCount(){
        return this.edcOrderRetryCount;
    }

    function  setEdcOrderRetryCount(count){
        this.edcOrderRetryCount = count;
    }

    function  getEdcOrderMerchantId(){
        return this.edcOrderMerchantId;
    }

    function setEdcOrderMerchantId(id){
        this.edcOrderMerchantId = id;
    }

    function getPaymentThroughEdcMode(){
        return this.paymentThroughEdcMode;
    }

    function  setPaymentThroughEdcMode(flag){
        this.paymentThroughEdcMode = flag;
    }

    function hasLiveInventory() {
        /*
         * if(this.isEmptyObject(this.unitDetails)){ if (typeof(Storage) !==
         * "undefined"){ this.unitDetails =
         * JSON.parse(localStorage.getItem("unitDetails")); }else{
         * this.unitDetails = {}; } }
         */
        return this.unitDetails.liveInventoryEnabled;
    }

    function hasTableService() {
        return this.unitDetails.tableService;
    }

    function hasExtendedTableService() {
        return this.unitDetails.tableService && this.unitDetails.tableServiceType > 0;
    }

    function tableServiceType() {
        return this.unitDetails.tableServiceType;
    }

    function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {

        // If JSONData is not an object then JSON.parse will parse the JSON
        // string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        var CSV = '';
        // This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";

            // This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                // Now convert each value to string and comma-seprated
                row += index + ',';
            }
            row = row.slice(0, -1);
            // append Label row with line break
            CSV += row + '\r\n';
        }

        // 1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            // 2nd loop will extract each column and convert it in string
            // comma-seprated
            for (var index in arrData[i]) {
                row += '"' + arrData[i][index] + '",';
            }
            row.slice(0, row.length - 1);
            // add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }

        // this trick will generate a temp "a" tag
        var link = document.createElement("a");
        link.id = "lnkDwnldLnk";

        // this part will append the anchor tag and remove it after automatic
        // click
        document.body.appendChild(link);

        var csv = CSV;
        var blob = new Blob([csv], {
            type: 'text/csv'
        });
        var csvUrl = window.webkitURL.createObjectURL(blob);
        var filename = ReportTitle + '.csv';
        $("#lnkDwnldLnk").attr({
            'download': filename,
            'href': csvUrl
        });

        $('#lnkDwnldLnk')[0].click();
        document.body.removeChild(link);
    }

    function covertToSentenceCase(text) {
        var result = text.replace(/([A-Z])/g, " $1");
        return result.charAt(0).toUpperCase() + result.slice(1);
    }

    function JSONToCSVConvertorLabelFromatter(JSONData, ReportTitle, ShowLabel) {
        // If JSONData is not an object then JSON.parse will parse the JSON
        // string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        var CSV = '';
        // This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";

            // This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                // Now convert each value to string and comma-seprated
                row += covertToSentenceCase(index) + ',';
            }
            row = row.slice(0, -1);
            // append Label row with line break
            CSV += row + '\r\n';
        }

        // 1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            // 2nd loop will extract each column and convert it in string
            // comma-seprated
            for (var index in arrData[i]) {
                row += '"' + arrData[i][index] + '",';
            }
            row.slice(0, row.length - 1);
            // add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }

        // this trick will generate a temp "a" tag
        var link = document.createElement("a");
        link.id = "lnkDwnldLnk";

        // this part will append the anchor tag and remove it after automatic
        // click
        document.body.appendChild(link);

        var csv = CSV;
        var blob = new Blob([csv], {
            type: 'text/csv'
        });
        var csvUrl = window.webkitURL.createObjectURL(blob);
        var filename = ReportTitle + '.csv';
        $("#lnkDwnldLnk").attr({
            'download': filename,
            'href': csvUrl
        });

        $('#lnkDwnldLnk')[0].click();
        document.body.removeChild(link);
    }

    function getAutoConfigData() {
        if (this.autoConfigData == null || this.isEmptyObject(this.autoConfigData)) {
            this.autoConfigData = JSON.parse(localStorage.getItem("autoConfigData"));
        }
        return this.autoConfigData;
    }

    function setAutoConfigData(autoConfigData) {
        this.autoConfigData = autoConfigData;
        localStorage.setItem("autoConfigData", JSON.stringify(autoConfigData));
    }

    function removeAutoConfigData() {
        this.autoConfigData = null;
        localStorage.removeItem("autoConfigData");
    }

    function getCoveredCustomerContact(contactNumber) {
        return "*******" + contactNumber.substring(7, contactNumber.length);
    }

    function getSelectedAddress() {
        var addr = null;
        for (var i = 0; i < this.CSObj.addresses.length; i++) {
            if (this.CSObj.addresses[i].isSelectedAddress) {
                addr = this.CSObj.addresses[i];
            }
        }
        return addr;
    }

    function getSelectedUnitIdName() {
        var outletObj = [{
            id: this.outlet.pri_unitId,
            name: this.outlet.pri_name
        }, {
            id: this.outlet.sec_unitId,
            name: this.outlet.sec_name
        }, {
            id: this.outlet.ter_unitId,
            name: this.outlet.ter_name
        }];
        return outletObj[this.outlet.selectedId - 1];
    }

    function getOrderModeFromSource(source) {
        var sourceModeMap = {
            COD: "DELIVERY",
            CAFE: "DINE_IN",
            TAKE_AWAY: "TAKE_AWAY"
        };
        return sourceModeMap[source];
    }

    /**
     * for sorting array of objects on any given field
     */
    function sortBy(array, key) {
        array.sort(function (a, b) {
            if (a[key] < b[key]) {
                return -1;
            } else if (a[key] > b[key]) {
                return 1;
            }
            return 0;
        });
    }

    function setCurrentTransactionGiftCards(cards) {
        this.currentTransactionGiftCards = cards;
    }

    function getCurrentTransactionGiftCards() {
        return this.currentTransactionGiftCards;
    }

    function getProductsOfCategory(type) {
        return this.productMap[type];
    }

    function getUnitsProductMap() {
        return this.unitProductMap;
    }

    function arrangeProducts(products, categoryList, productMap, unitProductMap) {
        for (var i = 0; i < categoryList.length; i++) {
            productMap[categoryList[i].detail.id] = [];
        }
        for (var i = 0; i < products.length; i++) {
            unitProductMap[products[i].id] = products[i];
            if (products[i].type != 7) {
                if (products[i].subType != 1202) {
                    if (productMap[products[i].type] != null) {
                        productMap[products[i].type].push(products[i]);
                    }
                }
            } else {
                if (products[i].subType == 701 || products[i].subType == 704) {
                    productMap[701].push(products[i]);
                } else {
                    productMap[702].push(products[i]);
                }
            }

        }
        //console.log("productMap",productMap);
    }

    function arrangeProductCategory(categories) {
        //console.log("categories",categories);
        var productCategory = [];
        var recom = {
            detail: {id: 99999, name: "RECOM", code: "Recommendation", shortCode: null, type: "CATEGORY"},
            content: [{id: 99999, name: "Recommendation"}]
        }
       productCategory.push(recom);
        for (var i = 0; i < categories.length; i++) {
            if (categories[i].detail.id != 43) {
                if (categories[i].detail.id != 7) {
                    productCategory.push(categories[i]);
                } else {
                    var food1 = {
                        detail: {id: 701, name: "MLS/SWCH", code: "Meals", shortCode: null, type: "CATEGORY"},
                        content: [{id: 701, name: "Meals"}, {id: 704, name: "Sandwich"}]
                    };
                    var food2 = {
                        detail: {id: 702, name: "NST/BRKFST", code: "Nasta", shortCode: null, type: "CATEGORY"},
                        content: [{id: 702, name: "Breakfast"}, {id: 703, name: "Nashta"}]
                    };
                    productCategory.push(food1);
                    productCategory.push(food2);
                }
            }
        }
        this.categoryList = productCategory;
    }

    function generateRandomNumber(minRange,maxRange){
        console.log(minRange+" max -  "+maxRange)
        var randomNumber = Math.ceil(Math.random() * (maxRange - minRange) + minRange);
        console.log(randomNumber)
        return randomNumber;
    }

    function getAllCafeAppProperties() {
        return this.allCafeAppProperties;
    }

    function setAllCafeAppProperties(cafeAppProperties) {
        this.allCafeAppProperties = cafeAppProperties;
    }
})();
