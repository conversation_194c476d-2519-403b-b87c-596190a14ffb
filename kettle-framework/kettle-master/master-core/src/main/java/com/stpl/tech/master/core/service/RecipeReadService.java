package com.stpl.tech.master.core.service;

import java.util.List;

import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import org.springframework.stereotype.Service;

import com.stpl.tech.master.recipe.model.RecipeDetail;

@Service
public interface RecipeReadService {

    public List<RecipeDetail> findByStatus(String status);
    public List<RecipeDetail> findById(Integer id);

    List<RecipeDetail> findAllRecipe();

    RecipeIterationDetail getRecipeIterationDetailByProductIdAndProfile(Integer linkedProductId, String profile);

}
