
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.master.core.service.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.hazelcast.map.IMap;
import com.hazelcast.multimap.MultiMap;
import com.stpl.tech.master.budget.metadata.model.ExpenseMetadata;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.config.attribute.service.ConfigAttributeService;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.tokenizer.ApiTokenizerService;
import com.stpl.tech.master.core.external.cache.CacheProxy;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.cache.ListTypes;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.MasterDataCacheProxy;
import com.stpl.tech.master.core.external.cache.PreAuthenticatedApiCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.interceptor.ExternalAPITokenCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.external.partner.service.ChannelPartnerExternalService;
import com.stpl.tech.master.core.external.partner.service.ExternalAPIService;
import com.stpl.tech.master.core.external.partner.service.impl.ExternalAPIToken;
import com.stpl.tech.master.core.mapper.DtoDataMapper;
import com.stpl.tech.master.core.service.BrandManagementService;
import com.stpl.tech.master.core.service.EntityAliasManagementService;
import com.stpl.tech.master.core.service.MasterDataCacheService;
import com.stpl.tech.master.core.service.MasterMetadataService;
import com.stpl.tech.master.core.service.ProductManagementService;
import com.stpl.tech.master.core.service.RecipeReadService;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.dao.BrandManagementDao;
import com.stpl.tech.master.data.dao.CompanyBrandMappingDao;
import com.stpl.tech.master.data.dao.LocalityMappingDao;
import com.stpl.tech.master.data.dao.MonkRecipeVersioningDao;
import com.stpl.tech.master.data.dao.PriceProfileDao;
import com.stpl.tech.master.data.dao.PriceProfileProductMappingsDao;
import com.stpl.tech.master.data.dao.PriceProfileVersionDao;
import com.stpl.tech.master.data.dao.UnitBrandMappingDao;
import com.stpl.tech.master.data.dao.UnitClosureStateRepository;
import com.stpl.tech.master.data.dao.UnitDetailRepository;
import com.stpl.tech.master.data.model.CacheReferenceMetadata;
import com.stpl.tech.master.data.model.CompanyBrandMapping;
import com.stpl.tech.master.data.model.DispenserCanisterItemData;
import com.stpl.tech.master.data.model.DispenserPattiSugarShotInfoData;
import com.stpl.tech.master.data.model.EntityAliasMappingData;
import com.stpl.tech.master.data.model.ExternalPartnerDetail;
import com.stpl.tech.master.data.model.ExternalPartnerInfo;
import com.stpl.tech.master.data.model.HodDetail;
import com.stpl.tech.master.data.model.PaymentModeAttributes;
import com.stpl.tech.master.data.model.PriceProfileData;
import com.stpl.tech.master.data.model.PriceProfileProductMapping;
import com.stpl.tech.master.data.model.ProductCondimentGroup;
import com.stpl.tech.master.data.model.ProductCondimentItem;
import com.stpl.tech.master.data.model.RegionMap;
import com.stpl.tech.master.data.model.UnitBrandMapping;
import com.stpl.tech.master.data.model.UnitChannelPartnerMappingData;
import com.stpl.tech.master.data.model.UnitChannelPartnerMenuMappingData;
import com.stpl.tech.master.data.model.UnitClosureState;
import com.stpl.tech.master.data.model.UnitContactDetailsData;
import com.stpl.tech.master.data.model.UnitDroolVersionMapping;
import com.stpl.tech.master.data.model.UnitIpAddressData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadata;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingMetadataType;
import com.stpl.tech.master.data.model.UnitProductPriceCategoryDomain;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.data.repository.UnitDroolVersionMappingDao;
import com.stpl.tech.master.domain.model.AddonData;
import com.stpl.tech.master.domain.model.AddonList;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CancellationReason;
import com.stpl.tech.master.domain.model.CashMetadata;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.CityLocalityKey;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.CondimentGroupData;
import com.stpl.tech.master.domain.model.CondimentItemData;
import com.stpl.tech.master.domain.model.ConfigAttributeValue;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.Department;
import com.stpl.tech.master.domain.model.Designation;
import com.stpl.tech.master.domain.model.DispenserCanisterItemDataDto;
import com.stpl.tech.master.domain.model.DispenserPattiSugarShotInfoDataDto;
import com.stpl.tech.master.domain.model.Division;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.Employee;
import com.stpl.tech.master.domain.model.EntityAliasKey;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.KioskCompanyDetails;
import com.stpl.tech.master.domain.model.KioskMachine;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PattiSugarType;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.PreAuthApi;
import com.stpl.tech.master.domain.model.PriceProfileDetail;
import com.stpl.tech.master.domain.model.PriceProfileKey;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.ProductDimensionKey;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.RestaurantPartnerKey;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TokenizedApi;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitContactDetails;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.UnitPartnerMenuMapping;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.enums.ConsumableSource;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.locality.model.LocalityMapping;
import com.stpl.tech.master.readonly.domain.model.ProductVO;
import com.stpl.tech.master.recipe.calculator.model.RecipeIterationDetail;
import com.stpl.tech.master.recipe.model.CondimentsData;
import com.stpl.tech.master.recipe.model.IngredientProduct;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariant;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.monk.model.MonkRecipeVersionData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.transaction.CashMetadataType;
import com.sun.management.OperatingSystemMXBean;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.lang.management.ManagementFactory;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.stpl.tech.util.AppUtils.generateIpAddressCacheKey;

@Service
@Log4j2
public class MasterDataCacheServiceImpl implements MasterDataCacheService {

	Logger LOG = LoggerFactory.getLogger(MasterDataCacheServiceImpl.class);

	@Autowired
	private MasterMetadataService metadataDao;

	@Autowired
	private RecipeReadService recipeService;

	@Autowired
	private MonkRecipeVersioningDao monkRecipeVerisoningDao;

	@Autowired
	private MasterDataCache cache;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private EnvironmentPropertiesCache environmentCache;

	@Autowired
	private ExternalAPIService externalAPIService;

	@Autowired
	private ExternalAPITokenCache externalAPICache;

	@Autowired
	private TokenService<ExternalAPIToken> tokenService;

	@Autowired
	private PreAuthenticatedApiCache preAuthenticatedApiCache;

	@Autowired
	private LocalityMappingDao localityDao;

	@Autowired
	private ChannelPartnerExternalService channelPartnerDao;

	@Autowired
	private ApiTokenizerService apiTokenizerService;

	@Autowired
	private ConfigAttributeService attributeService;

	@Autowired
	private MasterProperties props;

	@Autowired
	private BrandManagementService brandManagementService;

	@Autowired
	private MasterMetadataService masterMetadataService;

	@Autowired
	private EntityAliasManagementService entityAliasManagementService;

	@Autowired
	private InventoryService inventoryService;

	@Autowired
	private OfferManagementService offerManagementService;

	@Autowired
	private UnitClosureStateRepository unitClosureStateRepository;

	@Autowired
	@Lazy
	private ProductManagementService productManagementService;

	@Autowired
	private UnitDroolVersionMappingDao droolVersionMappingDao;


	@Autowired
	private PriceProfileDao priceProfileDao;

	@Autowired
	private PriceProfileVersionDao priceProfileVersionDao;

	@Autowired
	private PriceProfileProductMappingsDao priceProfileProductMappingsDao;

	@Autowired
	private CompanyBrandMappingDao companyBrandMappingDao;

	@Autowired
	private UnitDetailRepository unitDetailDao;

	@Autowired
	private BrandManagementDao brandManagementDao;

	@Autowired
	private UnitBrandMappingDao unitBrandMappingDao;

    public MasterDataCacheServiceImpl() {
    }

	@Override
	public void loadCache() throws DataNotFoundException {
    	LOG.info("POST-CONSTRUCT MasterDataCacheServiceImpl - STARTED");
		loadCache(false);
	}

	@Override
	public void loadCache(boolean flushInventory) throws DataNotFoundException {
        Stopwatch watch = Stopwatch.createUnstarted();
		//cache.clearCache();
		cache.createCache();
		watch.start();
		refreshCompanies();
		System.out.println("########## , refreshCompanies," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		loadEmployeeMealDimensions();
		System.out.println("########## , loadEmployeeMealDimensions," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshRecipeCache();
		System.out.println("########## , refreshRecipeCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshPaymentCache();
		System.out.println("########## , refreshPaymentCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshProductCache();
		System.out.println("########## , refreshProductCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshListData();
		System.out.println("########## , refreshListData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshBrandMetaData();
		System.out.println("########## , refreshBrandMetaData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshExpenseMetaDataCache();
		System.out.println("########## , refreshExpenseMetaData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshAddonData();
		System.out.println("########## , refreshAddonData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshDepartments();
		System.out.println("########## , refreshDepartments," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshDesignations();
		System.out.println("########## , refreshDesignations," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
//		watch.start();
//		refreshAddressInfo();
//		System.out.println("########## , refreshAddressInfo," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshEmployees();
		System.out.println("########## , refreshEmployees," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshHods();
		System.out.println("########## , refreshHods," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshAllPriceProfileToProductCache();
		System.out.println("########## , refreshPriceProfileToProductCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshUnits(flushInventory);
		System.out.println("########## , refreshUnits," + watch.stop().elapsed(TimeUnit.MILLISECONDS)
				+ " number of units loaded " + cache.getAllUnits().size());
		watch.start();
		addSubscriptionProduct(cache.getAllProducts());
		System.out.println("########## , refresh Subscription Products," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		updatePartnerListData(ListTypes.CHANNEL_PARTNERS);
        refreshChannelPartnerCache();
        System.out.println("########## , refreshChannelPartner," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        loadUnitChannelPartnerMapping();
        System.out.println(
                "########## , refreshUnitChannelPartnerMapping," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        refreshItemPerTicket();
        System.out.println("########## , refreshItemPerTicket," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        refreshUnitPartnerBrandMapping();
        System.out
                .println("########## , refreshUnitPartnerBrandMapping," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        refreshUnitPartnerBrandMetadata();
        System.out
                .println("########## , refreshUnitPartnerBrandMetaData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();
        refreshEntityAliasMappingData();
        System.out.println("########## , refreshEntityAliasMappingData," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        watch.start();

		loadUnitPartnerMenuMapping();
		System.out.println("########## , refreshUnitPartnerMenuMapping," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshDivisions();
		System.out.println("########## , refreshDivisions," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshTaxProfiles();
		System.out.println("########## , refreshTaxProfiles," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshDenominations();
		System.out.println("########## , refreshDenominations," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshPreAuthenticatedAPIs();
		System.out.println("########## , refreshPreAuthenticatedAPIs," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshKioskCompanies();
		System.out.println("########## , refreshKioskCompanies," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshKioskMachines();
		System.out.println("########## , refreshKioskMachines," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshExternalAPICacheCache();
		System.out.println("########## , refreshExternalAPICacheCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshExternalPartnerDetailCache();
		System.out.println(
				"########## , refreshExternalPartnerDetailCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshLocalities();
		System.out.println("########## , refreshLocalitiesCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshLocalityMappings();
		System.out.println("########## , refreshLocalityMappingsCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshLocations();
		System.out.println("########## , refreshLocationsCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshStates();
		System.out.println("########## , refreshStatesCache," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshCancellationReasons();
		System.out.println("########## , refreshCancellationReasons," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshTokenizedApis();
		System.out.println("########## , refreshTokenizedApis," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshEnvironmentPropsCache();
		System.out.println("########## , Environment Type ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		// initializing authorized mac addresses cache
		// TODO remove from here
		// AuthorizedMacCache.getInstance().setMacAddressCache(userDao.getAuthorizedMacs());
		// AppUtils.printHeapSize();
		watch.start();
		loadChaayosCashConfig();
		System.out.println("########## , ChaayosCash Config," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshRegionsMapData();
		System.out.println("########## , RegionMap ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshCacheReferenceValue();
		System.out.println("########## , Cache Reference Type ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshUnitToPartnerEdcMapping();
		System.out.println("########## , Unit To Partner EDC Mapping ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshSourceCondimentMapping();
		System.out.println("########## , Source Condiment Mapping ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshGroupCondimentMapping();
		System.out.println("########## , Group Condiment Mapping ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshUnitTerminalDataMap();
		System.out.println("########## , Unit Terminal App Name Ip Address Data Mapping ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshUnitClosureStateMetadata();
		System.out.println("########## , Unit Closure State Metadata ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		clearCustomerAppliedCouponCache();
		System.out.println("########## , Cleared Customer Applied Coupon Cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshUnitMonkRecipeProfileVersion();
		System.out.println("########## , Refreshed Unit Monk Recipe Profile Cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		refreshWorkStationsStationCategories();
		watch.start();
		refreshPriceCategoryWiseProductsPrice();
		System.out.println("########## , Refreshed Price Category Wise Products Price cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshSpecialMilkVariantMap();
		System.out.println("########## , Refreshed Special Milk Variant  cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
		watch.start();
		refreshDispenserCanisterItemDataMap();
		System.out.println("########## , Refreshed Dispenser Canister Item Data cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
//        watch.start();
//        addPriceProfileMap();
//        System.out.println("########## , Price Profile Mapping," + watch.stop().elapsed(TimeUnit.MILLISECONDS));

		watch.start();
		refreshCompanyBrandsMap();
		System.out.println("########## , Refreshed Company Brands Mapping Cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));

		watch.start();
		refreshUnitBrandMaps();
		System.out.println("########## , Refreshed Unit Brand Mappings Cache ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));

	}


	public Map<Integer, Pair<String, String>> getUnitMonkRecipeProfileVersion() {
		Map<Integer, Pair<String, String>> result = new HashMap<>();
		List<MonkRecipeVersionData> activatedRecipes = monkRecipeVerisoningDao.findByStatus(AppConstants.ACTIVATED);
		for (Unit unit : cache.getUnits().values()) {
			if (unit.getRegion() != null) {
				Optional<MonkRecipeVersionData> monkRecipeVersion = activatedRecipes.stream().filter(e -> e.getRegion().equals(unit.getRegion())).findFirst();
				if (!StringUtils.isEmpty(unit.getMonkRecipeProfile())) {
					Optional<MonkRecipeVersionData> monkRecipeVersionBasedOnProfile = activatedRecipes.stream().filter(e -> e.getRegion().equals(unit.getMonkRecipeProfile())).findFirst();
					if (monkRecipeVersionBasedOnProfile.isPresent()) {
						monkRecipeVersion = monkRecipeVersionBasedOnProfile;
					}
				}
				if (monkRecipeVersion.isPresent()) {
					result.put(unit.getId(), new Pair<>(monkRecipeVersion.get().getRegion(), monkRecipeVersion.get().getVersion()));
				} else {
					if (!activatedRecipes.isEmpty()) {
						result.put(unit.getId(), new Pair<>(activatedRecipes.get(0).getRegion(), activatedRecipes.get(0).getVersion()));
					} else {
						LOG.info("No Activated Recipes Found Not Adding for unit : {} : {} Inside Region ", unit.getName(), unit.getId());
					}
				}
			} else {
				if (!activatedRecipes.isEmpty()) {
					result.put(unit.getId(), new Pair<>(activatedRecipes.get(0).getRegion(), activatedRecipes.get(0).getVersion()));
				} else {
					LOG.info("No Activated Recipes Found Not Adding for unit : {} : {}", unit.getName(), unit.getId());
				}
			}
		}
		LOG.info("Total Units list is : {} and Monk Recipe List is : {}", cache.getAllUnits().size(), result.size());
		return result;
	}

	@Override
	public void refreshUnitMonkRecipeProfileVersion() {
		try {
			recipeCache.clearUnitMonkRecipeProfileVersion();
			recipeCache.getUnitMonkRecipeProfileVersion().putAll(getUnitMonkRecipeProfileVersion());
		} catch (Exception e) {
			LOG.error("Error while refreshUnitMonkRecipeProfileVersion :: ", e);
		}
	}

	@Override
	public void refreshWorkStationsStationCategories() {
		try {
			cache.clearWorkStationsStationCategories();
			cache.getWorkStationsStationCategories().addAll(getWorkStationsStationCategories());
		} catch (Exception e) {
			LOG.error("Error while refreshWorkStationsStationCategories :: ", e);
		}
	}

	private Collection<String> getWorkStationsStationCategories() throws DataNotFoundException {
		List<ListData> allStations = metadataDao.getAllListData(AppConstants.RTL_GROUP_STATION_CATEGORIES, true);
		if (Objects.nonNull(allStations)) {
			return allStations.stream()
					.flatMap(listData -> listData.getContent().stream())
					.map(IdCodeName::getName)
					.collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	@Override
	public void refreshUnitClosureStateMetadata() {
		try {
			cache.clearUnitStateMetadata();
			List<UnitClosureState> unitClosureStates = unitClosureStateRepository.findByStateStatusOrderByStateIdAsc(AppConstants.ACTIVE);
			for (UnitClosureState unitClosureState : unitClosureStates) {
				cache.getUnitClosureStateMetadata().put(unitClosureState.getStateId(), unitClosureState);
			}
		} catch (Exception e) {
			LOG.error("Error while create Unit Closure State Metadata :: ", e);
		}
	}

	@Override
	public  void refreshSourceCondimentMapping() throws DataNotFoundException{
		cache.clearSourceCondimentData();
//		List<ProductCondimentItem>  condimentItemList = metadataDao.getAllProductCondimentItem(AppConstants.ACTIVE);
//		Map<Integer,List<CondimentItemData>> groupCondimentMap = new HashMap<>();
//		if(Objects.nonNull(condimentItemList) && condimentItemList.size() >0 ){
//			for(ProductCondimentItem condimentItem: condimentItemList){
//				if(Objects.nonNull(condimentItem.getGroupId()) &&
//						!groupCondimentMap.containsKey(condimentItem.getGroupId())){
//					List<CondimentItemData> itemData = new ArrayList<>();
//					groupCondimentMap.put(condimentItem.getGroupId(),itemData);
//				}
//				CondimentItemData itemData = CondimentItemData.builder().itemId(condimentItem.getId()).name(condimentItem.getName()).quantity(condimentItem.getQuantity()).build();
//				groupCondimentMap.get(condimentItem.getGroupId()).add(itemData);
//			}
//		}

		List<ProductCondimentGroup>  condimentGroups = metadataDao.getAllProductCondimentGroup(AppConstants.ACTIVE);

		if(Objects.nonNull(condimentGroups) && condimentGroups.size() >0 ){
			for(ProductCondimentGroup condimentGroup: condimentGroups){
//				if(Objects.nonNull(condimentGroup.getSource()) && !cache.getSourceCondimentDataMapping().containsKey(condimentGroup.getSource())){
//					Map<Integer, List<CondimentItemData>>  condimentItemDataMap = new HashMap<>();
//					cache.getSourceCondimentDataMapping().put(condimentGroup.getSource(),condimentItemDataMap);
//				}
//				if(Objects.nonNull(condimentGroup.getSource()) && Objects.nonNull(condimentGroup.getGroupId()) && cache.getSourceCondimentDataMapping().containsKey(condimentGroup.getSource()) &&
//						!cache.getSourceCondimentDataMapping().get(condimentGroup.getSource()).containsKey(condimentGroup.getGroupId()) && groupCondimentMap.containsKey(condimentGroup.getGroupId()) ){
//
//					Map<Integer, List<CondimentItemData>> srcToProductMap = cache.getSourceCondimentDataMapping().get(condimentGroup.getSource());
//					List<CondimentItemData> condimentItemDataList = groupCondimentMap.get(condimentGroup.getGroupId());
//					srcToProductMap.put(condimentGroup.getGroupId(),condimentItemDataList);
//					cache.getSourceCondimentDataMapping().put(condimentGroup.getSource(),srcToProductMap);
//				}
				if(Objects.nonNull(condimentGroup.getSource()) && !cache.getSourceCondimentDataMapping().containsKey(condimentGroup.getSource())){
					 List<CondimentGroupData>  condimentGroupList = new ArrayList<>();
					cache.getSourceCondimentDataMapping().put(condimentGroup.getSource(),condimentGroupList);
				}
				CondimentGroupData data = CondimentGroupData.builder().groupId(condimentGroup.getGroupId()).groupName(condimentGroup.getName()).source(condimentGroup.getSource()).build();
					List<CondimentGroupData>  condimentGroupList = cache.getSourceCondimentDataMapping().get(condimentGroup.getSource());
					condimentGroupList.add(data);
					cache.getSourceCondimentDataMapping().put(condimentGroup.getSource(),condimentGroupList);

			}
		}
	}

	@Override
	public void refreshGroupCondimentMapping() throws DataNotFoundException{
		cache.clearGroupCondimentMapping();
		List<ProductCondimentItem>  condimentItemList = metadataDao.getAllProductCondimentItem(AppConstants.ACTIVE);
		if(Objects.nonNull(condimentItemList) && condimentItemList.size() >0 ){
			for(ProductCondimentItem condimentItem: condimentItemList){
				if(Objects.nonNull(condimentItem.getGroupId()) &&
						!cache.getGroupItemCondimentMapping().containsKey(condimentItem.getGroupId())){
					List<CondimentItemData> itemData = new ArrayList<>();
					cache.getGroupItemCondimentMapping().put(condimentItem.getGroupId(),itemData);
				}
				CondimentItemData itemData = CondimentItemData.builder().itemId(condimentItem.getId()).name(condimentItem.getName()).quantity(condimentItem.getQuantity()).build();
				List<CondimentItemData> condimentItemDataList = cache.getGroupItemCondimentMapping().get(condimentItem.getGroupId());
				condimentItemDataList.add(itemData);
				cache.getGroupItemCondimentMapping().put(condimentItem.getGroupId(),condimentItemDataList);
			}
		}
	}

	@Override
	public Map<String,List<CondimentGroupData> > getSrcToCondimentMap(){
		return cache.getSourceCondimentDataMapping();
	}

	@Override
	public void refreshUnitToPartnerEdcMapping() {
		cache.clearUnitToPartnerEdcMappings();
		for (UnitToPartnerEdcMapping detail : metadataDao.getUnitPartnerEdcMappingMetadata(AppConstants.ACTIVE)) {
			if(!cache.getUnitPartnerEdcMappingMetadata().containsKey(detail.getUnitId())){
				List<UnitToPartnerEdcMapping> mappingList = new ArrayList<>();
				cache.getUnitPartnerEdcMappingMetadata().put(detail.getUnitId(),mappingList);
			}
			List<UnitToPartnerEdcMapping> unitToPartnerEdcMappingList = cache.getUnitPartnerEdcMappingMetadata().get(detail.getUnitId());
			unitToPartnerEdcMappingList.add(detail);
			cache.getUnitPartnerEdcMappingMetadata().put(detail.getUnitId(),unitToPartnerEdcMappingList);
		}

	}

	@Override
	public void refreshAddressInfo() throws DataNotFoundException {
		cache.clearAddressInfo();
		for(Address address : metadataDao.getAllAddress()){
			cache.getAddresses().put(address.getId(),address);
		}
	}


	@Override
	public void refreshCacheReferenceValue() {
		cache.clearCacheReferenceValue();
		for (CacheReferenceMetadata detail : metadataDao.getCacheReferenceMetadata(AppConstants.ACTIVE)) {
			cache.getCacheReferenceMetadata().put(CacheReferenceType.valueOf(detail.getReferenceType()), detail.getReferenceValue());
		}
	}

    @Override
    public void refreshExternalPartnerDetailCache() {
		cache.getExternalPartnerMap().clear();
        for (ExternalPartnerDetail detail : metadataDao.getExternalPartnerDetail("ACTIVE")) {
            cache.getExternalPartnerMap().put(detail.getPartnerCode(), detail);
        }
    }

	@Override
	public void loadUnitChannelPartnerMapping() {
		List<UnitChannelPartnerMappingData> mappings = channelPartnerDao.getUnitChannelPartnerMappings();
		cache.loadUnitChannelPartnerMapping(mappings);
	}

	@Override
    public void loadUnitChannelPartnerMapping(UnitChannelPartnerMappingData mapping) {
        cache.loadUnitChannelPartnerMapping(mapping);
    }

	@Override
	public void loadEmployeeMealDimensions() {
		cache.clearEmployeeMealDimensions();
		cache.getEmployeeMealDimensions().add("None");
		cache.getEmployeeMealDimensions().add("Regular");
		cache.getEmployeeMealDimensions().add("Single");
	}

	@Override
	public void refreshRecipeCache() {
		recipeCache.removeRecipeCache();
		List<RecipeDetail> cacheDetails = recipeService.findAllRecipe();
		System.out.println("Total Recipes :" + cacheDetails.size());
		int count = 0;
		for (RecipeDetail recipe : cacheDetails) {
			RecipeIterationDetail recipeIterationDetail = recipeService.getRecipeIterationDetailByProductIdAndProfile(recipe.getProduct().getProductId(), recipe.getProfile());
			if(Objects.nonNull(recipeIterationDetail)) {
				recipe.setProductUom(recipeIterationDetail.getProductUom());
				recipe.setOutputUom(recipeIterationDetail.getOutputUom());
				recipe.setOutputConversion(recipeIterationDetail.getOutputConversion());
				recipe.setProductConversion(recipeIterationDetail.getProductConversion());
			}
			if (Objects.nonNull(recipe.getStatus()) && recipe.getStatus().equals(AppConstants.ACTIVE)) {
				count++;
				System.out.println("Recipe Id : " + recipe.getRecipeId());
				System.out.println("Count : " + count);
				AppUtils.printHeapSize();
				if (Objects.nonNull(recipe.getRecipeId()) && Objects.nonNull(recipe.getCondiments())) {
					Map<String, CondimentsData> srcToGroupMap = new HashMap<>();
					if (Objects.nonNull(recipe.getCondiments().getDineIn()) && Objects.nonNull(recipe.getCondiments().getDineIn().getGroupId()) && Objects.nonNull(recipe.getCondiments().getDineIn().getGroupName()) && Objects.nonNull(recipe.getCondiments().getDineIn().getQuantity())) {
						srcToGroupMap.put(UnitCategory.CAFE.value(), recipe.getCondiments().getDineIn());
					}
					if (Objects.nonNull(recipe.getCondiments().getDelivery()) && Objects.nonNull(recipe.getCondiments().getDelivery().getGroupId()) && Objects.nonNull(recipe.getCondiments().getDelivery().getGroupName()) && Objects.nonNull(recipe.getCondiments().getDelivery().getQuantity())) {
						srcToGroupMap.put(UnitCategory.COD.value(), recipe.getCondiments().getDelivery());
					}
					if (Objects.nonNull(recipe.getCondiments().getTakeaway()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getGroupId()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getGroupName()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getQuantity())) {
						srcToGroupMap.put(UnitCategory.TAKE_AWAY.value(), recipe.getCondiments().getTakeaway());
					}
					recipeCache.getRecipeToSrcGroupCondiment().put(recipe.getRecipeId(), srcToGroupMap);
				}
				if (ProductClassification.SCM_PRODUCT.equals(recipe.getProduct().getClassification())) {
//                recipeCache.getScmRecipes().put(recipe.getProduct().getProductId(), recipe);
					ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(), null,
							recipe.getProfile());
					recipeCache.getScmRecipes().put(key, recipe);
				} else {
					ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(),
							recipe.getDimension().getCode(), recipe.getProfile());
					addCriticalProducts(key, recipeCache.getCriticalProducts(), recipe);
					recipeCache.getRecipes().put(key, recipe);
				}
			}
			recipeCache.getRecipeMap().put(recipe.getRecipeId(), recipe);
		}
		cache.addCriticalSCMProducts(recipeCache.getCriticalProducts().values());
	}
	@Override
	public boolean addRecipeIntoCache(Integer id) {
		List<RecipeDetail> recipeList = recipeService.findById(id);
		if (Objects.nonNull(recipeList) && !recipeList.isEmpty()) {
			try {
				RecipeDetail recipe = recipeList.get(0);
				RecipeIterationDetail recipeIterationDetail = recipeService.getRecipeIterationDetailByProductIdAndProfile(recipe.getProduct().getProductId(), recipe.getProfile());
				if(Objects.isNull(recipeIterationDetail)){
					recipe.setProductUom(recipeIterationDetail.getProductUom());
					recipe.setOutputUom(recipeIterationDetail.getOutputUom());
					recipe.setOutputConversion(recipeIterationDetail.getOutputConversion());
					recipe.setProductConversion(recipeIterationDetail.getProductConversion());
				}
				System.out.println("Recipe Id : " + recipe.getRecipeId());
				if(recipe.getStatus().equals(AppConstants.ACTIVE)) {
					if (Objects.nonNull(recipe.getRecipeId()) && Objects.nonNull(recipe.getCondiments())) {
						Map<String, CondimentsData> srcToGroupMap = new HashMap<>();
						if (Objects.nonNull(recipe.getCondiments().getDineIn()) && Objects.nonNull(recipe.getCondiments().getDineIn().getGroupId()) && Objects.nonNull(recipe.getCondiments().getDineIn().getGroupName()) && Objects.nonNull(recipe.getCondiments().getDineIn().getQuantity())) {
							srcToGroupMap.put(UnitCategory.CAFE.value(), recipe.getCondiments().getDineIn());
						}
						if (Objects.nonNull(recipe.getCondiments().getDelivery()) && Objects.nonNull(recipe.getCondiments().getDelivery().getGroupId()) && Objects.nonNull(recipe.getCondiments().getDelivery().getGroupName()) && Objects.nonNull(recipe.getCondiments().getDelivery().getQuantity())) {
							srcToGroupMap.put(UnitCategory.COD.value(), recipe.getCondiments().getDelivery());
						}
						if (Objects.nonNull(recipe.getCondiments().getTakeaway()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getGroupId()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getGroupName()) && Objects.nonNull(recipe.getCondiments().getTakeaway().getQuantity())) {
							srcToGroupMap.put(UnitCategory.TAKE_AWAY.value(), recipe.getCondiments().getTakeaway());
						}
						recipeCache.getRecipeToSrcGroupCondiment().put(recipe.getRecipeId(), srcToGroupMap);
					}
					recipeCache.getRecipeMap().put(recipe.getRecipeId(), recipe);
					if (ProductClassification.SCM_PRODUCT.equals(recipe.getProduct().getClassification())) {
						ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(), null,
								recipe.getProfile());
						recipeCache.getScmRecipes().put(key, recipe);
					} else {
						ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(),
								recipe.getDimension().getCode(), recipe.getProfile());
						recipeCache.getRecipes().put(key, recipe);
						addCriticalProducts(key, recipeCache.getCriticalProducts(), recipe);
					}
					cache.addCriticalSCMProducts(recipeCache.getCriticalProducts().values());
					return true;
				}
				else{
					LOG.info("Recipe is not active with recipeId : {}",id);
					return false;
				}
			}catch (Exception e){
				LOG.info("Error in add recipe into Cache with Exception : {}",e);
				return false;
			}
		} else {
			return false;
		}
	}




	@Override
	public void refreshPaymentCache() throws DataNotFoundException {
		cache.clearPaymentModes();
		cache.clearPaymentModesCommission();
		for (PaymentMode data : metadataDao.getAllPaymentMode(PaymentCategory.ALL)) {
			List<PaymentModeAttributes> attributes = metadataDao.getPaymentModeAttributes(data.getId());
			if (attributes != null && attributes.size() > 0) {
				for (PaymentModeAttributes attribute : attributes) {
					data.getAttributes()
							.add(new Pair<String, String>(attribute.getAttributeKey(), attribute.getAttributeValue()));
				}
			}
			cache.getPaymentModes().put(data.getId(), data);
		}
		cache.getAllPaymentModeComission().putAll(metadataDao.getAllPaymentModeCommisson());
	}

	@Override
	public void refreshDivisions() throws DataNotFoundException {
		cache.clearDivisions();
		for (Division division : metadataDao.getAllDivisions()) {
			cache.getDivisions().put(division.getId(), division);
		}
	}

	@Override
	public void refreshDepartments() throws DataNotFoundException {
		cache.clearDepartments();
		for (Department department : metadataDao.getAllDepartments()) {
			cache.getDepartments().put(department.getId(), department);
		}
	}

	@Override
	public void refreshDesignations() throws DataNotFoundException {
		cache.clearDesignations();
		for (Designation designation : metadataDao.getAllDesignations()) {
			cache.getDesignations().put(designation.getId(), designation);
		}
	}

	@Override
	public void refreshTaxProfiles() throws DataNotFoundException {
		cache.clearTaxProfiles();
		cache.getTaxProfiles().addAll(metadataDao.getAllTaxProfile());
	}

	@Override
	public void refreshDenominations() throws DataNotFoundException {
		cache.clearDenominations();
		for (DenominationDetail d : metadataDao.getAllDenominations()) {
			cache.getDenominations().put(d.getDenominationId(), d);
		}
	}

	@Override
	public void refreshEmployees() throws DataNotFoundException {
		cache.clearEmployees();
		for (Employee e : metadataDao.getAllEmployees()) {
			cache.getEmployees().put(e.getId(), e.getName());
			cache.getEmployeeBasicDetails().put(e.getId(), MasterDataConverter.convert(e));
		}
	}

	@Override
	public void refreshHods() throws DataNotFoundException {
		cache.clearHods();
		for (HodDetail hod : metadataDao.getAllHods()) {
			cache.getHodDetails().put(hod.getEmployee().getId(), hod);
		}
	}

	@Override
	public void refreshPreAuthenticatedAPIs() throws DataNotFoundException {
		List<String> apis = metadataDao.getPreAuthenticatedApis().stream()
				.filter(preAuthApi -> AppConstants.ACTIVE.equals(preAuthApi.getStatus().name())).map(PreAuthApi::getApi)
				.collect(Collectors.toList());
		preAuthenticatedApiCache.setPreAuthenticatedAPIs(apis);
	}

	@Override
	public void refreshUnitTerminalDataMap() {
		cache.clearUnitTerminalDataMap();
		try {
			List<UnitIpAddressData> unitIpAddressDataList = metadataDao.getAllUnitIpAddressData();
			if (Objects.nonNull(unitIpAddressDataList)) {
				Map<String, UnitIpAddressData> masterMap = new HashMap<>();
				unitIpAddressDataList.forEach(mapping -> {
					masterMap.put(generateIpAddressCacheKey(mapping.getUnitId(),
							mapping.getTerminalId(), mapping.getAppName()), mapping);
				});
				cache.getUnitTerminalIpAddressMap().putAll(masterMap);
			}
		} catch (Exception e) {
			LOG.error("Error while refreshing unit ip address cache", e);
		}
	}

	@Override
	public void refreshUnits(boolean flushInventory) throws DataNotFoundException {
		//cache.clearUnits();
		//cache.clearOperationalHours();
		//cache.clearUnitsMetadata();
		//cache.clearUnitsBasicDetails();
		//cache.clearUnitTaxProfiles();
		//cache.clearCityUnitMapping();
		//cache.clearUnitsEmailId();
		startUnitThreads(props.getEnvironmentType(), flushInventory);
	}

	@Override
	public boolean refreshUnit(Integer unitId, Boolean flushInventory) {
		MasterDataCacheProxy cacheProxy = new MasterDataCacheProxy();
		try {
			Unit unit = metadataDao.getUnit(unitId, true);
			processUnitData(unit, props.getEnvironmentType(), cacheProxy);
			cache.getUnits().put(unit.getId(), cacheProxy.getUnit(unit.getId()));
			cache.getUnitsBasicDetails().put(unit.getId(), cacheProxy.getUnitBasicDetail(unit.getId()));
			Collection<UnitBasicDetail> details = cache.getUnitsMetadata().get(unit.getFamily());
			for(UnitBasicDetail ud : details){
				if(unitId.equals(ud.getId())){
					cache.getUnitsMetadata().remove(unit.getFamily(),ud);
				}
			}
			cache.getUnitsMetadata().put(unit.getFamily(), cacheProxy.getUnitBasicDetail(unit.getId()));
			for (TaxProfile profile : unit.getTaxProfiles()) {
				cache.getUnitTaxProfiles().put(unit.getId(), profile);
			}
			cache.getCityUnitMapping().putAll(cacheProxy.getCityUnitMapping());
			cache.getUnitCityList().addAll(cacheProxy.getCityList());
			cache.getAllUnitsEmailId().addAll(cacheProxy.getUnitsEmailIds());
			cache.getOperationalHours().putAll(cacheProxy.getOperationalHours());
		} catch (DataNotFoundException e) {
			return false;
		}
		Boolean recipeProfileChanged = cache.getUnitPriceProfileUpdateMap(unitId);
		System.out.println("recipe profile changes :::: " + recipeProfileChanged );
		if ((flushInventory != null && flushInventory) || recipeProfileChanged) {
			notifyToInventory(unitId);
			cache.updateUnitPriceProfileUpdateMap(unitId,false);
		}
		return true;
	}

	private void logSystemHealth() {
		OperatingSystemMXBean osBean = ManagementFactory.getPlatformMXBean(OperatingSystemMXBean.class);
		// % CPU load this current JVM is taking, from 0.0-1.0
		LOG.info("CURRENT JVM LOAD ON CPU :::: {}", osBean.getProcessCpuLoad());
		// % load the overall system is at, from 0.0-1.0
		LOG.info("CURRENT OVERALL LOAD ON CPU :::: {}", osBean.getSystemCpuLoad());
		// current memory footprint is at, from 0.00 to 1.00
		AppUtils.logMemoryFootprint();
	}

	private void startUnitThreads(EnvType envType, boolean flushInventory) throws DataNotFoundException {
		if (!AppUtils.isProd(envType)) {
			logSystemHealth();
		}

		MasterDataCacheProxy cacheProxy = new MasterDataCacheProxy();

		long connectionPool = props.getUnitCacheThreadCount();
		long unitSize = metadataDao.getAllUnitCount();
		double batchSize = Math.ceil(unitSize / connectionPool);
		long unitBatches = unitSize / (long) batchSize;
		if (Math.ceil(unitSize % batchSize) > 0) {
			unitBatches += 1;
		}
		ExecutorService taskExecutor = Executors.newFixedThreadPool((int) unitBatches);
		int offSet = 0;
		for (int i = 0; i < unitBatches; i++) {
			int finalOffSet = offSet;
			/*
			 * We cannot get exception generated in thread and halt the process, so we let
			 * them complete and log exceptions if any
			 */
			taskExecutor.execute(() -> {
				try {
					List<Unit> units = metadataDao.getAllUnits(finalOffSet, (int) batchSize);
					for (Unit unit : units) {
						processUnitData(unit, envType, cacheProxy);
						Boolean recipeProfileChanged = cache.getUnitPriceProfileUpdateMap(unit.getId());
						System.out.println("recipe profile changes :::: " + recipeProfileChanged );
						if (flushInventory || recipeProfileChanged) {
							notifyToInventory(unit.getId());
							cache.updateUnitPriceProfileUpdateMap(unit.getId(),false);
						}
					}
				} catch (Exception e) {
					LOG.error("Error while loading units into cache", e);
				}
			});
			offSet += batchSize;
		}

		taskExecutor.shutdown();
		try {
			taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
		} catch (InterruptedException e) {
			LOG.error("Error in completion of unit threads", e);
		}
		if (!AppUtils.isProd(envType)) {
			logSystemHealth();
		}
		LOG.info("Unit cache proxy loaded completely now replacing instantly");
		cache.clearUnits();
		Map<Integer, Unit> units = cacheProxy.getUnits();
		cache.getUnits().putAll(units);
		cache.clearUnitsBasicDetails();
		cache.getUnitsBasicDetails().putAll(cacheProxy.getUnitsBasicDetails());
		cache.clearOperationalHours();
		cache.getOperationalHours().putAll(cacheProxy.getOperationalHours());
		cache.clearUnitsMetadata();
		cacheProxy.getUnitsMetadata().forEach((key, value) -> cache.getUnitsMetadata().putAllAsync(key, value));
		cache.clearUnitTaxProfiles();
		cacheProxy.getUnitTaxProfiles().forEach((key, value) -> cache.getUnitTaxProfiles().putAllAsync(key, value));
		cache.clearCityUnitMapping();
		cacheProxy.getCityUnitMapping().forEach((integer, idCodeNames) -> cache.updateCityUnitMapping(integer, idCodeNames));
		//cache.getCityUnitMapping().putAll(cacheProxy.getCityUnitMapping());
		cache.getUnitCityList().clear();
		cache.getUnitCityList().addAll(cacheProxy.getCityList());
		cache.clearUnitsEmailId();
		cache.getAllUnitsEmailId().addAll(cacheProxy.getUnitsEmailIds());
		refreshUnitContactDetails();
		///////////////////////////////
//		cache.getUnits().put(unit.getId(), unit);
//		cache.getUnitsMetadata().remove(unit.getFamily(), unitData);
//		cache.getUnitsMetadata().remove(unit.getFamily(), unitToRemove);
//		cache.getUnitsMetadata().put(unit.getFamily(), unitData);
//		cache.getUnitsBasicDetails().put(unitData.getId(), unitData);
//		cache.getUnitTaxProfiles().put(unit.getId(), profile);
//		cache.getUnitCityList().add(codeName);
//		cache.updateCityUnitMapping(locationId, unitSet);
//		cache.getAllUnitsEmailId().add(emailId);
//		cache.setOperationalHoursForUnit(unit.getId(), unit.getOperationalHours());
	}

	private void processUnitData(Unit unit, EnvType envType, MasterDataCacheProxy cacheProxy) throws DataNotFoundException {
		try {
			addUnit(unit, cacheProxy);
			if (unit.getLocation() != null) {
				updateCityList(unit.getLocation(), cacheProxy);
				updateUnitLocationMapping(unit, unit.getLocation().getId(), cacheProxy);
			}
			if(unit.getUnitEmail() != null){
				updateUnitEmailIdsList(unit.getUnitEmail(), cacheProxy);
			}
			operationalHours(unit, cacheProxy);
			System.out.println("########## Loaded Unit Data for Unit ########## " + unit.getName());
			if (!AppUtils.isProd(envType)) {
				logSystemHealth();
			}
//            addPriceProfileMap(unit.getId());
		} catch (DataNotFoundException e) {
			LOG.error("Error while loading unit into cache {}", unit.getName(), e);
			throw e;
		}
	}

	@Override
	public void refreshCompanies() throws DataNotFoundException {
		cache.clearCompanies();
		for (Company c : metadataDao.getAllCompanies()) {
			cache.addToCompanyMap(c);
		}
	}

	private void updateCityList(Location location, MasterDataCacheProxy cacheProxy) {
		IdCodeName codeName = new IdCodeName();
		codeName.setCode(location.getCode());
		codeName.setName(location.getName());
		codeName.setId(location.getId());
		cacheProxy.getCityList().add(codeName);
//		cache.getUnitCityList().add(codeName);
	}

	private void updateUnitLocationMapping(Unit unit, int locationId, MasterDataCacheProxy cacheProxy) {
		if (UnitCategory.CAFE.equals(unit.getFamily()) && UnitStatus.ACTIVE.equals(unit.getStatus())) {
			IdCodeName codeName = new IdCodeName();
			codeName.setCode(unit.getTin());
			codeName.setName(unit.getName());
			codeName.setId(unit.getId());
			codeName.setStatus(unit.getStatus().name());
			if (!cacheProxy.getCityUnitMapping().containsKey(locationId) || cacheProxy.getCityUnitMapping().get(locationId) == null) {
				cacheProxy.getCityUnitMapping().put(locationId, new HashSet<>());
			}
			cacheProxy.getCityUnitMapping().get(locationId).add(codeName);
//			cache.updateCityUnitMapping(locationId, unitSet);
		}
	}

	private void operationalHours(Unit unit, MasterDataCacheProxy cacheProxy) {
		cacheProxy.setOperationalHoursForUnit(unit.getId(), unit.getOperationalHours());
//		cache.setOperationalHoursForUnit(unit.getId(), unit.getOperationalHours());
	}

	@Override
	public UnitBasicDetail addUnit(Unit unit) throws DataNotFoundException {
		MasterDataCacheProxy cacheProxy = new MasterDataCacheProxy();
		UnitBasicDetail ubd = addUnit(unit, cacheProxy);
		cache.getUnits().put(unit.getId(), cacheProxy.getUnit(unit.getId()));
		cache.getUnitsBasicDetails().put(unit.getId(), cacheProxy.getUnitBasicDetail(unit.getId()));
		cache.getUnitsMetadata().put(unit.getFamily(), cacheProxy.getUnitBasicDetail(unit.getId()));
		for (TaxProfile profile : unit.getTaxProfiles()) {
			cache.getUnitTaxProfiles().put(unit.getId(), profile);
		}
		return ubd;
	}

	@Override
	public UnitBasicDetail addUnit(Unit unit, MasterDataCacheProxy cacheProxy) throws DataNotFoundException {
		LOG.info("Adding Unit Cache ::: {}", unit.getId());
		UnitBasicDetail unitData = new UnitBasicDetail();
		unitData.setId(unit.getId());
		unitData.setName(unit.getName());
		unitData.setShortName(unit.getShortName());
		unitData.setReferenceName(unit.getReferenceName());
		unitData.setCategory(unit.getFamily());
		unitData.setSubCategory(unit.getSubCategory());
		unitData.setStatus(unit.getStatus());
		unitData.setNoOfTerminal(unit.getNoOfTerminals());
		unitData.setNoOfTakeawayTerminals(unit.getNoOfTakeawayTerminals());
		if (unit.getUnitManager() != null) {
			unitData.setUnitManagerId(unit.getUnitManager().getId());
		}
		if (unit.getCafeManager() != null) {
			unitData.setCafeManagerId(unit.getCafeManager().getId());
		}
		if (unit.getCostCenterName() != null) {
			unitData.setCostCenterName(unit.getCostCenterName());
		}
		if (unit.getFssai() != null) {
			unitData.setFssai(unit.getFssai());
		}
		if (unit.getHandoverDate() != null) {
			unitData.setHandOverDate(unit.getHandoverDate());
		}
		unitData.setRegion(unit.getRegion());
		unitData.setCity(unit.getAddress().getCity());
		unitData.setLongitude(unit.getAddress().getLongitude());
		unitData.setLatitude(unit.getAddress().getLatitude());
		unitData.setContact(unit.getAddress().getContact1());
		unitData.setEmail(unit.getUnitEmail());
		unitData.setTin(unit.getTin());
		unitData.setAddress(unit.getAddress().getLine1() + " " + unit.getAddress().getCity() + " "
				+ unit.getAddress().getState() + "," + unit.getAddress().getZipCode());
		unitData.setStatus(unit.getStatus());
		unitData.setCompanyId(unit.getCompany().getId());
		if (unit.getLocation() != null) {
			Location loc = unit.getLocation();
			unitData.setLocation(new IdCodeName(loc.getId(), loc.getCode(), loc.getName()));
			unitData.setLocationCode(loc.getCode());
			unitData.setState(loc.getState().getName());
			unitData.setStateCode(loc.getState().getCode());
		}
		unitData.setPartnerPriced(unit.isPartnerPriced());
		unitData.setTokenEnabled(unit.isTokenEnabled());
		unitData.setTrueCallerEnabled(unit.getTrueCallerEnabled());

		unitData.setWorkStationEnabled(unit.isWorkstationEnabled());
		unitData.setHotAndColdMerged(unit.isHotAndColdMerged());
		unitData.setLiveInventoryEnabled(unit.isLiveInventoryEnabled());
		unitData.setIsTestingUnit(unit.getIsTestingUnit());
		//
		unitData.setServiceCharge(unit.getServiceCharge());
		unitData.setServiceChargePosEnabled(unit.getServiceChargePosEnabled());
		unitData.setServiceChargeAppEnabled(unit.getServiceChargeAppEnabled());
	    unitData.setCafeType(unit.getCafeType());
		unitData.setLive(unit.isLive());
		unitData.setPackagingType(unit.getPackagingType());
		unitData.setPackagingValue(unit.getPackagingValue());
		unitData.setCustomAddonsLimit(unit.getCustomAddonsLimit());
		if (unit.getCafeNeoStatus() != null) {
			unitData.setCafeNeoStatus(unit.getCafeNeoStatus());
		}
		if (unit.getCafeAppStatus() != null) {
			unitData.setCafeAppStatus(unit.getCafeAppStatus());
		}
		unitData.setGoogleMerchantId(unit.getGoogleMerchantId());
		if (unit.getSalesClonedFrom() != null) {
			unitData.setSalesClonedFrom(unit.getSalesClonedFrom());
		}
		if (unit.getProbableOpeningDate() != null) {
			unitData.setProbableOpeningDate(unit.getProbableOpeningDate());
		}
		if (Objects.nonNull(unit.getF9Enabled())) {
			unitData.setF9Enabled(unit.getF9Enabled());
		}
		if(Objects.nonNull(unit.getClosed())){
			unitData.setClosed(unit.getClosed());
		}
		if(Objects.nonNull(unit.isHotspotEnabled())){
			unitData.setHotSpotEnabled(unit.isHotspotEnabled());
		}
		unitData.setPosVersion(unit.getPosVersion());
		unitData.setFaDaycloseEnabled(unit.getFaDaycloseEnabled());
		if(Objects.nonNull(unit.getUnitCafeManager())){
			unitData.setUnitCafeManager(unit.getUnitCafeManager());
		}
		if(Objects.nonNull(unit.getLastHandoverDate())){
			unitData.setLastHandoverDate(unit.getLastHandoverDate());
		}
		if(Objects.nonNull(unit.getLastHandoverFrom())){
			unitData.setLastHandoverFrom(unit.getLastHandoverFrom());
		}
		if(Objects.nonNull(unit.getVarianceAcknowledgementRequired())){
			unitData.setVarianceAcknowledgementRequired(unit.getVarianceAcknowledgementRequired());
		}
		unitData.setPricingProfile(unit.getPricingProfile());
		if(Objects.nonNull(unit.getUnitZone())){
			unitData.setUnitZone(unit.getUnitZone());
		}
		if(Objects.nonNull(unit.getGoogleRatingReviewUrl())){
			unitData.setGoogleRatingReviewUrl(unit.getGoogleRatingReviewUrl());
		}
		addUnitProducts(unit.getId(), metadataDao.getUnitProducts(unit.getId(), false));
		cacheProxy.getUnits().put(unit.getId(), unit);
//		cache.getUnits().put(unit.getId(), unit);
		UnitBasicDetail unitToRemove = null;
		for (UnitBasicDetail ud : cache.getUnitsMetadata().get(unit.getFamily())) {
			if (ud.getId() == unitData.getId()) {
				unitToRemove = ud;
			}
		}
//		cache.getUnitsMetadata().remove(unit.getFamily(), unitData);
		/*if (unitToRemove != null) {
			cache.getUnitsMetadata().remove(unit.getFamily(), unitToRemove);
		}*/
		if(cacheProxy.getUnitsMetadata().get(unit.getFamily()) == null) {
			cacheProxy.getUnitsMetadata().put(unit.getFamily(), new ArrayList<>());
		}
		cacheProxy.getUnitsMetadata().get(unit.getFamily()).add(unitData);
//		cache.getUnitsMetadata().put(unit.getFamily(), unitData);
		cacheProxy.getUnitsBasicDetails().put(unitData.getId(), unitData);
//		cache.getUnitsBasicDetails().put(unitData.getId(), unitData);
		cacheProxy.getUnitTaxProfiles().put(unit.getId(), new ArrayList<>());
		for (TaxProfile profile : unit.getTaxProfiles()) {
//			cache.getUnitTaxProfiles().remove(unit.getId(), profile);
			cacheProxy.getUnitTaxProfiles().get(unit.getId()).add(profile);
//			cache.getUnitTaxProfiles().put(unit.getId(), profile);
		}
		unitData.setLoyalTeaRedemptionAllowed(unit.getLoyalTeaRedemptionAllowed());
		unitData.setNoOfMonksNeeded(unit.getNoOfMonksNeeded());
		unitData.setClosure(unit.getClosure());
		unitData.setMonkRecipeProfile(unit.getMonkRecipeProfile());
		unitData.setIsOtpViaEmail(unit.getIsOtpViaEmail());
		if(Objects.isNull(unit.getAutoEdcPayment())){
			unitData.setAutoEdcPayment(AppConstants.YES);
		}else{
			unitData.setAutoEdcPayment(unit.getAutoEdcPayment());
		}
		if(Objects.isNull(unit.getAssemblyStrictMode())){
			unitData.setAssemblyStrictMode(false);
		}
		else
		{
			unitData.setAssemblyStrictMode(unit.getAssemblyStrictMode());

		}
		unitData.setLoyalTeaBurnSwiggyAllowed(unit.getLoyalTeaBurnSwiggyAllowed());

		unitData.setShowLoyalteaScreen(unit.getShowLoyalteaScreen());

		if(Objects.isNull(unit.getAssemblyOtpMode())){
			unitData.setAssemblyOtpMode(false);
		}
		else
		{
			unitData.setAssemblyOtpMode(unit.getAssemblyOtpMode());

		}

		if(Objects.nonNull(unit.getIsSuperUEnabled())){
			unitData.setIsSuperUEnabled(unit.getIsSuperUEnabled());
		}
		if(Objects.nonNull(unit.getCustomerLogin())){
			unitData.setCustomerLogin(unit.getCustomerLogin());
		}
		if(Objects.nonNull(unit.getAutoTokenEnabled())){
			unitData.setAutoTokenEnabled(unit.getAutoTokenEnabled());
		}
		return unitData;
	}

	@Override
	public boolean addSubscriptionProduct(List<Product> allProducts) {
		try {
			cache.getSubscriptionProductDetails().clear();
			cache.getSubscriptionSkuCodeDetail().clear();
			for (Product product : allProducts) {
				if (product.getStatus().equals(ProductStatus.ACTIVE)
						&& product.getSubType() == props.getSubcriptionProductType()) {
					cache.getSubscriptionProductDetails().put(product.getId(), product);
					CouponDetail offerInfo = offerManagementService.searchCoupon(product.getSkuCode(), false);
					if (Objects.nonNull(offerInfo)) {
						cache.getSubscriptionSkuCodeDetail().put(product.getSkuCode(), new Pair(offerInfo,product));
					} else {
						cache.getSubscriptionProductDetails().remove(product.getId());
					}
				}
			}
			return true;
		} catch (Exception e) {
			LOG.error("Error Occured While Creating Subscription Product Info", e);
			return false;
		}
	}


	public void addUnitProducts(int unitId, Collection<Product> products) {
		cache.getUnitProductDetails().remove(unitId);
		cache.getUnitProductAlias().remove(unitId);
		cache.getUnitProductTrimmedDetails().remove(unitId);
		recipeCache.getAllProductProfileDetails().remove(unitId);
		Map<Integer, Map<Pair<BigDecimal,String>, String>> productAlias = new HashMap<>();
		Map<Integer, Map<String, Pair<String, Integer>>> map = new HashMap<>();
		Map<String, Boolean> pickDineInConsumablesMap = new HashMap<>();
		for (Product product : products) {
			boolean isCombo = AppUtils.isCombo(product.getTaxCode());
			if (isCombo) {
				product.setCustomize(true);
			}

			for (ProductPrice price : product.getPrices()) {
				RecipeDetail recipe = recipeCache.getRecipe(product.getId(), price.getDimension(), price.getProfile());
				if (recipe != null) {
					price.setRecipe(recipe);
					price.setCustomize(isCombo || recipe.getCustomizationCount() > 0
							|| (recipe.getOptions() != null && !recipe.getOptions().isEmpty())
							|| (recipe.getAddons() != null && !recipe.getAddons().isEmpty()));
					if (recipe.getCustomizationCount() > 0) {
						product.setCustomize(true);
					}
					addUnitProductProfileDetails(map, product.getId(), price.getDimension(),
							new Pair<>(price.getProfile(), recipe.getRecipeId()));
				} else {
					addUnitProductProfileDetails(map, product.getId(), price.getDimension(),
							new Pair<>(price.getProfile(), null));
				}
				if(price.getAliasProductName() != null && price.getAliasProductName().length() > 0) {
					setUnitProductAlias(productAlias, product, price);
					product.setProductAliasName(price.getAliasProductName());
				} else if (product.getSubType() == props.getSubcriptionProductType()){
					setUnitProductAlias(productAlias, product, price);
					product.setProductAliasName(price.getAliasProductName());
				}
				pickDineInConsumablesMap.put(AppUtils.generateUniqueKey(product.getId().toString(), price.getDimension()),  price.getPickDineInConsumables());
			}
			if(Objects.isNull(product.getProductAliasName())){
				product.setProductAliasName(product.getName());
			}
			cache.getUnitProductDetails().put(unitId, product);
			//cache.getUnitProductAlias().put(unitId, productAlias);
			cache.getUnitProductTrimmedDetails().put(unitId, new ProductVO(product));
			//recipeCache.getAllProductProfileDetails().put(unitId, map);
		}
		updateConsumableFlagMap(unitId, pickDineInConsumablesMap);
		cache.getUnitProductAlias().put(unitId, productAlias);
		recipeCache.getAllProductProfileDetails().put(unitId, map);
	}

	private void updateConsumableFlagMap(int unitId, Map<String, Boolean> pickDineInConsumablesMap) {
		if (pickDineInConsumablesMap == null || pickDineInConsumablesMap.isEmpty()) {
			return;
		}
		cache.getConsumablesUnitProductFlagMap()
				.compute(unitId, (k, v) -> {
					if (v == null) v = new HashMap<>();
					v.putAll(pickDineInConsumablesMap);
					return v;
				});
	}

	private void setUnitProductAlias(Map<Integer, Map<Pair<BigDecimal, String>, String>> productAlias, Product product, ProductPrice price) {
		if (!productAlias.containsKey(product.getId())) {
			productAlias.put(product.getId(), new HashMap<>());
		}
		productAlias.get(product.getId()).put(new Pair(price.getPrice(), price.getDimension()),
				Objects.nonNull(price.getAliasProductName()) ? price.getAliasProductName() : null);
	}

	private void addUnitProductProfileDetails(Map<Integer, Map<String, Pair<String, Integer>>> map, int productId,
			String dimension, Pair<String, Integer> profile) {
		if (!map.containsKey(productId)) {
			map.put(productId, new HashMap<>());
		}
		map.get(productId).put(dimension, profile);
	}

	@Override
	public void refreshListData() throws DataNotFoundException {
		CacheProxy cacheProxy = new CacheProxy(AppConstants.GET_LIST_PROXY);
		cache.clearListData();
		for (ListTypes type : ListTypes.values()) {
			updateListProxyData(type,cacheProxy);
		}
		cache.clearListCategoryData();
		cache.getListCategoryData().putAll(cacheProxy.getListCategoryData());
		cache.clearDimensionProfileData();
		cache.getDimensionProfileData().putAll(cacheProxy.getDimensionProfileData());
		cache.clearItemPerTicket();
		cache.getItemPerTicket().putAll(cacheProxy.getItemPerTicket());
	}

	@Override
	public void refreshItemPerTicket() throws DataNotFoundException {
		cache.clearItemPerTicket();
		updateListData(ListTypes.ITEM_PER_TICKET);
	}

	public void updateListProxyData(ListTypes type,CacheProxy cacheProxy) throws DataNotFoundException {
		switch (type) {
			case DIMENSION_CODES:
				cache.getListData().remove(ListTypes.DIMENSION_CODES);
				for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_DIMENSION, true)) {
					cacheProxy.getDimensionProfileData().put(list.getDetail().getId(), list);
					for (IdCodeName data : list.getContent()) {
						cache.getListData().put(ListTypes.DIMENSION_CODES, data);
					}
				}
				break;
			case ITEM_PER_TICKET:
				cache.getListData().remove(ListTypes.ITEM_PER_TICKET);
				for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_ITEM_PER_TICKET, true)) {
					cacheProxy.getItemPerTicket().put(list.getDetail().getCode(), list);
				}
				break;

			case DISCOUNT_CODES:
				cache.getListData().remove(ListTypes.DISCOUNT_CODES);
				for (IdCodeName data : metadataDao.getDiscountCodes(true).getContent()) {
					cache.getListData().put(ListTypes.DISCOUNT_CODES, data);
				}
				break;
			case SUB_CATEGORIES:
				for (ListData list : metadataDao.getAllCategories(true)) {
					cacheProxy.getListCategoryData().put(list.getDetail().getId(), list);
					// cache.getListData().remove(ListTypes.SUB_CATEGORIES);
					for (IdCodeName data : list.getContent()) {
						cache.getListData().put(ListTypes.SUB_CATEGORIES, data);
					}
				}
				break;
			case PR_TYPE:
				for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_PR_TYPE, true)) {
					for (IdCodeName data : list.getContent()) {
						cache.getListData().put(ListTypes.PR_TYPE, data);
					}
				}
				break;
			case ADJUSTMENT_COMMENT:
				for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_ADJUSTMENT_COMMENT, true)) {
					cache.getListAdjustmentCommentData().put(list.getDetail().getCode(), list);
					for (IdCodeName data : list.getContent()) {
						cache.getListData().put(ListTypes.ADJUSTMENT_COMMENT, data);
					}
				}
				break;
			default:
				break;
		}
	}

	@Override
	public void updateListData(ListTypes type) throws DataNotFoundException {
		switch (type) {
		case DIMENSION_CODES:
			cache.getListData().remove(ListTypes.DIMENSION_CODES);
			for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_DIMENSION, true)) {
				cache.getDimensionProfileData().put(list.getDetail().getId(), list);
				for (IdCodeName data : list.getContent()) {
					cache.getListData().put(ListTypes.DIMENSION_CODES, data);
				}
			}
			break;
		case ITEM_PER_TICKET:
			cache.getListData().remove(ListTypes.ITEM_PER_TICKET);
			for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_ITEM_PER_TICKET, true)) {
				cache.getItemPerTicket().put(list.getDetail().getCode(), list);
			}
			break;

		case DISCOUNT_CODES:
			cache.getListData().remove(ListTypes.DISCOUNT_CODES);
			for (IdCodeName data : metadataDao.getDiscountCodes(true).getContent()) {
				cache.getListData().put(ListTypes.DISCOUNT_CODES, data);
			}
			break;
		case SUB_CATEGORIES:
			for (ListData list : metadataDao.getAllCategories(true)) {
				cache.getListCategoryData().put(list.getDetail().getId(), list);
				// cache.getListData().remove(ListTypes.SUB_CATEGORIES);
				for (IdCodeName data : list.getContent()) {
					cache.getListData().put(ListTypes.SUB_CATEGORIES, data);
				}
			}
			break;
		case PR_TYPE:
			for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_PR_TYPE, true)) {
				for (IdCodeName data : list.getContent()) {
					cache.getListData().put(ListTypes.PR_TYPE, data);
				}
			}
			break;
		case ADJUSTMENT_COMMENT:
			for (ListData list : metadataDao.getAllListData(AppConstants.RTL_GROUP_ADJUSTMENT_COMMENT, true)) {
				cache.getListAdjustmentCommentData().put(list.getDetail().getCode(), list);
				for (IdCodeName data : list.getContent()) {
					cache.getListData().put(ListTypes.ADJUSTMENT_COMMENT, data);
				}
			}
			break;
		default:
			break;
		}
	}

	@Override
	public void updateListData(String type) throws DataNotFoundException {

		for (ListTypes listType : ListTypes.values()) {
			if (listType.getGroup().equals(type)) {
				updateListData(listType);
			}
		}
		if (AppConstants.RTL_GROUP_ADDONS.equals(type)) {
			refreshAddonData();
		}
	}

	@Override
	public void refreshAddonData() throws DataNotFoundException {
		cache.clearAddonData();
		cache.clearParcelCodes();
		for (AddonList list : metadataDao.getAllAddons(true)) {
			for (AddonData data : list.getContent()) {
				cache.getAddonData().put(data.getId(), data);
				if (AppConstants.PARCEL_CODE.equals(data.getCode())) {
					cache.getParcelCodes().put(list.getDetail().getCode(), data);
				}
			}
		}
	}

	@Override
	public void refreshProductCache() throws DataNotFoundException {
		cache.clearProductAddons();
		CacheProxy productCacheProxy = new CacheProxy(AppConstants.GET_PRODUCT_PROXY);
		Map<String, ListData> map = new HashMap<>();
		for (Product product : metadataDao.getAllProductsFromDao()) {
			addProductToProxy(map, product,productCacheProxy);
		}
		cache.clearProductDetails();
		cache.getProductDetails().putAll(productCacheProxy.getProductDetails());
		cache.clearProductBasicDetails();
		cache.getProductBasicDetails().putAll(productCacheProxy.getProductBasicDetails());
		cache.clearEmployeeMealProducts();
		cache.getEmployeeMealProducts().addAll(productCacheProxy.getEmployeeMealProducts());
	}

	public void addProductToProxy(Map<String, ListData> map, Product product, CacheProxy cacheProxy) throws DataNotFoundException {
		cacheProxy.getProductDetails().put(product.getId(), product);
		cacheProxy.getProductBasicDetails().put(product.getId(), MasterDataConverter.convertToProductBasicDetail(product));
		if (product.isEmployeeMealComponent()) {
			cacheProxy.getEmployeeMealProducts().add(product.getId());
		} else {
			cacheProxy.getEmployeeMealProducts().remove(product.getId());
		}
		ListData listData = null;
		if (map.containsKey(product.getAddOnProfile())) {
			listData = map.get(product.getAddOnProfile());
		} else {
			listData = metadataDao.getListData(product.getAddOnProfile(), true);
			map.put(product.getAddOnProfile(), listData);
		}
		for (IdCodeName content : listData.getContent()) {
			if (cache.getProductAddons().get(product.getId()) != null
					&& cache.getProductAddons().get(product.getId()).contains(content)) {
				//cache.getProductAddons().get(product.getId()).remove(content);
				cache.getProductAddons().remove(product.getId(), content);
			}
			cache.getProductAddons().put(product.getId(), content);
		}
	}

	@Override
	public void addProductToMap(Map<String, ListData> map, Product product) throws DataNotFoundException {
		cache.getProductDetails().put(product.getId(), product);
		cache.getProductBasicDetails().put(product.getId(), MasterDataConverter.convertToProductBasicDetail(product));
		if (product.isEmployeeMealComponent()) {
			cache.getEmployeeMealProducts().add(product.getId());
		} else {
			cache.getEmployeeMealProducts().remove(product.getId());
		}
		ListData listData = null;
		if (map.containsKey(product.getAddOnProfile())) {
			listData = map.get(product.getAddOnProfile());
		} else {
			listData = metadataDao.getListData(product.getAddOnProfile(), true);
			map.put(product.getAddOnProfile(), listData);
		}
		for (IdCodeName content : listData.getContent()) {
			if (cache.getProductAddons().get(product.getId()) != null
					&& cache.getProductAddons().get(product.getId()).contains(content)) {
				//cache.getProductAddons().get(product.getId()).remove(content);
				cache.getProductAddons().remove(product.getId(), content);
			}
			cache.getProductAddons().put(product.getId(), content);
		}
	}

	@Override
	public String addEmployee(Employee e) {
		return cache.addEmployee(e.getId(), e.getName());
	}

	@Override
	public EmployeeBasicDetail addEmployeeBasicDetail(EmployeeBasicDetail e) {
		return cache.addEmployeeDetail(e);
	}

	@Override
	public void addRecipe(RecipeDetail recipe) throws DataNotFoundException {
//        ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(), recipe.getDimension().getCode(),
//                recipe.getProfile());
		recipeCache.getRecipeMap().put(recipe.getRecipeId(), recipe);
		if (ProductClassification.SCM_PRODUCT.equals(recipe.getProduct().getClassification())) {
//            recipeCache.getScmRecipes().put(recipe.getProduct().getProductId(), recipe);
			ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(), null, recipe.getProfile());
			recipeCache.getScmRecipes().put(key, recipe);
		} else {
			ProductRecipeKey key = new ProductRecipeKey(recipe.getProduct().getProductId(),
					recipe.getDimension().getCode(), recipe.getProfile());
			recipeCache.getRecipes().put(key, recipe);
			addCriticalProducts(key, recipeCache.getCriticalProducts(), recipe);
			addMandatoryAddons(key, recipeCache.getMandatoryAddons(), recipe);
		}
		// TODO recipe specific updates here do not refresh whole units it
		// causes order
		// failures
		// refreshUnits();
	}

	@Override
	public void removeRecipeFromCache(RecipeDetail recipeDetail) {
		try {
			ProductRecipeKey key = new ProductRecipeKey(recipeDetail.getProduct().getProductId(),
					recipeDetail.getDimension().getCode(), recipeDetail.getProfile());
			recipeCache.getRecipes().remove(key);
			recipeCache.getCriticalProducts().remove(key);
			recipeCache.getMandatoryAddons().remove(key);
		} catch (Exception e) {
			LOG.error("Exception Occurred While Removing Recipe From Cache ::: ",e);
		}
	}

	private void addCriticalProducts(ProductRecipeKey key, MultiMap<ProductRecipeKey, Integer> map,
			RecipeDetail recipe) {
		if (recipe == null) {
			return;
		}
		if (recipe.getIngredient() != null) {
			if (recipe.getIngredient().getVariants() != null) {
				for (IngredientVariant variant : recipe.getIngredient().getVariants()) {
					for (IngredientVariantDetail detail : variant.getDetails()) {
						if(variant.isCritical()){
							map.put(key, detail.getProductId());
						}
						if(detail.getProductId() == AppConstants.SCM_MILK_PRODUCT_ID){
							recipe.setMilkBasedRecipe(true);
						}
					}
				}
			}

			if (recipe.getIngredient().getProducts() != null) {
				for (IngredientProduct variant : recipe.getIngredient().getProducts()) {
					for (IngredientProductDetail detail : variant.getDetails()) {
						if(variant.isCritical()){
							map.put(key, detail.getProduct().getProductId());
						}
						if(detail.getProduct().getProductId() == AppConstants.SCM_MILK_PRODUCT_ID){
							recipe.setMilkBasedRecipe(true);
						}
					}

				}
			}

			if (recipe.getIngredient().getComponents() != null) {
				for (IngredientProductDetail variant : recipe.getIngredient().getComponents()) {
					if (variant.isCritical()) {
						map.put(key, variant.getProduct().getProductId());
					}
					if(variant.getProduct().getProductId() == AppConstants.SCM_MILK_PRODUCT_ID){
						recipe.setMilkBasedRecipe(true);
					}
				}
			}

		}
	}

	private void addMandatoryAddons(ProductRecipeKey key, MultiMap<ProductRecipeKey, IngredientProductDetail> map,
			RecipeDetail recipe) {
		if (recipe == null) {
			return;
		}
		if (recipe.getMandatoryAddons() != null) {
			for (IngredientProductDetail variant : recipe.getMandatoryAddons()) {
				map.put(key, variant);

			}
		}
	}

    @Override
    public void refreshKioskMachines() throws DataNotFoundException {
		cache.getKioskMachines().clear();
        Optional<List<KioskMachine>> locationsList = Optional.ofNullable(metadataDao.getAllKioskMachines());
        if (locationsList.isPresent()) {
            cache.getKioskMachines().putAll(locationsList.get().stream()
                    .collect(Collectors.toMap(KioskMachine::getMachineId, machineDetails -> machineDetails)));
        }
    }

	@Override
	public void refreshLocalities() {
		Optional<List<LocalityMapping>> localities = Optional.ofNullable(localityDao.findAll());
		if (localities.isPresent()) {
			Map<String, LocalityMapping> localityMap = new HashMap<>();
			for (LocalityMapping locality : localities.get()) {
				localityMap.put(locality.getObjectId(), locality);
			}
			cache.clearLocalities();
			cache.getAllLocalities().putAll(localityMap);
		}
	}

	@Override
	public void refreshLocalityMappings() {
		cache.clearLocalityMappingMap();
		Optional<List<LocalityMapping>> localities = Optional.ofNullable(localityDao.findAll());
		if (localities.isPresent()) {
			Map<CityLocalityKey, LocalityMapping> localityMap = new HashMap<>();
			for (LocalityMapping locality : localities.get()) {
				localityMap.put(new CityLocalityKey(locality.getCity().trim(), locality.getLocality().trim()),
						locality);
			}
			cache.getLocalityMappingMap().putAll(localityMap);
		}
	}

	@Override
	public void refreshLocations() {
		cache.clearLocations();
		List<Location> locations = metadataDao.getAllLocations();
		Map<String, Location> localityMap = new HashMap<>();
		for (Location locality : locations) {
			localityMap.put(locality.getCode(), locality);
			if (!cache.getCountries().containsKey(locality.getCountry().getId())) {
				IdCodeName country = new IdCodeName();
				country.setCode(locality.getCountry().getCode());
				country.setId(locality.getCountry().getId());
				country.setName(locality.getCountry().getName());
				cache.getCountries().put(locality.getCountry().getId(), country);
			}
			IdCodeName state = new IdCodeName();
			state.setCode(locality.getState().getCode());
			state.setId(locality.getState().getId());
			state.setName(locality.getState().getName());
			state.setShortCode(locality.getState().getShortCode());
			if (!cache.getCountryToState().containsEntry(locality.getCountry().getId(), state)) {
				cache.getCountryToState().put(locality.getCountry().getId(), state);
			}
			Location data = new Location();
			data.setCode(locality.getCode());
			data.setId(locality.getId());
			data.setName(locality.getName());
			cache.getStateToLocation().put(state.getId(), data);
			cache.getAllLocationsData().put(locality.getId(), locality);
		}
		cache.getAllLocations().putAll(localityMap);
	}

	@Override
	public boolean refreshCancellationReasons() {
		cache.clearCancellationReason();
		List<CancellationReason> reasons = metadataDao.getAllCancellationReasons();
		for (CancellationReason reason : reasons) {
			cache.getCancellationReason().put(reason.getSource(), reason);
		}
		return true;
	}

	@Override
	public void refreshTokenizedApis() {
		cache.clearTokenizedApis();
		List<TokenizedApi> apis = apiTokenizerService.getAllTokenizedApi();
		for (TokenizedApi api : apis) {
			if (api.isStatus()) {
				cache.getTokenizedApis().put(api.getApiName(), api.getApiMethod());
			}
		}
	}

	@Override
	public void refreshEnvironmentPropsCache() {
		environmentCache.clearCache();
		environmentCache.createCache();
		List<ConfigAttributeValue> attributeValues = attributeService.getAllAttributeValues();
		List<ApplicationName> names = new ArrayList<>(EnumSet.allOf(ApplicationName.class));
		for (ApplicationName name : names) {
			if (environmentCache.getApplicationAttributeValues().get(name.name()) == null) {
				Map<String, String> map = new HashMap<>();
				environmentCache.getApplicationAttributeValues().put(name.name(), map);
			}
		}
		for (ConfigAttributeValue value : attributeValues) {
			Map<String, String> map = environmentCache.getApplicationAttributeValues().get(value.getApplicationName());
			map.put(value.getAttributeDef().getAttributeName(), value.getAttributeValue());
			environmentCache.getApplicationAttributeValues().put(value.getApplicationName(), map);

		}
		environmentCache.setInternalNos();
	}

	@Override
	public void refreshStates() {
		cache.clearStates();
		List<State> locations = metadataDao.getAllStates();
		Map<String, State> localityMap = new HashMap<>();
		for (State locality : locations) {
			localityMap.put(locality.getCode(), locality);
		}
		cache.getAllStates().putAll(localityMap);

	}

    @Override
    public void refreshKioskCompanies() throws DataNotFoundException {
		cache.getKioskCompanies().clear();
        Optional<List<KioskCompanyDetails>> allKioskCompanies = Optional.ofNullable(metadataDao.getAllKioskCompanies());
        if (allKioskCompanies.isPresent()) {
            cache.getKioskCompanies().putAll(allKioskCompanies.get().stream().collect(
                    Collectors.toMap(KioskCompanyDetails::getCompanyId, kioskCompanyDetails -> kioskCompanyDetails)));
        }
    }

	@Override
	public void refreshExternalAPICacheCache() {
		externalAPICache.clearCache();
		externalAPICache.createCache();
		for (ExternalPartnerInfo info : externalAPIService.getAllActivePartners()) {
			ExternalAPIToken token = new ExternalAPIToken();
			tokenService.parseToken(token, info.getApiKey());
			externalAPICache.getTokenMap().put(info.getApiKey(), token);
		}
		externalAPIService.addAccessAPISToToken(externalAPICache.getTokenMap());
	}

	@Override
	public Map<String, LocalityMapping> getAllLocalities() {
		return cache.getAllLocalities();
	}

	@Override
	public Set<IdCodeName> getUnitCityList() {
		return cache.getUnitCityList();
	}

	@Override
	public Set<IdCodeName> getUnitsOfLocation(int locationId) {
		Set<IdCodeName> unitCityList = cache.getCityUnitMapping(locationId);
		if(Objects.nonNull(unitCityList)){
			for(IdCodeName idCodeName: unitCityList){
				Unit unit = cache.getUnit(idCodeName.getId());
				idCodeName.setZone(unit.getUnitZone());
			}
		}
		return unitCityList;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.stpl.tech.master.core.service.MasterDataCacheService#changeUnitLiveStatus
	 * (int, boolean)
	 */
	@Override
	public void changeUnitLiveStatus(int unitId, boolean status) {
		Unit unit = cache.getUnit(unitId);
		cache.getUnit(unitId).setLive(status);
		cache.getUnitBasicDetail(unitId).setLive(status);
		cache.getUnitsMetadata().get(unit.getFamily()).forEach((item) -> {
			if (item.getId() == unitId) {
				item.setLive(status);
			}
		});
	}

	@Override
	public void loadChaayosCashConfig() {
		cache.clearChaayosCashMetadata();
		List<CashMetadata> mappings = metadataDao.getCashMetadata();
		// TODO put start date and end date checks
		mappings.stream().forEach(mapping -> {
			cache.getChaayosCashMetadata().put(CashMetadataType.valueOf(mapping.getType()), mapping);
		});
	}

	@Override
	public void loadUnitPartnerMenuMapping() {
		List<UnitChannelPartnerMenuMappingData> mappings = channelPartnerDao.getUnitPartnerMenuMappings();
		Map<Integer, PriceProfileDetail> priceProfilesData = channelPartnerDao.getAllActivePriceProfiles();
		cache.loadUnitPartnerMenuMapping(mappings, priceProfilesData);
	}

	@Override
	public void loadUnitPartnerMenuMapping(UnitPartnerMenuMapping mapping){
		List<UnitChannelPartnerMenuMappingData> mappings = channelPartnerDao.getUnitPartnerMenuMappings(mapping.getUnitChannelPartnerMappingId(),mapping.getBrand().getId());
		Map<Integer, PriceProfileDetail> priceProfilesData=new HashMap<>();
		if(mapping.getPriceProfileId()!=null){
			priceProfilesData = channelPartnerDao.getAllActivePriceProfiles(mapping.getPriceProfileId());
		}
		cache.loadSpecificUnitPartnerMenuMapping(mappings, priceProfilesData);
	}

	@Override
	public Unit getUnit(Integer unitId) {
		return cache.getUnit(unitId);
	}

	public void updatePartnerListData(String type) throws DataNotFoundException {
		updatePartnerListData(ListTypes.valueOf(type));
	}

	@Override
	public void updatePartnerListData(ListTypes type) throws DataNotFoundException {
		switch (type) {
		case CHANNEL_PARTNERS:
			Map<Integer, IdCodeName> partnerMap = new TreeMap<>();
			// cache.getListMappingData().put(ListTypes.CHANNEL_PARTNERS, new
			// TreeMap<Integer, IdCodeName>());
			for (ChannelPartnerDetail data : channelPartnerDao.getAllChannelPartner(AppUtils.getBusinessDate())) {
				// cache.getListMappingData().get(ListTypes.CHANNEL_PARTNERS).put(data.getId(),
				// data);
				partnerMap.put(data.getId(), data);
			}
			cache.getListMappingData().put(ListTypes.CHANNEL_PARTNERS, partnerMap);
			break;
		default:
			break;
		}
	}

	@Override
	public void refreshChannelPartnerCache() throws DataNotFoundException {
		Map<Integer, ChannelPartnerDetail> partnerMap = new TreeMap<>();
		Map<Integer, IdCodeName> map = new TreeMap<>();
		for (ChannelPartnerDetail data : channelPartnerDao.getAllChannelPartner(AppUtils.getBusinessDate())) {
			partnerMap.put(data.getId(), data);
			map.put(data.getId(), data);
		}
		cache.getListMappingData().put(ListTypes.CHANNEL_PARTNERS, map);
		cache.getChannelPartnerMap().putAll(partnerMap);
	}

	@Override
	public void refreshBrandMetaData() {
		cache.clearBrandMetaData();
		List<Brand> brands = brandManagementService.getBrands();
		for (Brand brand : brands) {
			cache.getBrandMetaData().put(brand.getBrandId(), brand);
		}
		for (Brand brand : cache.getBrandMetaData().values()) {
			System.out.println(brand);
		}
	}

	@Override
	public void refreshExpenseMetaDataCache() {
		cache.clearExpenseMetadataMap();
		List<ExpenseMetadata> expenseMetadataList = masterMetadataService.getAllExpenseList();
		for (ExpenseMetadata expenseMetadata : expenseMetadataList) {
			cache.getExpenseMetadataIMap().put(expenseMetadata.getId(), expenseMetadata);
		}
	}

	@Override
	public void refreshUnitPartnerBrandMapping() {
		cache.clearUnitPartnerBrandMapping();
		cache.clearUnitPartnerBrandMapping2();
		List<UnitPartnerBrandMappingData> mappingData = brandManagementService.getUnitPartnerBrandMappingList();
		for (UnitPartnerBrandMappingData data : mappingData) {
			if (data.getStatus().equalsIgnoreCase(AppConstants.ACTIVE)) {
				UnitPartnerBrandKey unitPartner = MasterDataConverter.convert(data);
				RestaurantPartnerKey restaurantPartnerKey = MasterDataConverter.convertToKey(data);
				cache.getUnitPartnerBrandMappingMetaData().put(unitPartner, data);
				cache.getUnitwisePartnerBrandMappingMetaData().put(unitPartner.getUnitId(), data);
				cache.getUnitPartnerBrandMappingMetaData2().put(restaurantPartnerKey, data);
			}
		}
	}

	@Override
	public void refreshUnitPartnerBrandMetadata() {
		List<UnitPartnerBrandMappingMetadata> unitPartnerBrandMappingMetadata = brandManagementService
				.getAllUnitPartnerBrandMappingMetadata();
		Map<UnitPartnerBrandKey, Map<UnitPartnerBrandMappingMetadataType, String>> metadata = new HashMap<>();
		for (UnitPartnerBrandMappingMetadata data : unitPartnerBrandMappingMetadata) {
			UnitPartnerBrandKey key = new UnitPartnerBrandKey(data.getUnitId(), data.getBrandId(), data.getPartnerId());
			if (!metadata.containsKey(key) || metadata.get(key) == null) {
				metadata.put(key, new HashMap<>());
			}
			if (!cache.getUnitPartnerBrandMetadataMap().containsKey(key)
					|| cache.getUnitPartnerBrandMetadataMap().get(key) == null) {
				cache.getUnitPartnerBrandMetadataMap().put(key, new HashMap<>());
			}
			metadata.get(key).put(data.getKey(), data.getValue());
			cache.getUnitPartnerBrandMetadataMap().get(key).put(data.getKey(), data.getValue());
		}
		metadata.forEach((key, value) -> cache.getUnitPartnerBrandMetadataMap().put(key, value));
	}

	@Override
	public void refreshEntityAliasMappingData() {
		cache.clearEntityAliasMappingData();
		List<EntityAliasMappingData> entityAliasMappingData = entityAliasManagementService.getAllEntityAlias();
		for (EntityAliasMappingData data : entityAliasMappingData) {
			EntityAliasKey entityAliasKey = MasterDataConverter.convert(data);
			cache.getEntityAliasMappingData().put(entityAliasKey, data);
		}
	}


	@Override
	public void refreshRegionsMapData(){
		cache.clearRegionMap();
		List<RegionMap> regionMapList = metadataDao.getAllRegions();
		for(RegionMap regionMap : regionMapList){
			cache.getRegionMap().put(regionMap.getUnitRegion(),regionMap.getRegionValue());
		}
	}

	private void notifyToInventory(int unitId) {
		try {
			inventoryService.publishInventorySQSFifo(props.getEnvironmentType().name(), new QuantityResponseData(unitId, null,
					InventoryAction.FLUSH, InventorySource.CACHE_REFRESH, null, AppUtils.getCurrentTimestamp()));
		} catch (Exception e) {
			LOG.error("error notifying inventory::", e);
		}
	}

	private void updateUnitEmailIdsList(String emailId, MasterDataCacheProxy cacheProxy) {
		cacheProxy.getUnitsEmailIds().add(emailId);
//		cache.getAllUnitsEmailId().add(emailId);
	}

	private void refreshUnitContactDetails() throws DataNotFoundException {
		cache.clearUnitContactDetailsMap();
		cache.clearUnitContactDetailsMap();
		Map<Integer, UnitContactDetails> unitContactDetailsMap = metadataDao.getAllUnitContactDetails();
		cache.getUnitContactDetailsIMap().putAll(unitContactDetailsMap);
		LOG.info("UnitContactDetails refreshed, number of units contacts loaded {}",cache.getUnitContactDetailsIMap().size());
	}

	@Override
	public void refreshSpecialMilkVariantMap(){
		cache.clearSpecialMilkVariantMap();
		Map<Integer,Boolean> milkVariantMap = props.getMilkSelectionPaidAddons();
		try{
			milkVariantMap.keySet().forEach(key -> {
				cache.setSpecialMilkVariantMap(cache.getProduct(key).getName(),key);
			});
		}catch (Exception e){
			LOG.error("Error While Getting Special Milk Variant List :: {} ", e);
		}
	}

	@Override
	public IMap<Integer, DispenserCanisterItemDataDto> getDispenserItemCanisterCache() {
		return cache.getDispenserCanisterItemDataMap();
	}

	@Override
	public void refreshDispenserCanisterItemDataMap() {
		try {
			cache.clearDispenserCanisterItemDataMap();
			Map<Integer, DispenserCanisterItemDataDto> dispenserCanisterItemDataMap = getDispenserCanisterItemDataMap();
			cache.getDispenserCanisterItemDataMap().putAll(dispenserCanisterItemDataMap);
			Map<Integer, Integer> canisterToProductMap = dispenserCanisterItemDataMap.entrySet().stream().collect(Collectors.toMap(e -> e.getValue().getDispenserCanisterItemDataId(), Map.Entry::getKey, (e1, e2) -> e1));
			cache.clearCanisterToProductMap();
			cache.getCanisterToProductMap().putAll(canisterToProductMap);
		} catch (Exception e) {
			LOG.error("Error while refreshDispenserCanisterItemDataMap :: ", e);
		}
	}

	private Map<Integer, DispenserCanisterItemDataDto> getDispenserCanisterItemDataMap() {
		List<DispenserCanisterItemData> dispenserCanisterItemDataList = metadataDao.getDispenserCanisterItemData();
		if (!CollectionUtils.isEmpty(dispenserCanisterItemDataList)) {
			return dispenserCanisterItemDataList.stream().
					collect(Collectors.toMap(DispenserCanisterItemData::getProductId,
							e -> getDispenserCanisterItemDataDto(DtoDataMapper.INSTANCE.toDispenserCanisterItemDataDto(e)),
							(e1, e2) -> e1));
		}
		return new HashMap<>();
	}

	private DispenserCanisterItemDataDto getDispenserCanisterItemDataDto(DispenserCanisterItemDataDto dispenserCanisterItemDataDto) {
		if (PattiSugarType.getDefaultProductIds().contains(dispenserCanisterItemDataDto.getProductId())) {
			List<DispenserPattiSugarShotInfoData> dispenserPattiSugarShotInfoDataList = metadataDao.getDispenserPattiSugarShotInfoData(PattiSugarType.valueOf(dispenserCanisterItemDataDto.getProductName()));
			if (!CollectionUtils.isEmpty(dispenserPattiSugarShotInfoDataList)) {
				Map<Integer, DispenserPattiSugarShotInfoDataDto> shotsInfoByProduct = dispenserPattiSugarShotInfoDataList.stream().collect(Collectors.toMap(DispenserPattiSugarShotInfoData::getProductId,
						DtoDataMapper.INSTANCE::dispenserPattiSugarShotInfoDataToDispenserPattiSugarShotInfoDataDto, (e1, e2) -> e1));
				dispenserCanisterItemDataDto.setDispenserPattiSugarShotInfoDataDtoMap(shotsInfoByProduct);
			}
		}
		return dispenserCanisterItemDataDto;
	}

	public void refreshSingleUnitContacts(UnitContactDetailsData unitContactDetailsData) {
		UnitContactDetails unitContactDetails = MasterDataConverter.convert(unitContactDetailsData);
		cache.getUnitContactDetailsIMap().put(unitContactDetails.getUnitId(), unitContactDetails);
		LOG.info("Single UnitContactDetails refreshed of unitId {}", unitContactDetails.getUnitId());
	}


	@Override
	public void refreshAllPriceProfileToProductCache(){
		Map<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>> proxyPriceProfileMap = getPriceProfileMapFromDao();
		cache.clearProductPriceMap();
		cache.getAllProductPriceMap().putAll(proxyPriceProfileMap);
		log.info("map : {}", new Gson().toJson(cache.getAllProductPriceMap()));
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>> getPriceProfileMapFromDao(){
		Map<PriceProfileKey,Map<ProductDimensionKey,BigDecimal>> proxyPriceProfileMap = new HashMap<>();
		List<Integer> activePriceProfiles  = priceProfileDao.findAllByStatus(AppConstants.ACTIVE).stream().
				map(PriceProfileData::getPriceProfileDataId).toList();
		Map<PriceProfileKey , Boolean> activePriceProfileVersionsMap = priceProfileVersionDao.findByProfileIdsAndStatus(activePriceProfiles,AppConstants.ACTIVE)
				.stream().collect(Collectors.toMap(version -> PriceProfileKey.builder().priceProfileId(version.getPriceProfileData().getPriceProfileDataId())
						.priceProfileVersion(version.getVersionNo()).build(),version-> Boolean.TRUE));
		for(PriceProfileKey key :  activePriceProfileVersionsMap.keySet()){
			log.info("key : {}",key);
			Map<ProductDimensionKey,BigDecimal> productPriceMap = priceProfileProductMappingsDao.filterMappings(key.getPriceProfileVersion()
					,key.getPriceProfileId(),null,null).stream().collect(Collectors.toMap(
					mapping -> new ProductDimensionKey(mapping.getProductId(),mapping.getDimensionCode().getRlCode()),PriceProfileProductMapping::getPrice
					,(p1,p2)->  p2));
			proxyPriceProfileMap.put(key,productPriceMap);
		}
		return  proxyPriceProfileMap;
	}



	@Override
	public void updatePriceProfileStateMapForUnit(Integer unitId , Boolean value){
		cache.updateUnitPriceProfileUpdateMap(unitId,value);
	}

	@Override
	public void clearCustomerAppliedCouponCache(){
		cache.getCustomerAppliedCouponDetailMap().clear();
	}
	@Override
	public void refreshPriceCategoryWiseProductsPrice(){
		Map< String,Map<Integer,  Set<UnitProductPriceCategoryDomain>>> map = productManagementService.getUnitsSpecificProductsPrice();
		cache.clearPriceCategoryWiseProductsPrice();
		cache.getPriceCategoryWiseProductsPrice().putAll(map);
		LOG.info("Price Category Wise Products Price refreshed");

	}

	@Override
	public IMap<Integer, Pair<String, String>> getUnitMonkRecipeProfileVersionImap() {
		return recipeCache.getUnitMonkRecipeProfileVersion();
	}

	public boolean refreshUnitDroolVersionMapping() {
		try {
			Map<Integer, Map<String, DroolVersionDomain>> unitDroolVersionMapping = new HashMap<>();
			List<UnitDroolVersionMapping> droolVersionMappings = droolVersionMappingDao.findByMappingStatus(AppConstants.ACTIVE);
			log.info("drool mappimgs size for cache :::: {}",droolVersionMappings.size());
			for (UnitDroolVersionMapping mapping : droolVersionMappings) {
				unitDroolVersionMapping.computeIfAbsent(mapping.getUnitId(), k -> new HashMap<>())
						.computeIfAbsent(mapping.getDroolType(), k -> getDroolVersionDomain(new DroolVersionDomain(), mapping));
			}
			log.info("Drool cache structure :::: {}",new Gson().toJson(unitDroolVersionMapping));
			cache.clearUnitDroolVersionMapping();
			cache.getUnitDroolVersionMapping().putAll(unitDroolVersionMapping);
			return true;
		}catch (Exception e){
			LOG.info("Error while refreshing Unit drool version mapping cache ::::: {}",e);
			return false;
		}
	}

	public DroolVersionDomain getDroolVersionDomain(DroolVersionDomain domain, UnitDroolVersionMapping mapping) {
		domain.setVersion(mapping.getVersion());
		domain.setCreationTime(mapping.getCreationTime());
		domain.setCreatedBy(mapping.getCreatedBy());
		domain.setUpdatedBy(mapping.getUpdatedBy());
		domain.setLastUpdationTime(mapping.getLastUpdationTime());
		domain.setRemarks(mapping.getRemarks());
		return domain;
	}

	@Override
	public void refreshCompanyBrandsMap() {
		try {
			Map<Integer, List<Brand>> companyBrandsMap = new HashMap<>();
			List<CompanyBrandMapping> companyBrandMappings = companyBrandMappingDao.findByStatus(AppConstants.ACTIVE);
			List<Brand> activeBrandDetails = cache.getAllBrands();
			Map<Integer, Brand> brandIdDetailMap = new HashMap<>();
			for (Brand bd : activeBrandDetails) {
				brandIdDetailMap.put(bd.getBrandId(), bd);
			}
			for (CompanyBrandMapping cbm : companyBrandMappings) {
				if (companyBrandsMap.containsKey(cbm.getCompanyId())) {
					companyBrandsMap.get(cbm.getCompanyId()).add(brandIdDetailMap.get(cbm.getBrandId()));
				} else {
					List<Brand> brandDetails = new ArrayList<>();
					brandDetails.add(brandIdDetailMap.get(cbm.getBrandId()));
					companyBrandsMap.put(cbm.getCompanyId(), brandDetails);
				}
			}
			cache.clearCompanyBrandsMap();
			cache.getCompanyBrandsMap().putAll(companyBrandsMap);
		} catch (Exception e) {
			LOG.info("Error occurred while refreshing Company Brands Mapping Cache : {}", e);
			throw new RuntimeException("Error occurred while refreshing Company Brands Mapping Cache", e);
		}
	}

	@Override
	public void refreshUnitBrandMaps() {
		try {
			Map<Integer, List<Brand>> unitBrandsMap = new HashMap<>();
			Map<Integer, List<Integer>> brandUnitsMap = new HashMap<>();

			List<UnitBrandMapping> unitBrandMappings = unitBrandMappingDao.findByStatus(AppConstants.ACTIVE);
			List<Brand> activeBrandDetails = cache.getAllBrands();

			Map<Integer, Brand> brandIdDetailMap = new HashMap<>();
			Map<Integer, UnitBasicDetail> unitIdDetailMap = cache.getUnitsBasicDetails();

			for (Brand bd : activeBrandDetails) {
				brandIdDetailMap.put(bd.getBrandId(), bd);
			}

			for (UnitBrandMapping ubm : unitBrandMappings) {
				if (unitBrandsMap.containsKey(ubm.getUnitId())) {
					unitBrandsMap.get(ubm.getUnitId()).add(brandIdDetailMap.get(ubm.getBrandId()));
				} else {
					List<Brand> brandDetails = new ArrayList<>();
					brandDetails.add(brandIdDetailMap.get(ubm.getBrandId()));
					unitBrandsMap.put(ubm.getUnitId(), brandDetails);
				}

				if (brandUnitsMap.containsKey(ubm.getBrandId())) {
					brandUnitsMap.get(ubm.getBrandId()).add(unitIdDetailMap.get(ubm.getUnitId()).getId());
				} else {
					List<Integer> unitIds = new ArrayList<>();
					unitIds.add(unitIdDetailMap.get(ubm.getUnitId()).getId());
					brandUnitsMap.put(ubm.getBrandId(), unitIds);
				}
			}

			cache.clearUnitBrandsMap();
			cache.getUnitBrandsMap().putAll(unitBrandsMap);

			cache.clearBrandUnitsMap();
			cache.getBrandUnitsMap().putAll(brandUnitsMap);

		} catch (Exception e) {
			LOG.info("Error occurred while refreshing Unit Brand Mappings Cache : {}", e);
			throw new RuntimeException("Error occurred while refreshing Unit Brand Mappings Cache", e);
		}
	}

}
