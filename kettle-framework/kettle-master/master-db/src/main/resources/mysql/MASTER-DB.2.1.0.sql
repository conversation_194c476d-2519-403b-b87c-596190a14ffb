CREATE TABLE KETTLE_MASTER_DEV.BUSINESS_HOURS (
    BUSINESS_HOURS_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
    UNIT_ID INTEGER NOT NULL,
    IS_OPERATIONAL VARCHAR(1) NOT NULL,
    HAS_DINE_IN VARCHAR(1) NOT NULL,
    HAS_DELIVERY VARCHAR(1) NOT NULL,
    HAS_TAKE_AWAY VARCHAR(1) NOT NULL,
    DAY_OF_WEEK_NUMBER INTEGER NOT NULL,
   	DAY_OF_WEEK_TEXT VARCHAR(10) NOT NULL,
    NO_OF_SHIFTS INTEGER NOT NULL,
    DINE_IN_OPEN_TIME TIME NULL,
    DINE_IN_CLOSE_TIME TIME NULL,
    DELIVERY_OPEN_TIME TIME NULL,
    DELIVERY_CLOSE_TIME TIME NULL,
    TAKE_AWAY_OPEN_TIME TIME NULL,
    TAKE_AWAY_CLOSE_TIME TIME NULL,
    SHIFT_ONE_HANDOVER_TIME TIME NULL,
    SHIFT_TWO_HANDOVER_TIME TIME,
    BUSINESS_HOURS_STATUS VARCHAR(15) NOT NULL DEFAULT 'ACTIVE');

INSERT INTO TAX_PROFILE(TAX_PROFILE_ID, TAX_TYPE, TAX_NAME)
VALUES(7, 'KK_CESS' , 'K.K. Cess');
INSERT INTO UNIT_TAX_MAPPING(TAX_PROFILE_ID,UNIT_ID, TAX_PERCENTAGE, PROFILE_STATUS, STATE)
select 7,UNIT_ID, '0.2', PROFILE_STATUS, STATE from UNIT_TAX_MAPPING where TAX_PROFILE_ID = 6;

CREATE INDEX PRICING_STATUS ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING(PRICING_STATUS) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA CHANGE COLUMN BIRTH_MONTH EVENT_MONTH INTEGER;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA CHANGE COLUMN BIRTH_DATE EVENT_DATE INTEGER;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA CHANGE COLUMN BIRTHDAY_OFFER_1 EVENT_OFFER_1 varchar(50);
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA CHANGE COLUMN BIRTHDAY_OFFER_2 EVENT_OFFER_2 VARCHAR(50);
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA ADD COLUMN EVENT_TYPE VARCHAR(50) DEFAULT 'Birthday' AFTER CUSTOMER_ID;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA ADD COLUMN ACQUISITION_SOURCE VARCHAR(50) DEFAULT 'WHATSAPP' AFTER BIRTH_DATE ;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA ADD COLUMN CREATED_AT DATETIME AFTER EVENT_TYPE;
ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA RENAME TO CUSTOMER_EVENTS_DETAIL_DATA;

CREATE TABLE KETTLE_MASTER_DEV.UNIT_CAMERA_DETAIL(
	ID INTEGER PRIMARY KEY NOT NULL AUTO_INCREMENT,
	UNIT_ID INTEGER NOT NULL,
    TERMINAL INTEGER NOT NULL,
    CAMERA_ID INTEGER NOT NULL,
    STATUS VARCHAR(25) DEFAULT 'ACTIVE'
);
