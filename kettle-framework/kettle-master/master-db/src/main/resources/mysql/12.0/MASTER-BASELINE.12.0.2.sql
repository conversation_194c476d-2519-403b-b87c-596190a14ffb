ALTER TABLE KETTLE_MASTER_DEV.ME<PERSON>_RECOMMENDATION_MAPPING_DATA ADD COLUMN DISCOUNT_AMOUNT DECIMAL(16,6) NULL;
ALTER TABLE KETTLE_MASTER_DEV.MENU_RECOMMENDATION_MAPPING_DATA ADD COLUMN DISCOUNT_TYPE VARCHAR(20) NULL;


ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
ADD COLUMN `POS_VERSION` INT NULL DEFAULT 1 ;

ALTER TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_ALLOCATION_DETAIL_DATA
ADD START_DATE TIMESTAMP NULL,
ADD END_DATE TIMESTAMP NULL;

CREATE TABLE KETTLE_MASTER_DEV.MENU_SEQUENCE_TIMING_DATA (
  MENU_SEQUENCE_TIMING_ID INT(11) PRIMARY KEY AUTO_INCREMENT,
  MENU_SEQUENCE_ID INT(11) NOT NULL,
  PRODUCT_GROUP_PARENT_ID INT(11) NOT NULL,
  SERVICE VARCHAR(200),
  TIME_1_FROM VARCHAR(45),
  TIME_1_TO VARCHAR(45),
  TIME_2_FROM VARCHAR(45),
  TIME_2_TO VARCHAR(45),
  TIME_3_FROM VARCHAR(45),
  TIME_3_TO VARCHAR(45),
  DAY_MONDAY BOOLEAN,
  DAY_TUESDAY BOOLEAN,
  DAY_WEDNESDAY BOOLEAN,
  DAY_THURSDAY BOOLEAN,
  DAY_FRIDAY BOOLEAN,
  DAY_SATURDAY BOOLEAN,
  DAY_SUNDAY BOOLEAN,
  START_DATE VARCHAR(45),
  END_DATE VARCHAR(45)
);


INSERT INTO KETTLE_MASTER_DEV.CACHE_REFERENCE_METADATA (`CACHE_ID`, `REFERENCE_TYPE`, `REFERENCE_VALUE`, `CACHE_STATUS`, `ADDED_BY`, `UPDATED_BY`) VALUES ('6', 'WALLET_RECOMMENDATION_DROOLS_STRATEGY', 'Y', 'ACTIVE', 'SYSTEM', '100026');

INSERT INTO KETTLE_MASTER_DEV.`CACHE_REFERENCE_METADATA` (`REFERENCE_TYPE`, `REFERENCE_VALUE`, `CACHE_STATUS`, `ADDED_BY`, `UPDATED_BY`) VALUES ('LOYALTY_AWARD_THRESHOLD_AMOUNT', '79', 'ACTIVE', 'SYSTEM', '100026');
INSERT INTO KETTLE_MASTER_DEV.`CACHE_REFERENCE_METADATA` (`REFERENCE_TYPE`, `REFERENCE_VALUE`, `CACHE_STATUS`, `ADDED_BY`, `UPDATED_BY`) VALUES ('LOYALTY_AWARD_THRESHOLD_TIME', '240', 'ACTIVE', 'SYSTEM', '100026');

CREATE TABLE `KETTLE_MASTER_DEV`.`DOCUMENT_DETAIL_DATA`
(
    `DOCUMENT_ID`             INT(11) NOT NULL AUTO_INCREMENT,
    `FILE_TYPE`               VARCHAR(45)  NOT NULL,
    `DOCUMENT_LINK`           VARCHAR(200) NOT NULL,
    `UPDATE_TIME`             DATETIME     NOT NULL,
    `UPDATED_BY`              INT(11) DEFAULT NULL,
    `DOCUMENT_MIME_TYPE`      VARCHAR(45)  NOT NULL,
    `DOCUMENT_UPLOAD_TYPE`    VARCHAR(45)  NOT NULL,
    `DOCUMENT_UPLOAD_TYPE_ID` INT(11) DEFAULT NULL,
    `DOCUMENT_S3_KEY`         VARCHAR(180) DEFAULT NULL,
    `DOCUMENT_S3_BUCKET`      VARCHAR(180) DEFAULT NULL,
    `DOCUMENT_URL`            VARCHAR(500) DEFAULT NULL,
    PRIMARY KEY (`DOCUMENT_ID`),
    UNIQUE KEY `DOCUMENT_ID_UNIQUE` (`DOCUMENT_ID`)
);

CREATE TABLE `KETTLE_MASTER_DEV`.`EMPLOYEE_USER_POLICY_RESET_LOG`
(
    `EMPLOYEE_USER_POLICY_RESET_LOG_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `EMPLOYEE_ID`                       INT(11) DEFAULT NULL,
    `PREVIOUS_USER_POLICY_ID`           INT(11) DEFAULT NULL,
    `NEW_USER_POLICY_ID`                INT(11) DEFAULT NULL,
    `UPLOADED_DOCUMENT_ID`              INT(11) DEFAULT NULL,
    `LOGGED_BY`                         VARCHAR(300) DEFAULT NULL,
    `LOGGED_AT`                         TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`EMPLOYEE_USER_POLICY_RESET_LOG_ID`),
    UNIQUE KEY `EMPLOYEE_USER_POLICY_RESET_LOG_ID_UNIQUE` (`EMPLOYEE_USER_POLICY_RESET_LOG_ID`)
);

CREATE TABLE `KETTLE_MASTER_DEV`.`ROLE_CHANGE_AUDIT_LOG_DATA`
(
    `ROLE_CHANGE_AUDIT_LOG_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `KEY_ID`                   INT(11) NOT NULL,
    `KEY_TYPE`                 VARCHAR(45)  NOT NULL,
    `ROLE_ID`                  INT(11) NOT NULL,
    `UPLOADED_DOCUMENT_ID`     INT(11) DEFAULT NULL,
    `LOGGED_BY`                VARCHAR(300) NOT NULL,
    `LOGGED_AT`                TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`ROLE_CHANGE_AUDIT_LOG_ID`),
    UNIQUE KEY `ROLE_CHANGE_AUDIT_ID_UNIQUE` (`ROLE_CHANGE_AUDIT_LOG_ID`)
);

CREATE TABLE `KETTLE_MASTER_DEV`.`USER_POLICY_DATA`
(
    `USER_POLICY_ID`         INT(11) NOT NULL AUTO_INCREMENT,
    `POLICY_NAME`            VARCHAR(400) NOT NULL,
    `POLICY_DESCRIPTION`     VARCHAR(500) NOT NULL,
    `DEPARTMENT_DESIGNATION` VARCHAR(100) NOT NULL,
    `CREATED_BY`             VARCHAR(255) NOT NULL,
    `CREATED_AT`             TIMESTAMP NULL DEFAULT NULL,
    `UPDATED_BY`             VARCHAR(255) DEFAULT NULL,
    `UPDATED_AT`             TIMESTAMP NULL DEFAULT NULL,
    PRIMARY KEY (`USER_POLICY_ID`),
    UNIQUE KEY `USER_POLICY_ID_UNIQUE` (`USER_POLICY_ID`),
    UNIQUE KEY `POLICY_NAME_UNIQUE` (`POLICY_NAME`)
);

CREATE TABLE `KETTLE_MASTER_DEV`.`USER_POLICY_ROLE_MAPPING`
(
    `USER_POLICY_ROLE_MAPPING_ID` INT(11) NOT NULL AUTO_INCREMENT,
    `USER_POLICY_ID`              INT(11) NOT NULL,
    `ROLE_ID`                     INT(11) NOT NULL,
    `MAPPING_STATUS`              VARCHAR(45) NOT NULL,
    PRIMARY KEY (`USER_POLICY_ROLE_MAPPING_ID`),
    UNIQUE KEY `USER_POLICY_ROLE_MAPPING_ID_UNIQUE` (`USER_POLICY_ROLE_MAPPING_ID`)
);

ALTER TABLE `KETTLE_MASTER_DEV`.`EMPLOYEE_DETAIL`
ADD COLUMN `USER_POLICY_ID` INT(11) NULL DEFAULT NULL AFTER `LOCATION_CODE`;

ALTER TABLE `KETTLE_MASTER_DEV`.`USER_POLICY_DATA`
ADD COLUMN `UPLOADED_DOCUMENT_ID` INT NOT NULL AFTER `DEPARTMENT_DESIGNATION`;




CREATE TABLE KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_GROUP(
                                                            GROUP_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
                                                            GROUP_SOURCE VARCHAR(15) NOT NULL,
                                                            GROUP_NAME VARCHAR(40) NOT NULL,
                                                            CREATED_BY VARCHAR(50) NOT NULL,
                                                            CREATED_ON TIMESTAMP NOT NULL,
                                                            GROUP_STATUS VARCHAR(15) NOT NULL);

CREATE INDEX PRODUCT_CONDIMENT_GROUP_GROUP_STATUS ON KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_GROUP(GROUP_STATUS) USING BTREE;
CREATE INDEX PRODUCT_CONDIMENT_GROUP_GROUP_SOURCE ON KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_GROUP(GROUP_SOURCE) USING BTREE;

CREATE TABLE KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_ITEM(
                                                           GROUP_ITEM_ID INTEGER NOT NULL PRIMARY KEY AUTO_INCREMENT,
                                                           GROUP_ID INTEGER NOT NULL,
                                                           CONDIMENT_NAME VARCHAR(40) NOT NULL,
                                                           CONDIMENT_QUANTITY INTEGER NOT NULL,
                                                           CREATED_BY VARCHAR(50) NOT NULL,
                                                           CREATED_ON TIMESTAMP NOT NULL,
                                                           CONDIMENT_ITEM_STATUS VARCHAR(15) NOT NULL);

CREATE INDEX PRODUCT_CONDIMENT_ITEM_CONDIMENT_ITEM_STATUS ON KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_ITEM(CONDIMENT_ITEM_STATUS) USING BTREE;
CREATE INDEX PRODUCT_CONDIMENT_ITEM_GROUP_ID ON KETTLE_MASTER_STAGE.PRODUCT_CONDIMENT_ITEM(GROUP_ID) USING BTREE;

CREATE TABLE KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA (
  ID INT NOT NULL AUTO_INCREMENT,
  UNIT_ID INT NOT NULL,
  TERMINAL_ID INT NOT NULL,
  IP_ADDRESS VARCHAR(30) NOT NULL,
  LAST_UPDATE_TIME TIMESTAMP NOT NULL,
  APP_NAME VARCHAR(20) NOT NULL,
  PRIMARY KEY (ID));

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Recipes Approver', 'Access to Approve Reject Recipes', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADRAR', '5', 'ACTION', 'SHOW', 'Kettle Admin -> Kettle/SCM Recipe -> Approve', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Recipes Approver'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADRAR'), 'ACTIVE', '120063', '2023-08-22 12:00:00');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Request Recipe Approval', 'Access to Request/Cancel Recipe For Approval', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADRFAC', '5', 'ACTION', 'SHOW', 'Kettle Admin -> Kettle/SCM Recipe -> Approve', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Request Recipe Approval'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADRFAC'), 'ACTIVE', '120063', '2023-08-22 12:00:00');
CREATE INDEX UNIT_IP_ADDRESS_DATA_UNIT_ID ON KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA(UNIT_ID) USING BTREE;
CREATE INDEX UNIT_IP_ADDRESS_DATA_TERMINAL_ID ON KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA(TERMINAL_ID) USING BTREE;
CREATE INDEX UNIT_IP_ADDRESS_DATA_APP_NAME ON KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA(APP_NAME) USING BTREE;
CREATE INDEX UNIT_IP_ADDRESS_DATA_IP_ADDRESS ON KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA(IP_ADDRESS) USING BTREE;
CREATE INDEX UNIT_IP_ADDRESS_DATA_LAST_UPDATE_TIME ON KETTLE_MASTER_DEV.UNIT_IP_ADDRESS_DATA(LAST_UPDATE_TIME) USING BTREE;


ALTER TABLE KETTLE_MASTER_STAGE.UNIT_TO_PARTNER_EDC_MAPPING
    ADD COLUMN `SECRET_KEY` VARCHAR(256) NULL DEFAULT NULL AFTER `T_ID`;

CREATE TABLE KETTLE_MASTER_DEV.REDEEMED_COUPON_DETAIL_DATA (
  `DELIVERY_COUPON_ID` int(11) NOT NULL AUTO_INCREMENT,
  `BILL_AMOUNT` double DEFAULT NULL,
  `BRAND_ID` int(11) DEFAULT NULL,
  `COUPON_CODE` varchar(255) DEFAULT NULL,
  `COUPON_DISCOUNT` double DEFAULT NULL,
  `MERCHENT_DISCOUNT` double DEFAULT NULL,
  `REGION` varchar(255) DEFAULT NULL,
  `UPDATED_AT` datetime DEFAULT NULL,
  `UPDATED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`DELIVERY_COUPON_ID`)
);

ALTER TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA ADD IS_REDEEMED VARCHAR(10) NULL;


CREATE TABLE KETTLE_MASTER_DEV.APPLICATION_VERSION_DETAIL (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) DEFAULT NULL,
  `APPLICATION_VERSION` varchar(255) DEFAULT NULL,
  `VERSION_STATUS` varchar(255) DEFAULT NULL,
  `UPDATED_BY` int(11) DEFAULT NULL,
  `UPDATED_TIME` datetime DEFAULT NULL,
  `RELEASED_TYPE` varchar(255) DEFAULT NULL,
  `BUILD_NAME` varchar(255) DEFAULT NULL,
  `DEPLOYMENT_DESCRIPTION` text DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ;


CREATE TABLE KETTLE_MASTER_DEV.APPLICATION_VERSION_DETAIL_DATA (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) DEFAULT NULL,
  `APPLICATION_VERSION` varchar(255) DEFAULT NULL,
  `LAST_UPDATE_TIME` datetime DEFAULT NULL,
  `TERMINAL` int(11) DEFAULT NULL,
  `UNIT_ID` int(11) DEFAULT NULL,
  `UNIT_REGION` varchar(255) DEFAULT NULL,
  `VERSION_STATUS` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`ID`)
) ;

CREATE TABLE KETTLE_MASTER_DEV.APPLICATION_VERSION_EVENT (
  `EVENT_ID` int(11) NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) NOT NULL,
  `APPLICATION_VERSION` varchar(255) NOT NULL,
  `EVENT_STATUS` varchar(255) NOT NULL,
  `UNIT_ID` int(11) NOT NULL,
  `UNIT_REGION` varchar(255) NOT NULL,
  `UPDATED_BY` varchar(255) NOT NULL,
  `UPDATION_TIME` datetime NOT NULL,
  `TERMINAL_ID` int(11) DEFAULT NULL,
  PRIMARY KEY (`EVENT_ID`)
);

CREATE TABLE KETTLE_MASTER_DEV.VERSION_COMPATIBILITY_DATA (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `APPLICATION_NAME` varchar(255) DEFAULT NULL,
  `APPLICATION_VERSION` varchar(255) DEFAULT NULL,
  `POS_VERSION` varchar(255) DEFAULT NULL,
  `VERSION_STATUS` varchar(255) DEFAULT NULL,
  `UPDATED_AT` datetime DEFAULT NULL,
  `UPDATED_BY` int(11) DEFAULT NULL,
  PRIMARY KEY (`ID`)
);

INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)VALUES ('Application Version Management', 'Access to application version management', 'ACTIVE', '5');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)VALUES ('ADMN_APPVER_MGT', '5', 'MENU', 'SHOW', 'Admin -> Application Version Management', 'ACTIVE');

INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Application Version Management'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_APPVER_MGT'), 'ACTIVE', '120057', '2023-09-12 12:00:00');

CREATE TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA(
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `BIRTH_MONTH` int(11) DEFAULT NULL,
  `CONTACT_NUMBER` varchar(255) DEFAULT NULL,
  `CUSTOMER_ID` int(11) DEFAULT NULL,
  `UPDATED_AT` datetime DEFAULT NULL,
  PRIMARY KEY (`ID`)
);


INSERT INTO `KETTLE_MASTER`.`CACHE_REFERENCE_METADATA` (`CACHE_ID`, `REFERENCE_TYPE`, `REFERENCE_VALUE`, `CACHE_STATUS`, `ADDED_BY`, `UPDATED_BY`)
 VALUES ('10', 'SEND_SMS_FROM_SUMO', 'Y', 'ACTIVE', '140199', '140199');


ALTER TABLE KETTLE_MASTER_DEV.CUSTOMER_BIRTHDAY_DETAIL_DATA
ADD BIRTH_DATE INT DEFAULT NULL;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_B2B_MNK_MGT', '5', 'MENU', 'SHOW', 'Admin -> B2B Monk Management -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('B2B Monk Management Access', 'Access to Manage B2B Monk Details', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'B2B Monk Management Access'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_B2B_MNK_MGT'), 'ACTIVE', '100026', '2023-09-28 00:00:00');


INSERT INTO KETTLE_MASTER_STAGE.USER_ROLE_DATA
(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Product Profile Update Access', 'Access to update product profile', 'ACTIVE', '5');

INSERT INTO KETTLE_MASTER_STAGE.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_PPU_MGT', '5', 'ACTION', 'UPDATE', 'Admin -> Product List -> Product Price Profile', 'ACTIVE');

INSERT INTO KETTLE_MASTER_STAGE.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_STAGE.USER_ROLE_DATA WHERE ROLE_NAME = 'Product Profile Update Access'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_STAGE.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_PPU_MGT'), 'ACTIVE', '120057', '2023-10-18 10:00:00');

ALTER TABLE KETTLE_MASTER_STAGE.CUSTOMER_BIRTHDAY_DETAIL_DATA
ADD COUPON_GENERATED_AT datetime DEFAULT NULL;

ALTER TABLE KETTLE_MASTER_STAGE.CUSTOMER_BIRTHDAY_DETAIL_DATA
ADD BIRTHDAY_OFFER_1 varchar(255) DEFAULT NULL;

ALTER TABLE KETTLE_MASTER_STAGE.CUSTOMER_BIRTHDAY_DETAIL_DATA
ADD BIRTHDAY_OFFER_2 varchar(255) DEFAULT NULL;




