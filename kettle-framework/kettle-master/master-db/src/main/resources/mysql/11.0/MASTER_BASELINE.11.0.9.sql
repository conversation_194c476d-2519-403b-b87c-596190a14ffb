CREATE TABLE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(
	  EVENT_ID INT(11) NOT NULL AUTO_INCREMENT,
    TOTAL_RECORDS INTEGER NOT NULL,
    TOTAL_RECORDS_CHANGED INTEGER NOT NULL,
    TOTAL_RECORDS_UPDATED_SUCCESSFULLY INTEGER,
    TOTAL_ERROR_RECORDS INTEGER NOT NULL,
    TOTAL_FAILURE_RECORDS INTEGER,
    UPDATED_BY INT(11) NOT NULL,
    UPDATE_TIME TIMESTAMP NOT NULL,
    SHEET_PATH VARCHAR(300),
    UNIT_CATEGORY VARCHAR(100) NOT NULL,
    BRAND_ID INT(11) NOT NULL,
    PRIMARY KEY(EVENT_ID)
);

CREATE TABLE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG(
	  UPDATE_LOG_ID INT(11) NOT NULL AUTO_INCREMENT,
    EVENT_ID INT(11) NOT NULL,
    UNIT_PROD_PRICE_ID INT(11) NOT NULL,
    OLD_PRICE DECIMAL(10,2) NOT NULL,
    NEW_PRICE DECIMAL(10,2) NOT NULL,
    PRIMARY KEY(UPDATE_LOG_ID),
    FOREIGN KEY(EVENT_ID) REFERENCES KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(EVENT_ID)
);

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ADD COLUMN REMINDER_DAYS INT(11);
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_REMINDER_DAYS ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(REMINDER_DAYS) USING BTREE;
UPDATE  KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL SET REMINDER_DAYS = 5;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ADD COLUMN CUSTOMER_TYPE VARCHAR(30);
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CUSTOMER_TYPE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CUSTOMER_TYPE) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ADD COLUMN CURRENT_JOURNEY_NUMBER INT(11);
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_CURRENT_JOURNEY_NUMBER ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(CURRENT_JOURNEY_NUMBER) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ADD COLUMN NEXT_JOURNEY_NUMBER INT(11);
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_NEXT_JOURNEY_NUMBER ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(NEXT_JOURNEY_NUMBER) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ADD COLUMN NEXT_JOURNEY_OFFER_DATE TIMESTAMP NULL DEFAULT NULL;
CREATE INDEX CUSTOMER_CAMPIAGN_OFFER_DETAIL_NEXT_JOURNEY_OFFER_DATE ON KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL(NEXT_JOURNEY_OFFER_DATE) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY ADD COLUMN DEVICE_TYPE varchar(20);
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_DEVICE_TYPE ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(DEVICE_TYPE) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY
DROP COLUMN JOURNEY_TYPE;
ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY
DROP COLUMN UTM_CAMPAIGN;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY MODIFY SESSION_START_TIME DATETIME;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_RSCMI', '8', 'ACTION', 'UPDATE', 'Inventory -> Refresh SCM Inventory -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Refresh SCM Inventory', 'Refresh SCM Inventory access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Refresh SCM Inventory'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_RSCMI'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_FRSCMI', '8', 'ACTION', 'UPDATE', 'Inventory -> Force Refresh SCM Inventory -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Force Refresh SCM Inventory', 'Force Refresh SCM Inventory access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Force Refresh SCM Inventory'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_FRSCMI'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_DK', '8', 'ACTION', 'UPDATE', 'Inventory -> Delete Keys -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Delete Keys', 'Delete Keys access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Delete Keys'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_DK'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_RAU', '8', 'ACTION', 'UPDATE', 'Inventory -> Refresh All Units -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Refresh All Units', 'Refresh All Units access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Refresh All Units'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_RAU'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_DCIS', '8', 'ACTION', 'UPDATE', 'Inventory -> Download Cafe Inventory Sheet -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Download Cafe Inventory Sheet', 'Download Cafe Inventory Sheet access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Download Cafe Inventory Sheet'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_DCIS'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_UT', '8', 'ACTION', 'UPDATE', 'Inventory -> Unit Timeline -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management Unit Timeline', 'Unit Timeline Sheet access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management Unit Timeline'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_UT'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('IM_SCMI_AI', '8', 'ACTION', 'UPDATE', 'Inventory -> SCM Inventory -> Add Inventory -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Inventory Management SCM Inventory Add Inventory', 'SCM Inventory Add Inventory access to kettle inventory', 'ACTIVE', '8');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Inventory Management SCM Inventory Add Inventory'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'IM_SCMI_AI'), 'ACTIVE', '120063', '2022-08-3 00:00:00');


ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN APPLICABLE_HOUR INTEGER;
CREATE INDEX OFFER_DETAIL_DATA_APPLICABLE_HOUR ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(APPLICABLE_HOUR) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN DAILY_FREQUENCY_COUNT INTEGER;
CREATE INDEX OFFER_DETAIL_DATA_DAILY_FREQUENCY_COUNT ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(DAILY_FREQUENCY_COUNT) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN APPLICABLE_FOR_ORDER VARCHAR(3);
CREATE INDEX CAMPAIGN_DETAIL_DATA_APPLICABLE_FOR_ORDER ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(APPLICABLE_FOR_ORDER) USING BTREE;

CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_SESSION_START_TIME ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(SESSION_START_TIME) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY ADD COLUMN BROWSER_NAME varchar(20);
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_BROWSER_NAME ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(BROWSER_NAME) USING BTREE;

ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY ADD COLUMN DEVICE_OS varchar(20);
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_DEVICE_OS ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(DEVICE_OS) USING BTREE;

CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_TOTAL_RECORDS ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(TOTAL_RECORDS) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_TOTAL_RECORDS_CHANGED ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(TOTAL_RECORDS_CHANGED) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_RECORDS_UPDATED_SUCSSFLY ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(TOTAL_RECORDS_UPDATED_SUCCESSFULLY) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_TOTAL_ERROR_RECORDS ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(TOTAL_ERROR_RECORDS) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_TOTAL_FAILURE_RECORDS ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(TOTAL_FAILURE_RECORDS) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_UPDATED_BY ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(UPDATED_BY) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_UPDATE_TIME ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(UPDATE_TIME) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_SHEET_PATH ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(SHEET_PATH) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_UNIT_CATEGORY ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(UNIT_CATEGORY) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT_BRAND_ID ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_EVENT(BRAND_ID) USING BTREE;

CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG_EVENT_ID ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG(EVENT_ID) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG_UNIT_PROD_PRICE_ID ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG(UNIT_PROD_PRICE_ID) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG_OLD_PRICE ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG(OLD_PRICE) USING BTREE;
CREATE INDEX UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG_NEW_PRICE ON KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING_BULK_UPDATE_LOG(NEW_PRICE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN UNIT_AUTO_APPLICABLE VARCHAR(10);
CREATE INDEX OFFER_DETAIL_DATA_UNIT_AUTO_APPLICABLE ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(UNIT_AUTO_APPLICABLE) USING BTREE;

UPDATE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA
SET  APPLICABLE_FOR_ORDER = 'Y' WHERE CAMPAIGN_STRATEGY IN ('NBO', 'DELIVERY_NBO');

UPDATE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA
SET  BRAND_ID = 1 WHERE BRAND_ID IS NULL;


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('UO_C_RC', '5', 'ACTION', 'UPDATE', 'Kettle Admin -> Unit Onboarding -> Refresh Cache -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Unit Onboarding Refresh Cache', 'Unit Onboarding refresh cache access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Unit Onboarding Refresh Cache'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UO_C_RC'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('UO_C_RCAI', '5', 'ACTION', 'UPDATE', 'Kettle Admin -> Unit Onboarding -> Refresh Cache And Inventory -> update', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Unit Onboarding Refresh Cache And Inventory', 'Unit Onboarding Refresh Cache And Inventory access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Unit Onboarding Refresh Cache And Inventory'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'UO_C_RCAI'), 'ACTIVE', '120063', '2022-08-3 00:00:00');

ALTER TABLE KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA ADD COLUMN APP_OFFER_TYPE VARCHAR(30);
CREATE INDEX APP_OFFER_DETAIL_DATA_APP_OFFER_TYPE ON KETTLE_MASTER_DEV.APP_OFFER_DETAIL_DATA(APP_OFFER_TYPE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL ADD COLUMN UNIT_ZONE VARCHAR(30);
CREATE INDEX UNIT_DETAIL_UNIT_ZONE ON KETTLE_MASTER_DEV.UNIT_DETAIL(UNIT_ZONE) USING BTREE;

ALTER TABLE `KETTLE_MASTER_DEV`.`UNIT_DETAIL`
ADD COLUMN `F9_ENABLED` VARCHAR(1) NULL DEFAULT 'N' AFTER `PRICING_PROFILE`;


INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_PRL_BUL', '5', 'SUBMENU', 'SHOW', 'Admin -> Product Price Bulk Update -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Product List Product Price Bulk Update', 'Product Price Bulk Update access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Product List Product Price Bulk Update'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_PRL_BUL'), 'ACTIVE', '120063', '2022-10-13 00:00:00');

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN PARENT_CAMPAIGN_STRATEGY VARCHAR(50);
CREATE INDEX CAMPAIGN_DETAIL_DATA_PARENT_CAMPAIGN_STRATEGY ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(PARENT_CAMPAIGN_STRATEGY) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.OFFER_DETAIL_DATA ADD COLUMN TERMS_AND_CONDITIONS VARCHAR(500);
CREATE INDEX OFFER_DETAIL_DATA_TERMS_AND_CONDITIONS ON KETTLE_MASTER_DEV.OFFER_DETAIL_DATA(TERMS_AND_CONDITIONS) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA ADD COLUMN MAX_USAGE INTEGER;
CREATE INDEX DELIVERY_COUPON_DETAIL_DATA_MAX_USAGE ON KETTLE_MASTER_DEV.DELIVERY_COUPON_DETAIL_DATA(MAX_USAGE) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.UNIT_DETAIL
    CHANGE COLUMN UNIT_SUB_CATEGORY UNIT_SUB_CATEGORY VARCHAR(25) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_MASTER_DEV`.`MENU_TO_SCM_PRODUCT_MAP`
ADD COLUMN `INGREDIENT_TYPE` VARCHAR(20) NULL DEFAULT NULL AFTER `SCM_PRODUCT_QUANTITY`;

ALTER TABLE `KETTLE_MASTER_DEV`.`MENU_TO_SCM_PRODUCT_MAP`
ADD COLUMN `PRODUCT_CLASSIFICATION` VARCHAR(20) NULL DEFAULT NULL AFTER `INGREDIENT_TYPE`;
CREATE TABLE KETTLE_MASTER_DEV.EMPLOYEE_APPLICATION_MAPPING_DETAIL (
    MAPPING_ID INTEGER AUTO_INCREMENT PRIMARY KEY ,
    EMPLOYEE_ID INTEGER ,
    MAPPING_TYPE VARCHAR (30),
    MAPPING_VALUE VARCHAR (50),
    RECORD_STATUS VARCHAR(30)
);


ALTER TABLE KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY ADD COLUMN GCLID VARCHAR(100) NULL DEFAULT NULL;
CREATE INDEX CUSTOMER_CAMPAIGN_JOURNEY_GCLID ON KETTLE_DEV.CUSTOMER_CAMPAIGN_JOURNEY(GCLID) USING BTREE;
INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Add New Report', 'Access to Add New Report', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADM_RD_ANR', '5', 'ACTION', 'SHOW', 'Admin -> Reporting Dashboard -> Add Update Report', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Add New Report'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADM_RD_ANR'), 'ACTIVE', '120063', '2022-12-21 12:00:00');

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PRODUCT_PRICING ADD COLUMN `IS_DELIVERY_ONLY_PRODUCT` VARCHAR(45) NULL AFTER `DIMENSION_DESCRIPTOR`;

CREATE TABLE KETTLE_MASTER_DEV.SYSTEM_STATUS_MAPPING
(
    MAPPING_ID         INTEGER AUTO_INCREMENT PRIMARY KEY,
    SYSTEM_1           varchar(50) default NULL,
    SYSTEM_2           varchar(50) default NULL,
    STATUS             varchar(15) default 'IN_ACTIVE',
    LAST_ACTIVITY_TIME varchar(20)
);

Alter table KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ADD COLUMN DOB date;
Alter table KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ADD COLUMN REASON_FOR_TERMINATION varchar(50);
Alter table KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ADD COLUMN HR_EXECUTIVE varchar(25);
Alter table KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ADD COLUMN LEAVE_APPROVAL_AUTHORITY varchar(25);
ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_DETAIL ADD COLUMN LOCATION_CODE int NOT NULL ;

ALTER TABLE KETTLE_MASTER_DEV.DEPARTMENT_DESIGNATION_MAPPING ADD COLUMN MAPPING_ID INTEGER  ;
ALTER TABLE KETTLE_MASTER_DEV.DEPARTMENT_DESIGNATION_MAPPING ADD COLUMN MAPPING_STATUS VARCHAR(15);
ALTER TABLE KETTLE_MASTER_DEV.DEPARTMENT_DESIGNATION_MAPPING ADD COLUMN EMPLOYEE_MEAL_ELIGIBLE boolean;

UPDATE KETTLE_MASTER_DEV.DEPARTMENT SET DEPT_NAME = 'Cafe', DEPT_DESC = 'Cafe' WHERE (DEPT_ID = '101');

UPDATE KETTLE_MASTER_DEV.DESIGNATION SET DESIGNATION_NAME = 'Customer Relationship Executive', DESIGNATION_DESC = 'Customer Relationship Executive' WHERE (`DESIGNATION_ID` = '1005');
UPDATE KETTLE_MASTER_DEV.DESIGNATION SET DESIGNATION_NAME = 'Utility Boy', DESIGNATION_DESC = 'Utility Boy' WHERE (`DESIGNATION_ID` = '1008');
UPDATE KETTLE_MASTER_DEV.DESIGNATION SET DESIGNATION_NAME = 'Cafe Manager', DESIGNATION_DESC = 'Cafe Manager' WHERE (`DESIGNATION_ID` = '1003');
UPDATE KETTLE_MASTER_DEV.DESIGNATION SET DESIGNATION_NAME = 'Cafe Assistant Manager', DESIGNATION_DESC = 'Cafe Assistant Manager' WHERE (`DESIGNATION_ID` = '1006');
UPDATE KETTLE_MASTER_DEV.DESIGNATION SET DESIGNATION_NAME = 'Zonal Manager' WHERE (`DESIGNATION_ID` = '1539');

INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Dispatcher', 'Dispatcher', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Sales Promoter', 'Sales Promoter', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Site Incharge', 'Site Incharge', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Shift Supervisor', 'Shift Supervisor', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'Y', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Cafe Handler', 'Cafe Handler', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Delivery Rider', 'Delivery Rider', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Senior Assistant Manager', 'Senior Assistant Manager', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', '2', 'N', 'Y');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Senior Shift Manager', 'Senior Shift Manager', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', '2', 'N', 'Y');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Shift Manager - Part Time', 'Shift Manager - Part Time', 'Y', 'Y', 'N', 'N', 'N', 'Y', 'Y', 'N', 'N', '2', 'N', 'Y');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Team Member - Part time', 'Team Member - Part time', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '2', 'N', 'N');
INSERT INTO KETTLE_MASTER_DEV.DESIGNATION (DESIGNATION_NAME, DESIGNATION_DESC, TRANSACTION_SYSTEM_ACCESS, SCM_SYSTEM_ACCESS, ANALYTICS_SYSTEM_ACCESS, ADMIN_SYSTEM_ACCESS, CLM_SYSTEM_ACCESS, CRM_SYSTEM_ACCESS, FORMS_SYSTEM_ACCESS, CHANNEL_PARTNER_ACCESS, APP_INSTALLER_ACCESS, MAX_ALLOCATED_UNIT, ATTENDANCE_ACCESS, KNOCK_APPLICATION_ACCESS) VALUES ('Senior Area Manager', 'Senior Area Manager', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'Y', 'N', 'N', '-1', 'N', 'N');


ALTER TABLE KETTLE_MASTER_DEV.EMPLOYEE_DETAIL
    CHANGE COLUMN EMP_GENDER EMP_GENDER VARCHAR(1) NULL ;

INSERT INTO KETTLE_MASTER_DEV.DEPARTMENT (DEPT_NAME, DEPT_DESC, BUSINESS_DIV_ID) VALUES ('Operations', 'Cafe Operations', '1000');

ALTER TABLE KETTLE_MASTER_DEV.PAYMENT_MODE
    ADD COLUMN `APPLICABLE_ON_DISCOUNTED_ORDERS` VARCHAR(1) NULL DEFAULT 'Y' AFTER `IS_EDITABLE`;

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Application Support', 'Access to Application Support', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADMN_AS_MGT', '5', 'MENU', 'SHOW', 'Admin -> Application Support', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Application Support'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_AS_MGT'), 'ACTIVE', '120063', '2022-01-03 12:00:00');


INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Order Status Management', 'Access to Order Status Management', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADMN_ORD_MGT', '5', 'SUBMENU', 'SHOW', 'Admin -> Application Support -> Order Management', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Order Status Management'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_ORD_MGT'), 'ACTIVE', '120063', '2022-01-03 12:00:00');

INSERT INTO KETTLE_MASTER_DEV.REF_LOOKUP (`RTL_ID`, `RL_CODE`, `RL_NAME`, `RL_SHORT_CODE`, `RL_STATUS`)
VALUES ((select RTL_ID from KETTLE_MASTER_DEV.REF_LOOKUP_TYPE where RTL_GROUP="DIMENSION" and RTL_CODE="Membership") , '45Days', '45 Days Membership', '45DM', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`USER_ROLE_DATA`
(`ROLE_NAME`, `ROLE_DESCRIPTION`, `ROLE_STATUS`, `APPLICATION_ID`)
VALUES ('Customer Upload Access', 'Access to upload Customer details', 'ACTIVE', '5');

INSERT INTO `KETTLE_MASTER_DEV`.`ACTION_DETAIL` (`ACTION_CODE`, `APPLICATION_ID`, `ACTION_TYPE`, `ACTION_CATEGORY`, `ACTION_DESCRIPTION`, `ACTION_STATUS`)
VALUES ('ADMN_CU_MGT', '5', 'MENU', 'SHOW', 'Admin -> Customer Upload Access', 'ACTIVE');

INSERT INTO `KETTLE_MASTER_DEV`.`ROLE_ACTION_MAPPING` (`ROLE_ID`, `ACTION_DETAIL_ID`, `MAPPING_STATUS`, `UPDATED_BY`, `LAST_UPDATE_TIME`)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Customer Upload Access'),(SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_CU_MGT'), 'ACTIVE', '120057', '2022-01-03 12:00:00');

ALTER TABLE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING
ADD COLUMN `SWIGGY_CLOUD_KITCHEN` VARCHAR(1) NULL DEFAULT 'N' AFTER `PARTNER_SOURCE_SYSTEM_ID`,
ADD COLUMN `LIVE_DATE` DATE NULL DEFAULT NULL AFTER `SWIGGY_CLOUD_KITCHEN`;

UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2014-10-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10000;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2012-11-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10001;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2014-04-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10002;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2013-10-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10005;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2014-10-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10006;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2015-10-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10009;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2015-11-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=10013;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2015-12-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12011;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-01-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12014;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-01-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12015;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-04-07' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12018;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-02-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12019;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-02-26' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12020;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-04-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12028;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-05-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12030;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-06-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=12031;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-08-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26005;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-11-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26012;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-12-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26015;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2016-12-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26016;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-03-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26018;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-02-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26019;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-04-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26021;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-10-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26022;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-05-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26023;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-07-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26025;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-11-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26027;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-07-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26028;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-10-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26029;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-08-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26034;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-08-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26036;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-08-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26037;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-08-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26038;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-09-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26039;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-11-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26043;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-10-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26044;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2017-10-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26048;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-08-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26052;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-01-18' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26053;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-01-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26055;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-11-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26056;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-03-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26058;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-04-07' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26059;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-06-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26060;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-08-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26066;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-04-11' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26071;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2018-12-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26076;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-03-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26081;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-02-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26084;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-05-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26087;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-04-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26090;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-04-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26091;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-05-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26093;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-06-18' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26094;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-06-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26095;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-08-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26104;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-07-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26105;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-08-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26106;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-08-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26107;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-10-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26108;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-09-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26109;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26110;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-11-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26111;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-09-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26112;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-10-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26113;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-11-29' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26114;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26117;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-11-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26121;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-10-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26124;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26125;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-11-29' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26126;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-02-02' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26127;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-01-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26128;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-01-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26134;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26137;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26138;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-28' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26139;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2019-12-29' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26140;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-01-11' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26141;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-02-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26143;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-03-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26144;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26145;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-03-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26146;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-03-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26150;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-04-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26151;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-03-28' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26152;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26153;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-10-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26155;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-12-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26158;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2020-11-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26162;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-02-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26164;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-01-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26179;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26181;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26182;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-26' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26183;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26184;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26186;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26188;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-26' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26196;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-11' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26197;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-06-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26200;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-03-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26201;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-02-02' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26202;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26203;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26207;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-04-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26211;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-05-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26214;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-05-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26218;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-05-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26219;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-06-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26220;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-06-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26221;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-07-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26222;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-06-29' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26224;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-07-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26225;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26226;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-07-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26227;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26228;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26229;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26230;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26232;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26234;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26236;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26237;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-09-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26238;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-09-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26239;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26241;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26242;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26243;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-08-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26244;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26245;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26246;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26247;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26248;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-02' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26251;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26252;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26253;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26256;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26257;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26260;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26261;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-10-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26262;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26265;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26266;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26267;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-18' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26268;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26269;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26271;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26272;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26277;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26278;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-11-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26279;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26280;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26281;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26288;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26289;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26290;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26292;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26294;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-09-07' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26295;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26296;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26300;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-26' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26301;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26303;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26304;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26305;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2021-12-29' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26307;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-11' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26308;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-02-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26309;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26310;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26311;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-05-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26312;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26313;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26314;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26315;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-02-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26316;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26317;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26318;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-26' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26320;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26321;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-09-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26326;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26328;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26329;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-01-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26330;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-07' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26331;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26332;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26336;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26337;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26338;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26340;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-10-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26341;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26347;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26348;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26349;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-02' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26350;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26351;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-02-28' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26352;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26356;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-11' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26358;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-05-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26359;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26362;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-21' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26365;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-20' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26366;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-07' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26367;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26368;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-03-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26369;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-04-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26371;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-05-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26375;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-09-03' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26376;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-04' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26377;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-05-25' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26378;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26379;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26380;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-05-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26381;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-16' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26383;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26384;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26394;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-13' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26398;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26403;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26404;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26405;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-24' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26411;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-06-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26413;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-09-17' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26431;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26434;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26435;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-10-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26440;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-07-31' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26442;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-08-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26453;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-09-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26454;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26458;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-18' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26459;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26466;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-27' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26472;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-05' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26497;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-08' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26507;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-14' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26508;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-30' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26510;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-11-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26522;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-12' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26524;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26529;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26534;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26535;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26536;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26538;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26539;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26540;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26545;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-03-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26546;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2022-12-22' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26547;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-23' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26552;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26553;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-10' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26554;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-09' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26555;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-01-19' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26558;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-01' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26559;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-02-15' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26561;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING SET LIVE_DATE='2023-03-06' where PARTNER_ID IN (3,6) and BRAND_ID=1 and MAPPING_STATUS='ACTIVE' and UNIT_ID=26562;

UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26310 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26306 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26312 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26313 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26314 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26315 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26379 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26380 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26383 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26381 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26459 and PARTNER_ID=6;
UPDATE KETTLE_MASTER_DEV.UNIT_PARTNER_BRAND_MAPPING  set SWIGGY_CLOUD_KITCHEN='Y' where UNIT_ID=26472 and PARTNER_ID=6;


CREATE TABLE KETTLE_DEV.ORDER_COMMISSION(
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `PARTNER_ORDER_ID` varchar(255) NOT NULL,
  `KETTLE_ORDER_ID` int(11) DEFAULT NULL,
  `UNIT_ID` int(11) DEFAULT NULL,
  `SUB_TOTAL` decimal(19,2) DEFAULT NULL,
  `PACKAGING_CHARGES` decimal(19,2) DEFAULT NULL,
  `DISCOUNT` decimal(19,2) DEFAULT NULL,
  `NET_AMOUNT` decimal(19,2) DEFAULT NULL,
  `AOV` decimal(19,2) DEFAULT NULL,
  `COMMISSION_RATE` double DEFAULT NULL,
  `EXTRA_COMMISSION_RATE` double DEFAULT NULL,
  `COMMISSION_AMOUNT` decimal(19,2) DEFAULT NULL,
  `GST_RATE` double DEFAULT NULL,
  `FINAL_COMMISSION_AMOUNT` decimal(19,2) DEFAULT NULL,
  `BRAND` int(11) DEFAULT NULL,
  `PARTNER_NAME` varchar(255) DEFAULT NULL,
  `UNIT_AGE` bigint(20) DEFAULT NULL,
  `UNIT_LIVE_DATE` datetime DEFAULT NULL,
  `SWIGGY_CLOUD_KITCHEN` varchar(255) DEFAULT NULL,
  `RULE_PARAMS` varchar(255) DEFAULT NULL
  PRIMARY KEY (`id`)
) ENGINE=MyISAM AUTO_INCREMENT=3483 DEFAULT CHARSET=latin1;
CREATE INDEX ORDER_COMMISSION_KETTLE_ORDER_ID ON KETTLE_DEV.ORDER_COMMISSION(KETTLE_ORDER_ID) USING BTREE;
CREATE INDEX ORDER_COMMISSION_PARTNER_ORDER_ID ON KETTLE_DEV.ORDER_COMMISSION(PARTNER_ORDER_ID) USING BTREE;
CREATE INDEX ORDER_COMMISSION_UNIT_ID ON KETTLE_DEV.ORDER_COMMISSION(UNIT_ID) USING BTREE;
CREATE INDEX ORDER_COMMISSION_ORDER_DATE ON KETTLE_DEV.ORDER_COMMISSION(ORDER_DATE) USING BTREE;
ALTER TABLE KETTLE_DEV.ORDER_COMMISSION ADD COLUMN ORDER_DATE TIMESTAMP NULL;


INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ACTION_MAPPING_ID, ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME) VALUES ('1465', '6', '338', 'ACTIVE', '120063', '2023-03-24 16:30:00');
INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_DETAIL_ID, ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS) VALUES ('338', 'ADMN_UPBM_EDIT_LD', '5', 'ACTION', 'EDIT', 'ADMIN->UNIT_PARTNER_BRAND_MAPPING->EDIT_LIVE_DATE', 'ACTIVE');






ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN LAUNCH_UNIT_ID INT(11);
CREATE INDEX CAMPAIGN_DETAIL_DATA_LAUNCH_UNIT_ID ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(LAUNCH_UNIT_ID) USING BTREE;

ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA ADD COLUMN CAFE_LAUNCH_DATE TIMESTAMP NULL DEFAULT NULL;
CREATE INDEX CAMPAIGN_DETAIL_DATA_CAFE_LAUNCH_DATE ON KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA(CAFE_LAUNCH_DATE) USING BTREE;

INSERT INTO KETTLE_MASTER_DEV.ACTION_DETAIL (ACTION_CODE, APPLICATION_ID, ACTION_TYPE, ACTION_CATEGORY, ACTION_DESCRIPTION, ACTION_STATUS)
VALUES ('ADMN_RCP_MED', '5', 'SUBMENU', 'SHOW', 'Admin -> Add Recipe Media -> show', 'ACTIVE');
INSERT INTO KETTLE_MASTER_DEV.USER_ROLE_DATA(ROLE_NAME, ROLE_DESCRIPTION, ROLE_STATUS, APPLICATION_ID)
VALUES ('Kettle Recipe Management Add Recipe Media', 'Add Recipe Media access to kettle admin', 'ACTIVE', '5');
INSERT INTO KETTLE_MASTER_DEV.ROLE_ACTION_MAPPING (ROLE_ID, ACTION_DETAIL_ID, MAPPING_STATUS, UPDATED_BY, LAST_UPDATE_TIME)
VALUES((SELECT ROLE_ID FROM KETTLE_MASTER_DEV.USER_ROLE_DATA WHERE ROLE_NAME = 'Kettle Recipe Management Add Recipe Media'),
       (SELECT ACTION_DETAIL_ID FROM KETTLE_MASTER_DEV.ACTION_DETAIL WHERE ACTION_CODE = 'ADMN_RCP_MED'), 'ACTIVE', '120063', '2023-03-28 00:00:00');

INSERT INTO KETTLE_MASTER_DEV.`CACHE_REFERENCE_METADATA` (`CACHE_ID`, `REFERENCE_TYPE`, `REFERENCE_VALUE`, `CACHE_STATUS`, `ADDED_BY`, `UPDATED_BY`) VALUES ('3', 'DIRECT_PURCHASE_OF_GIFT_CARD', 'N', 'ACTIVE', 'SYSTEM', '100026');


ALTER TABLE KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA
    ADD COLUMN `CRM_APP_BANNER` VARCHAR(100) NULL AFTER `CAFE_LAUNCH_DATE`;
