<!DOCTYPE html>
<html ng-app="testApp">
<head>
    <title>Back Button Test</title>
    <script src="https://ajax.googleapis.com/ajax/libs/angularjs/1.6.9/angular.min.js"></script>
    <style>
        .section { padding: 20px; margin: 10px; border: 2px solid #ccc; }
        .employee-section { background: #e8f5e8; }
        .mapping-section { background: #e8f0ff; }
        .step { display: inline-block; padding: 10px; margin: 5px; background: #f0f0f0; }
        .step.active { background: #3498db; color: white; }
        button { padding: 10px 20px; margin: 10px; cursor: pointer; }
    </style>
</head>
<body ng-controller="TestController">
    <h1>Back Button Test</h1>
    
    <div>Current Step: {{currentStep}}</div>
    
    <div class="step" ng-class="{active: currentStep >= 1}">Step 1: Employee Selection</div>
    <div class="step" ng-class="{active: currentStep >= 2}">Step 2: Mapping Configuration</div>
    
    <!-- Employee Selection Section -->
    <div class="section employee-section" ng-show="currentStep === 1">
        <h2>Employee Selection</h2>
        <p>This is the employee selection section.</p>
        <button ng-click="goToMapping()">Next Step →</button>
    </div>
    
    <!-- Mapping Section -->
    <div class="section mapping-section" ng-show="currentStep === 2">
        <h2>Mapping Configuration</h2>
        <p>This is the mapping configuration section.</p>
        <button ng-click="goBackToEmployeeSelection()">← Back to Employee Selection</button>
    </div>
    
    <script>
        angular.module('testApp', [])
        .controller('TestController', function($scope, $timeout) {
            $scope.currentStep = 1;
            
            $scope.goToMapping = function() {
                console.log('Going to mapping - Current step before:', $scope.currentStep);
                $scope.currentStep = 2;
                console.log('Step changed to:', $scope.currentStep);
            };
            
            $scope.goBackToEmployeeSelection = function() {
                console.log('🔙 Back button clicked - Current step before:', $scope.currentStep);
                $scope.currentStep = 1;
                console.log('🔙 Step changed to:', $scope.currentStep);
                window.scrollTo(0, 0);
            };
        });
    </script>
</body>
</html>
