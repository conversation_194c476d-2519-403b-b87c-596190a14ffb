adminapp.controller("employeeAttendanceMappingCtrl", function($scope, $http, $location, $window, $rootScope, $timeout, AuthService, AppUtil) {

    $scope.init = function() {
        $rootScope.showFullScreenLoader = true;

        // Initialize scope variables
        $scope.employees = [];
        $scope.filteredEmployees = [];
        $scope.paginatedEmployees = [];
        $scope.selectedEmployees = [];
        $scope.units = [];
        $scope.allUnits = [];
        $scope.filteredUnits = [];
        $scope.departments = [];
        $scope.regions = [];
        $scope.cities = [];
        $scope.categories = [];
        $scope.managers = [];
        $scope.designations = [];
        $scope.statuses = [];

        // Pagination variables
        $scope.currentPage = 1;
        $scope.itemsPerPage = 20;
        $scope.totalPages = 1;

        // Searchable dropdown variables
        $scope.categorySearch = '';
        $scope.citySearch = '';
        $scope.regionSearch = '';
        $scope.unitSearch = '';
        $scope.unitZoneSearch = '';
        $scope.filteredCategories = [];
        $scope.filteredUnitZones = [];
        $scope.filteredCities = [];
        $scope.filteredRegions = [];
        $scope.filteredUnitsDropdown = [];
        $scope.showCategoryDropdown = false;
        $scope.showUnitZoneDropdown = false;
        $scope.showCityDropdown = false;
        $scope.showRegionDropdown = false;
        $scope.showUnitDropdown = false;
        $scope.unitZones = [];

        // Filter variables
        $scope.filters = {
            designation: 'All Designations',
            status: 'All Status',
            department: 'All Departments'
        };

        // Unit mapping filters
        $scope.unitFilters = {
            category: null,
            city: null,
            region: null,
            selectedUnitId: null,
            selectedUnitName: null,
            unitZone: null
        };

        // Search variables
        $scope.searchText = '';

        // Step management
        $scope.currentStep = 1;
        $scope.mappings = [];
        $scope.selectedMappingType = 'unit';
        $scope.eligibilityType = 'attendance';

        // Load all data
        $scope.loadEmployees();
        $scope.loadAllUnits();
        $scope.loadDepartments();
        $scope.loadDesignations();
        $scope.loadRegions();
        $scope.loadCities();
        $scope.loadCategories();
        $scope.loadManagers();
        $scope.loadUnitZones();
    };

    // Load Employees
    $scope.loadEmployees = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.users
        }).then(function success(response) {
            $scope.employees = response.data || [];
            $scope.filteredEmployees = $scope.employees.slice();

            // Extract unique statuses from employee data
            $scope.extractStatuses();
        }, function error(response) {
            console.log("Error loading employees:", response);
            $scope.employees = [];
            $scope.filteredEmployees = [];
        });
    };

    // Load All Units
    $scope.loadAllUnits = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnitsList
        }).then(function success(response) {
            $scope.allUnits = response.data || [];
            $scope.filteredUnits = $scope.allUnits.slice();
            console.log('All units loaded:', $scope.allUnits);
        }, function error(response) {
            console.log("Error loading units:", response);
            $scope.allUnits = [];
            $scope.filteredUnits = [];
        });
    };

    // Load Departments
    $scope.loadDepartments = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.departments
        }).then(function success(response) {
            $scope.departments = response.data || [];
        }, function error(response) {
            console.log("Error loading departments:", response);
            $scope.departments = [];
        });
    };

    // Load Designations
    $scope.loadDesignations = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.designations
        }).then(function success(response) {
            $scope.designations = response.data || [];
        }, function error(response) {
            console.log("Error loading designations:", response);
            $scope.designations = [];
        });
    };

    // Load Regions
    $scope.loadRegions = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.regions
        }).then(function success(response) {
            $scope.regions = response.data || [];
        }, function error(response) {
            console.log("Error loading regions:", response);
            $scope.regions = [];
        });
    };

    // Load Cities
    $scope.loadCities = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.cities
        }).then(function success(response) {
            $scope.cities = response.data || [];
            $rootScope.showFullScreenLoader = false;
        }, function error(response) {
            console.log("Error loading cities:", response);
            $scope.cities = [];
            $rootScope.showFullScreenLoader = false;
        });
    };

    // Load Managers
    $scope.loadManagers = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.userManagement.managers
        }).then(function success(response) {
            $scope.managers = response.data || [];
        }, function error(response) {
            console.log("Error loading managers:", response);
            $scope.managers = [];
        });
    };

    // Load All Units
    $scope.loadAllUnits = function() {
        $http({
            method: 'GET',
            url: AppUtil.restUrls.unitMetaData.allUnitsList
        }).then(function success(response) {
            $scope.allUnits = response.data || [];
            $scope.filteredUnits = $scope.allUnits.slice();
            console.log('All units loaded:', $scope.allUnits);
        }, function error(response) {
            console.log("Error loading units:", response);
            $scope.allUnits = [];
            $scope.filteredUnits = [];
        });
    };

    // Load Categories
    $scope.loadCategories = function() {
        // Define common unit categories
        $scope.categories = [
            { name: 'CAFE', value: 'CAFE' },
            { name: 'WAREHOUSE', value: 'WAREHOUSE' },
            { name: 'KITCHEN', value: 'KITCHEN' },
            { name: 'OFFICE', value: 'OFFICE' },
            { name: 'DELIVERY', value: 'DELIVERY' },
            { name: 'COD', value: 'COD' },
            { name: 'TAKE_AWAY', value: 'TAKE_AWAY' }
        ];
    };

    $scope.loadUnitZones = function() {
        // defining unit zones
        $scope.unitZones     = [
            { name: 'NORTH', value: 'NORTH' },
            { name: 'SOUTH', value: 'SOUTH' },
            { name: 'WEST', value: 'WEST' },
            { name: 'EAST', value: 'EAST' }
        ];
    }

    // Filter units based on selected criteria
    $scope.filterUnits = function() {
        $scope.filteredUnits = $scope.allUnits.filter(function(unit) {
            var matchesCategory = !$scope.unitFilters.category || unit.category === $scope.unitFilters.category;
            var matchesCity = !$scope.unitFilters.city || (unit.city && unit.city.toLowerCase().includes($scope.unitFilters.city.toLowerCase()));
            var matchesRegion = !$scope.unitFilters.region || (unit.region && unit.region.toLowerCase().includes($scope.unitFilters.region.toLowerCase()));
            var matchesUnitZone = !$scope.unitFilters.unitZone || (unit.unitZone && unit.unitZone.toLowerCase().includes($scope.unitFilters.unitZone.toLowerCase()));

            return matchesCategory && matchesCity && matchesRegion && matchesUnitZone;
        });

        // Reset unit selections when filters change
        $scope.unitFilters.selectedUnitId = null;
        $scope.unitFilters.selectedUnitName = null;

        console.log('Filtered units:', $scope.filteredUnits);
    };

    // Handle unit filter changes
    $scope.onUnitFilterChange = function() {
        $scope.filterUnits();
    };

    // Clear all unit filters
    $scope.clearUnitFilters = function() {
        $scope.unitFilters = {
            category: null,
            city: null,
            region: null,
            selectedUnitId: null,
            selectedUnitName: null,
            unitZone: null
        };

        // Clear search inputs
        $scope.categorySearch = '';
        $scope.citySearch = '';
        $scope.regionSearch = '';
        $scope.unitSearch = '';
        $scope.unitZoneSearch = '';

        // Reset filtered arrays
        if ($scope.categories) $scope.filteredCategories = $scope.categories.slice();
        if ($scope.unitZones) $scope.filteredUnitZones = $scope.unitZones.slice();
        if ($scope.cities) $scope.filteredCities = $scope.cities.slice();
        if ($scope.regions) $scope.filteredRegions = $scope.regions.slice();
        $scope.filteredUnits = $scope.allUnits.slice();
        $scope.filteredUnitsDropdown = $scope.allUnits.slice();
    };

    // Sync unit selection between ID and Name dropdowns
    $scope.syncUnitSelection = function(changedField) {
        if (changedField === 'id' && $scope.unitFilters.selectedUnitId) {
            // Find the unit by ID and set the name
            var selectedUnit = $scope.filteredUnits.find(function(unit) {
                return unit.id == $scope.unitFilters.selectedUnitId;
            });
            if (selectedUnit) {
                $scope.unitFilters.selectedUnitName = selectedUnit.name;
            }
        } else if (changedField === 'name' && $scope.unitFilters.selectedUnitName) {
            // Find the unit by name and set the ID
            var selectedUnit = $scope.filteredUnits.find(function(unit) {
                return unit.name === $scope.unitFilters.selectedUnitName;
            });
            if (selectedUnit) {
                $scope.unitFilters.selectedUnitId = selectedUnit.id;
            }
        }
    };

    // Search and Filter Functions
    $scope.searchEmployees = function() {
        $scope.filteredEmployees = $scope.employees.filter(function(employee) {
            var empDesignation = employee.designation || employee.designationName || '';
            var empDepartment = employee.department || employee.departmentName || '';

            var matchesDesignation = $scope.filters.designation === 'All Designations' ||
                                    empDesignation === $scope.filters.designation;
            var matchesStatus = $scope.filters.status === 'All Status' ||
                              (employee.status && employee.status === $scope.filters.status);
            var matchesDepartment = $scope.filters.department === 'All Departments' ||
                                  empDepartment === $scope.filters.department;
            var matchesSearch = !$scope.searchText ||
                              (employee.name && employee.name.toLowerCase().includes($scope.searchText.toLowerCase())) ||
                              (employee.employeeId && employee.employeeId.toString().includes($scope.searchText)) ||
                              (employee.id && employee.id.toString().includes($scope.searchText));

            return matchesDesignation && matchesStatus && matchesDepartment && matchesSearch;
        });

        // Reset to first page when search changes
        $scope.currentPage = 1;
        $scope.updatePagination();
    };

    // Pagination Functions
    $scope.updatePagination = function() {
        $scope.totalPages = Math.ceil($scope.filteredEmployees.length / $scope.itemsPerPage);
        if ($scope.totalPages === 0) $scope.totalPages = 1;

        var startIndex = ($scope.currentPage - 1) * $scope.itemsPerPage;
        var endIndex = startIndex + $scope.itemsPerPage;
        $scope.paginatedEmployees = $scope.filteredEmployees.slice(startIndex, endIndex);
    };

    $scope.goToPage = function(page) {
        if (page >= 1 && page <= $scope.totalPages) {
            $scope.currentPage = page;
            $scope.updatePagination();
        }
    };

    $scope.getVisiblePages = function() {
        var pages = [];
        var start = Math.max(1, $scope.currentPage - 1);
        var end = Math.min($scope.totalPages, start + 2);

        // Adjust start if we're near the end
        if (end - start < 2) {
            start = Math.max(1, end - 2);
        }

        for (var i = start; i <= end; i++) {
            pages.push(i);
        }

        // Add ellipsis and last page if needed
        if (end < $scope.totalPages) {
            if (end < $scope.totalPages - 1) {
                pages.push('...');
            }
            pages.push($scope.totalPages);
        }

        return pages;
    };

    $scope.getStartIndex = function() {
        return ($scope.currentPage - 1) * $scope.itemsPerPage + 1;
    };

    $scope.getEndIndex = function() {
        return Math.min($scope.currentPage * $scope.itemsPerPage, $scope.filteredEmployees.length);
    };

    // Employee Selection Functions
    $scope.toggleEmployeeSelection = function(employee) {
        var index = $scope.selectedEmployees.findIndex(function(emp) {
            return emp.id === employee.id;
        });

        if (index > -1) {
            $scope.selectedEmployees.splice(index, 1);
        } else {
            $scope.selectedEmployees.push(employee);
        }
        $scope.updateSelectedCount();
    };

    $scope.isEmployeeSelected = function(employee) {
        return $scope.selectedEmployees.some(function(emp) {
            return emp.id === employee.id;
        });
    };

    $scope.toggleSelectAll = function() {
        if ($scope.selectedEmployees.length === $scope.paginatedEmployees.length && $scope.paginatedEmployees.length > 0) {
            // Deselect all employees on current page
            $scope.paginatedEmployees.forEach(function(employee) {
                var index = $scope.selectedEmployees.findIndex(function(emp) {
                    return emp.id === employee.id;
                });
                if (index > -1) {
                    $scope.selectedEmployees.splice(index, 1);
                }
            });
        } else {
            // Select all employees on current page
            $scope.paginatedEmployees.forEach(function(employee) {
                var exists = $scope.selectedEmployees.some(function(emp) {
                    return emp.id === employee.id;
                });
                if (!exists) {
                    $scope.selectedEmployees.push(employee);
                }
            });
        }
        $scope.updateSelectedCount();
    };

    $scope.updateSelectedCount = function() {
        $scope.selectedCount = $scope.selectedEmployees.length;
    };

    // Step Navigation Functions
    $scope.goToMapping = function() {
        if ($scope.selectedEmployees.length === 0) {
            alert('Please select at least one employee to proceed.');
            return;
        }
        $scope.currentStep = 2;
        $scope.updateStepIndicator();
    };

    $scope.goBackToEmployeeSelection = function () {
        $scope.currentStep = 1;
        $scope.updateStepIndicator();
        $timeout(() => {
            window.scrollTo(0, 0);
        });
    };


    $scope.updateStepIndicator = function() {
        // Update step indicator classes
        var steps = document.querySelectorAll('.step');
        steps.forEach(function(step, index) {
            if (index + 1 <= $scope.currentStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        });

        // Show/hide sections
        $timeout(function () {
            var employeeSection = document.getElementById('employeeSelection');
            var mappingSection = document.getElementById('mappingSection');

            if ($scope.currentStep === 2) {
                employeeSection.style.display = 'none';
                mappingSection.style.display = 'block';
            } else {
                employeeSection.style.display = 'block';
                mappingSection.style.display = 'none';
            }
        });
    };

    // Mapping Functions
    $scope.switchEligibilityTab = function(type) {
        $scope.eligibilityType = type;
        // Update tab classes
        var tabs = document.querySelectorAll('.tab-btn');
        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');
    };

    $scope.switchMappingTab = function(type) {
        $scope.selectedMappingType = type;
        // Update tab classes
        var tabs = document.querySelectorAll('.mapping-tab');
        tabs.forEach(function(tab) {
            tab.classList.remove('active');
        });
        event.target.classList.add('active');

        // Update form title
        var title = document.querySelector('.search-title');
        if (title) {
            switch(type) {
                case 'unit':
                    title.textContent = '🏢 Configure Unit Mapping';
                    break;
                case 'city':
                    title.textContent = '🏙️ Configure City Mapping';
                    break;
                case 'region':
                    title.textContent = '🌍 Configure Region Mapping';
                    break;
            }
        }
    };

    $scope.toggleIndividualSelection = function() {
        var panel = document.getElementById('individualSelectionPanel');
        var select = document.getElementById('applyToSelect');

        if (select.value === 'individual') {
            panel.classList.remove('hidden');
        } else {
            panel.classList.add('hidden');
        }
    };

    $scope.addMapping = function() {
        var applyTo = document.getElementById('applyToSelect').value;

        // For unit mapping, use the selected unit from dropdowns
        var mappingValue, mappingName;
        if ($scope.selectedMappingType === 'unit') {
            if (!$scope.unitFilters.selectedUnitId || !$scope.unitFilters.selectedUnitName) {
                alert('Please select both Unit ID and Unit Name.');
                return;
            }
            mappingValue = $scope.unitFilters.selectedUnitId;
            mappingName = $scope.unitFilters.selectedUnitName;
        } else {
            // For city and region mapping, use text inputs
            mappingValue = document.getElementById('mappingValue').value;
            mappingName = document.getElementById('mappingName').value;

            if (!mappingValue || !mappingName) {
                alert('Please fill in all required fields.');
                return;
            }
        }

        var mapping = {
            type: $scope.selectedMappingType,
            value: mappingValue,
            name: mappingName,
            applyTo: applyTo,
            employees: applyTo === 'all' ? $scope.selectedEmployees.slice() : [],
            eligibilityType: $scope.eligibilityType
        };

        $scope.mappings.push(mapping);
        $scope.updateMappingsList();

        // Clear form
        if ($scope.selectedMappingType === 'unit') {
            $scope.unitFilters.selectedUnitId = null;
            $scope.unitFilters.selectedUnitName = null;
        } else {
            document.getElementById('mappingValue').value = '';
            document.getElementById('mappingName').value = '';
        }
        document.getElementById('applyToSelect').value = 'all';
        $scope.toggleIndividualSelection();
    };

    $scope.updateMappingsList = function() {
        // Update mappings count
        var countElement = document.querySelector('.mappings-count');
        if (countElement) {
            countElement.textContent = $scope.mappings.length + ' mappings';
        }
    };

    $scope.removeMapping = function(index) {
        $scope.mappings.splice(index, 1);
        $scope.updateMappingsList();
    };

    $scope.filterMappings = function() {
        var searchText = document.getElementById('mappingSearch').value.toLowerCase();
        var filterType = document.getElementById('mappingFilter').value;

        var mappingItems = document.querySelectorAll('.mapping-item');
        mappingItems.forEach(function(item) {
            var itemType = item.getAttribute('data-type');
            var itemValue = item.getAttribute('data-value').toLowerCase();
            var itemEmployees = item.getAttribute('data-employees').toLowerCase();

            var matchesSearch = !searchText || itemValue.includes(searchText) || itemEmployees.includes(searchText);
            var matchesFilter = filterType === 'all' || itemType === filterType;

            if (matchesSearch && matchesFilter) {
                item.style.display = 'block';
            } else {
                item.style.display = 'none';
            }
        });
    };

    $scope.saveMappings = function() {
        if ($scope.mappings.length === 0) {
            alert('Please add at least one mapping before saving.');
            return;
        }
        AppUtil.mySuccessalert('Mappings saved successfully!');
    };

    // Helper function to get employee names for display
    $scope.getEmployeeNames = function(employees) {
        if (!employees || employees.length === 0) {
            return 'No employees';
        }
        return employees.map(function(emp) {
            return emp.name || emp.firstName + ' ' + emp.lastName;
        }).join(', ');
    };

    // Edit mapping function
    $scope.editMapping = function(index) {
        // Implementation for editing mappings
        console.log('Edit mapping at index:', index);
    };

    // Extract unique statuses from employee data
    $scope.extractStatuses = function() {
        var statuses = [];

        $scope.employees.forEach(function(employee) {
            if (employee.status && statuses.indexOf(employee.status) === -1) {
                statuses.push(employee.status);
            }
        });

        $scope.statuses = statuses.sort();

        console.log('Statuses extracted:', $scope.statuses);
    };

    // Searchable Dropdown Functions
    $scope.filterCategories = function() {
        $scope.filteredCategories = $scope.categories.filter(function(category) {
            return !$scope.categorySearch || category.name.toLowerCase().includes($scope.categorySearch.toLowerCase());
        });
    };

    $scope.filterUnitZones = function() {
        $scope.filteredUnitZones = $scope.unitZones.filter(function(unitZone) {
            return !$scope.unitZoneSearch || unitZone.name.toLowerCase().includes($scope.unitZoneSearch.toLowerCase());
        });
    };

    $scope.filterCities = function() {
        $scope.filteredCities = $scope.cities.filter(function(city) {
            var cityName = city.name || city;
            return !$scope.citySearch || cityName.toLowerCase().includes($scope.citySearch.toLowerCase());
        });
    };

    $scope.filterRegions = function() {
        $scope.filteredRegions = $scope.regions.filter(function(region) {
            var regionName = region.name || region;
            return !$scope.regionSearch || regionName.toLowerCase().includes($scope.regionSearch.toLowerCase());
        });
    };

    $scope.filterUnitsDropdown = function() {
        $scope.filteredUnitsDropdown = $scope.filteredUnits.filter(function(unit) {
            return !$scope.unitSearch ||
                   unit.name.toLowerCase().includes($scope.unitSearch.toLowerCase()) ||
                   unit.id.toString().includes($scope.unitSearch);
        });
    };

    // Dropdown selection functions
    $scope.selectCategory = function(category) {
        $scope.categorySearch = category.name;
        $scope.unitFilters.category = category.value;
        $scope.showCategoryDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectUnitZone = function(unitZone) {
        $scope.unitZoneSearch = unitZone.name;
        $scope.unitFilters.unitZone = unitZone.value;
        $scope.showUnitZoneDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectCity = function(city) {
        $scope.citySearch = city.name || city;
        $scope.unitFilters.city = city.name || city;
        $scope.showCityDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectRegion = function(region) {
        $scope.regionSearch = region.name || region;
        $scope.unitFilters.region = region.name || region;
        $scope.showRegionDropdown = false;
        $scope.onUnitFilterChange();
    };

    $scope.selectUnit = function(unit) {
        $scope.unitSearch = unit.id + ' - ' + unit.name;
        $scope.unitFilters.selectedUnitId = unit.id;
        $scope.unitFilters.selectedUnitName = unit.name;
        $scope.showUnitDropdown = false;
    };

    // Dropdown hide functions with delay
    $scope.hideCategoryDropdown = function() {
        $timeout(function() {
            $scope.showCategoryDropdown = false;
        }, 200);
    };

    $scope.hideUnitZoneDropdown = function() {
        $timeout(function() {
            $scope.showUnitZoneDropdown = false;
        }, 200);
    };

    $scope.hideCityDropdown = function() {
        $timeout(function() {
            $scope.showCityDropdown = false;
        }, 200);
    };

    $scope.hideRegionDropdown = function() {
        $timeout(function() {
            $scope.showRegionDropdown = false;
        }, 200);
    };

    $scope.hideUnitDropdown = function() {
        $timeout(function() {
            $scope.showUnitDropdown = false;
        }, 200);
    };

    // Initialize filtered arrays when data loads
    $scope.$watch('categories', function(newVal) {
        if (newVal) {
            $scope.filteredCategories = newVal.slice();
        }
    });

    $scope.$watch('unitZones', function(newVal) {
        if (newVal) {
            $scope.filteredUnitZones = newVal.slice();
        }
    });

    $scope.$watch('cities', function(newVal) {
        if (newVal) {
            $scope.filteredCities = newVal.slice();
        }
    });

    $scope.$watch('regions', function(newVal) {
        if (newVal) {
            $scope.filteredRegions = newVal.slice();
        }
    });

    $scope.$watch('filteredUnits', function(newVal) {
        if (newVal) {
            $scope.filteredUnitsDropdown = newVal.slice();
        }
    });

    // Update pagination when employees load
    $scope.$watch('employees', function(newVal) {
        if (newVal && newVal.length > 0) {
            $scope.searchEmployees();
        }
    });

    // Initialize the controller
    $scope.init();
});
