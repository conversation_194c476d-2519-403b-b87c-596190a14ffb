* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            padding: 0 30px;
        }

        .step {
            display: flex;
            align-items: center;
            margin: 0 20px;
            position: relative;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: #666;
            transition: all 0.3s ease;
        }

        .step.active .step-number {
            background: #3498db;
            color: white;
            transform: scale(1.1);
        }

        .step.completed .step-number {
            background: #27ae60;
            color: white;
        }

        .step-label {
            margin-left: 10px;
            font-weight: 600;
            color: #666;
        }

        .step.active .step-label {
            color: #3498db;
        }

        .step-connector {
            width: 60px;
            height: 2px;
            background: #e0e0e0;
            margin: 0 10px;
        }

        .main-content {
            padding: 30px;
        }

        .search-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 25px;
            border-left: 4px solid #3498db;
        }

        .search-title {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: #2c3e50;
        }

        .filters-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-bottom: 16px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group label {
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
        }

        .form-control {

            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .search-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 24px;
            border-radius: 6px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            align-self: flex-end;
        }

        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .employee-list {
            background: white;
            border-radius: 12px;
            border: 1px solid #e0e0e0;
            overflow: hidden;
        }

        .list-header {
            background: #f1f2f6;
            padding: 12px 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .list-title {
            font-weight: 600;
            color: #2c3e50;
        }

        .select-all {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
            color: #3498db;
        }

        .employee-item {
            padding: 12px 16px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 12px;
            transition: all 0.3s ease;
        }

        .employee-item:hover {
            background: #f8f9fa;
        }

        .employee-checkbox {
            width: 18px;
            height: 18px;
            accent-color: #3498db;
        }

        .employee-info {
            flex: 1;
        }

        .employee-name {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .employee-details {
            color: #666;
            font-size: 14px;
        }

        .employee-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .selected-summary {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 20px;
            border-radius: 15px;
            margin: 20px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .selected-count {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .next-btn {
            background: white;
            color: #27ae60;
            border: 2px solid white;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .next-btn:hover {
            background: rgba(255,255,255,0.1);
            transform: translateX(5px);
        }

        .mapping-section {
            display: none;
        }

        .mapping-section.active {
            display: block;
        }

        .selected-employees {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            border-left: 5px solid #2196f3;
        }

        .employees-chips {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }

        .employee-chip {
            background: #2196f3;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .chip-remove {
            background: rgba(255,255,255,0.3);
            border: none;
            color: white;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            cursor: pointer;
            font-size: 12px;
        }

        .eligibility-tabs {
            display: flex;
            background: #f1f2f6;
            border-radius: 15px;
            padding: 5px;
            margin-bottom: 30px;
        }

        .tab-btn {
            flex: 1;
            padding: 15px;
            border: none;
            background: transparent;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .tab-btn.active {
            background: white;
            color: #3498db;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .mapping-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 25px;
        }

        .mapping-tab {
            flex: 1;
            padding: 12px;
            border: none;
            background: transparent;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            color: #666;
        }

        .mapping-tab.active {
            background: #3498db;
            color: white;
        }

        .mapping-form {
            background: white;
            border: 2px solid #e0e0e0;
            border-radius: 15px;
            padding: 25px;
        }

        .form-row {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .add-mapping-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 15px;
        }

        .add-mapping-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        }

        .current-mappings {
            margin-top: 30px;
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
        }

        .mappings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .mappings-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .mappings-count {
            background: #3498db;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
        }

        .mappings-actions {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .mapping-search {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 20px;
            font-size: 14px;
            width: 200px;
            transition: all 0.3s ease;
        }

        .mapping-search:focus {
            outline: none;
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
        }

        .mapping-filter {
            padding: 8px 12px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            background: white;
            cursor: pointer;
        }

        .mappings-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .mapping-item {
            background: white;
            border-radius: 12px;
            padding: 18px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .mapping-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .mapping-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            text-align: center;
            min-width: 60px;
        }

        .unit-badge {
            background: #e3f2fd;
            color: #1976d2;
        }

        .city-badge {
            background: #f3e5f5;
            color: #7b1fa2;
        }

        .region-badge {
            background: #e8f5e8;
            color: #388e3c;
        }

        .mapping-info {
            flex: 1;
        }

        .mapping-value {
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 6px;
        }

        .mapping-employees {
            font-size: 14px;
            color: #666;
        }

        .employee-count {
            font-weight: 500;
            color: #3498db;
        }

        .employee-names {
            color: #666;
        }

        .mapping-actions-btn {
            display: flex;
            gap: 8px;
        }

        .edit-mapping, .remove-mapping {
            background: none;
            border: none;
            padding: 8px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            transition: all 0.3s ease;
        }

        .edit-mapping {
            color: #3498db;
        }

        .edit-mapping:hover {
            background: #e3f2fd;
        }

        .remove-mapping {
            color: #e74c3c;
        }

        .remove-mapping:hover {
            background: #ffebee;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-text {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .empty-subtext {
            font-size: 14px;
            opacity: 0.7;
        }

        .action-buttons {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding: 20px 0;
            border-top: 2px solid #e0e0e0;
        }

        .back-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bulk-actions {
            display: flex;
            gap: 15px;
        }

        .bulk-btn {
            background: #17a2b8;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .bulk-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }

        .save-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .hidden {
            display: none !important;
        }

        .individual-selection-panel {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
        }

        .individual-employees {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin-top: 15px;
        }

        .individual-employee-item {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .individual-employee-item:hover {
            border-color: #3498db;
            background: #f8f9fa;
        }

        .individual-employee-item.selected {
            border-color: #3498db;
            background: #e3f2fd;
        }

        .individual-checkbox {
            width: 16px;
            height: 16px;
            accent-color: #3498db;
        }

        .individual-emp-info {
            flex: 1;
        }

        .individual-emp-name {
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
        }

        .individual-emp-id {
            color: #666;
            font-size: 12px;
            margin-top: 2px;
        }

        .selection-summary {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 12px;
            margin-top: 15px;
            color: #856404;
            font-size: 14px;
            display: none;
        }

        .selection-summary.show {
            display: block;
        }

        @media (max-width: 768px) {
            .filters-row {
                grid-template-columns: 1fr;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .step-indicator {
                flex-direction: column;
                align-items: center;
            }

            .step {
                margin: 10px 0;
            }

            .step-connector {
                display: none;
            }

            .action-buttons {
                flex-direction: column;
                gap: 15px;
            }

            .bulk-actions {
                flex-direction: column;
                width: 100%;
            }
        }

        /* Additional styles for Angular integration */
        .employee-chip {
            display: inline-block;
            background: #e3f2fd;
            color: #1976d2;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 12px;
            font-size: 0.85rem;
            border: 1px solid #bbdefb;
        }

        .empty-chips {
            color: #666;
            font-style: italic;
            padding: 10px;
        }

        .individual-employee-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-bottom: 1px solid #eee;
        }

        .individual-employee-item:last-child {
            border-bottom: none;
        }

        .region-badge {
            background: linear-gradient(135deg, #ff9800, #f57c00);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 15px;
        }

        .empty-text {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 8px;
        }

.empty-subtext {
    font-size: 0.9rem;
    opacity: 0.7;
}

/* Searchable dropdown styles */
.searchable-dropdown {
    position: relative;
}

.searchable-dropdown select {
    appearance: none;
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 8px center;
    background-size: 12px;
    padding-right: 30px;
}

.unit-filters-section {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    border-left: 4px solid #17a2b8;
}

.unit-filters-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 15px;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 15px;
}

.unit-selection-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.form-control.searchable {
    background-color: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px 12px;
    font-size: 14px;
    transition: all 0.3s ease;
    height: 100px;
    overflow-y: auto;
}

.form-control.searchable:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.unit-info-display {
    background: #e8f5e8;
    border: 1px solid #c8e6c9;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    font-size: 14px;
    color: #2e7d32;
}

.filter-info {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 10px;
    margin-top: 10px;
    font-size: 13px;
    color: #856404;
}

.clear-filters-btn {
    background: #6c757d;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.clear-filters-btn:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Searchable dropdown container */
.searchable-dropdown-container {
    position: relative;
    width: 100%;
}

.searchable-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background-color: #fff;
    cursor: pointer;
}

.searchable-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.dropdown-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #e0e0e0;
    border-top: none;
    border-radius: 0 0 8px 8px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.dropdown-options.show {
    display: block;
}

.dropdown-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    font-size: 14px;
}

.dropdown-option:hover {
    background-color: #f8f9fa;
}

.dropdown-option.selected {
    background-color: #3498db;
    color: white;
}

.dropdown-option:last-child {
    border-bottom: none;
}

/* Pagination Styles */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e0e0e0;
}

.pagination-info {
    font-size: 14px;
    color: #666;
    font-weight: 500;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

.pagination-btn {
    background: #3498db;
    color: white;
    border: none;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 35px;
}

.pagination-btn:hover {
    background: #2980b9;
    transform: translateY(-1px);
}

.pagination-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

.pagination-btn.active {
    background: #27ae60;
    font-weight: bold;
}

.pagination-ellipsis {
    color: #666;
    padding: 8px 4px;
    font-weight: bold;
}

.employee-count-info {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 10px 15px;
    margin: 10px 0;
    font-size: 14px;
    color: #1976d2;
    display: flex;
    align-items: center;
    gap: 8px;
}

.employee-count-info .count-badge {
    background: #1976d2;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}